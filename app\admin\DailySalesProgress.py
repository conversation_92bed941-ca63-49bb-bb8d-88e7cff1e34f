# -*- coding: utf-8 -*-
# 日销售进度
import pymysql
import pandas as pd
import requests
from app.CommonLibraries.getConfig import get_config
import json

response = requests.session()


def conn_mysql(host, port, username, password, database):
    """
    链接数据库
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=username, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_daily_sales_progress(month, sell_type, is_liquor,conn_statics):
    """
    获取日销售进度数据
    :param month:月份
    :param sell_type:销售类型
    :param is_liquor:是否白酒
    :param conn_statics
    :return:
    """
    # config_info = get_config(data_id="db.v3_all", group="vinehoo.accounts")
    # host = config_info['host']
    # port = config_info['port']
    # username = config_info['user']
    # password = config_info['password']
    # conn = conn_mysql(host=host, port=port, username=username, password=password, database="vh_data_statistics")
    if sell_type ==1:
            sql = f"""
                        SELECT
                                id,`week`,`date`,operation_duty,remark,sum(plan_sales) 'plan_sales',round(sum(actual_sales)) 'actual_sales',round(((sum(actual_sales) - sum(cost))/sum(actual_sales))*100,2)  'gross_margin',plan_quantity,sum(actual_quantity) 'actual_quantity',round((sum(actual_quantity)/plan_quantity)*100,2) 'daily_compliance_quantity',round((sum(actual_sales)/sum(actual_quantity))) 'unit_price',round((sum(actual_sales)/sum(plan_sales))*100,2) 'daily_compliance_sales',is_liquor,`month`
                        FROM 
                                `vh_daily_sales_progress` 
                        WHERE   
                                sell_type in(1,4)
                                AND 
                                is_liquor = {is_liquor} 
                                AND 
                                `month` = '{month}'
                        group by 
                        `week`,`date`,`operation_duty`,remark
                        order by
                        `date`,sell_type
                    """
            df = pd.read_sql(sql, conn_statics)
            df = df.drop('id', axis=1).drop('month', axis=1).drop('is_liquor', axis=1)
            df['monthly_compliance_sales']=round((df.actual_sales.cumsum(0)/df.plan_sales.cumsum(0))*100,2)
            df['monthly_compliance_quantity']=round((df.actual_quantity.cumsum(0)/df.plan_quantity.cumsum(0))*100,2)
            df['single_volume_difference']=df.actual_quantity.cumsum(0) - df.plan_quantity.cumsum(0)
            df['poor_sales']=df.actual_sales.cumsum(0) - df.plan_sales.cumsum(0)   
    elif sell_type ==5:
            sql = f"""
                        SELECT
                                id,`week`,`date`,operation_duty,remark,sum(plan_sales) 'plan_sales',round(sum(actual_sales)) 'actual_sales',round(((sum(actual_sales) - sum(cost))/sum(actual_sales))*100,2)  'gross_margin',plan_quantity,sum(actual_quantity) 'actual_quantity',round((sum(actual_quantity)/plan_quantity)*100,2) 'daily_compliance_quantity',round((sum(actual_sales)/sum(actual_quantity))) 'unit_price',round((sum(actual_sales)/sum(plan_sales))*100,2) 'daily_compliance_sales',is_liquor,`month`
                        FROM 
                                `vh_daily_sales_progress` 
                        WHERE
                                `month` = '{month}' and is_liquor not in(2,3)
                        group by 
                        `week`,`date`,`operation_duty`,remark
                        order by
                        `date`,sell_type
                    """
            df = pd.read_sql(sql, conn_statics)
            df = df.drop('id', axis=1).drop('month', axis=1).drop('is_liquor', axis=1)
            df['monthly_compliance_sales']=round((df.actual_sales.cumsum(0)/df.plan_sales.cumsum(0))*100,2)
            df['monthly_compliance_quantity']=round((df.actual_quantity.cumsum(0)/df.plan_quantity.cumsum(0))*100,2)
            df['single_volume_difference']=df.actual_quantity.cumsum(0) - df.plan_quantity.cumsum(0)
            df['poor_sales']=df.actual_sales.cumsum(0) - df.plan_sales.cumsum(0)        
    elif sell_type == 6:
            sql = f"""
                        SELECT
                                id,`week`,`date`,operation_duty,remark,sum(plan_sales) 'plan_sales',round(sum(actual_sales)) 'actual_sales',round(((sum(actual_sales) - sum(cost))/sum(actual_sales))*100,2)  'gross_margin',plan_quantity,sum(actual_quantity) 'actual_quantity',round((sum(actual_quantity)/plan_quantity)*100,2) 'daily_compliance_quantity',round((sum(actual_sales)/sum(actual_quantity))) 'unit_price',round((sum(actual_sales)/sum(plan_sales))*100,2) 'daily_compliance_sales',is_liquor,`month`
                        FROM 
                                `vh_daily_sales_progress` 
                        WHERE   
                                sell_type = 0
                                AND 
                                is_liquor = 2
                                AND 
                                `month` = '{month}'
                        group by 
                        `week`,`date`,`operation_duty`,remark
                        order by
                        `date`,sell_type
                    """
            df = pd.read_sql(sql, conn_statics)
            df = df.drop('id', axis=1).drop('month', axis=1).drop('is_liquor', axis=1)
            df['monthly_compliance_sales']=round((df.actual_sales.cumsum(0)/df.plan_sales.cumsum(0))*100,2)
            df['monthly_compliance_quantity']=round((df.actual_quantity.cumsum(0)/df.plan_quantity.cumsum(0))*100,2)
            df['single_volume_difference']=df.actual_quantity.cumsum(0) - df.plan_quantity.cumsum(0)
            df['poor_sales']=df.actual_sales.cumsum(0) - df.plan_sales.cumsum(0)
    elif sell_type == 7:
        sql = f"""
                    SELECT
                            id,`week`,`date`,operation_duty,remark,sum(plan_sales) 'plan_sales',round(sum(actual_sales)) 'actual_sales',round(((sum(actual_sales) - sum(cost))/sum(actual_sales))*100,2)  'gross_margin',plan_quantity,sum(actual_quantity) 'actual_quantity',round((sum(actual_quantity)/plan_quantity)*100,2) 'daily_compliance_quantity',round((sum(actual_sales)/sum(actual_quantity))) 'unit_price',round((sum(actual_sales)/sum(plan_sales))*100,2) 'daily_compliance_sales',is_liquor,`month`
                    FROM 
                            `vh_daily_sales_progress` 
                    WHERE   
                            sell_type = 0
                            AND 
                            is_liquor = 3
                            AND 
                            `month` = '{month}'
                    group by 
                    `week`,`date`,`operation_duty`,remark
                    order by
                    `date`,sell_type
                """
        df = pd.read_sql(sql, conn_statics)
        df = df.drop('id', axis=1).drop('month', axis=1).drop('is_liquor', axis=1)
        df['monthly_compliance_sales']=round((df.actual_sales.cumsum(0)/df.plan_sales.cumsum(0))*100,2)
        df['monthly_compliance_quantity']=round((df.actual_quantity.cumsum(0)/df.plan_quantity.cumsum(0))*100,2)
        df['single_volume_difference']=df.actual_quantity.cumsum(0) - df.plan_quantity.cumsum(0)
        df['poor_sales']=df.actual_sales.cumsum(0) - df.plan_sales.cumsum(0)
    else:
            sql = f"""
                   SELECT
                                id,`week`,`date`,operation_duty,remark,plan_sales,round(sum(actual_sales)) 'actual_sales',round(((sum(actual_sales) - sum(cost))/sum(actual_sales))*100,2)  'gross_margin',plan_quantity,sum(actual_quantity) 'actual_quantity',round((sum(actual_quantity)/plan_quantity)*100,2) 'daily_compliance_quantity',round((sum(actual_sales)/sum(actual_quantity))) 'unit_price',round((sum(actual_sales)/sum(plan_sales))*100,2) 'daily_compliance_sales',is_liquor,`month`
                    FROM 
                                `vh_daily_sales_progress` 
                    WHERE 
                                sell_type = {sell_type} and is_liquor not in(2,3)
                    AND 
                                `month` = '{month}'
                    group by 
                                `week`,`date`,`operation_duty`,remark
                    order by
                                `date`"""
            df = pd.read_sql(sql, conn_statics)
            df = df.drop('id', axis=1).drop('month', axis=1).drop('is_liquor', axis=1)
            df['monthly_compliance_sales']=round((df.actual_sales.cumsum(0)/df.plan_sales.cumsum(0))*100,2)
            df['monthly_compliance_quantity']=round((df.actual_quantity.cumsum(0)/df.plan_quantity.cumsum(0))*100,2)
            df['single_volume_difference']=df.actual_quantity.cumsum(0) - df.plan_quantity.cumsum(0)
            df['poor_sales']=df.actual_sales.cumsum(0) - df.plan_sales.cumsum(0)  
    res_json = json.loads(df.to_json(orient='records'))
    return res_json
