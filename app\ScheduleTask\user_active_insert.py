import urllib
from datetime import datetime, timezone, timedelta
import pymongo
import pymysql
import requests
import pandas as pd

response = requests.session()


def get_user_activity_statistics(date, client):
    """
    获取用户时段统计数据
    :param date:日期
    :return:
    """
    db = client['vinehoo_v3']
    active_items = db['gateway_logs']
    year = int(date.split('-')[0])
    month = int(date.split('-')[1])
    day = int(date.split('-')[2])

    pipeline = {
        'request_time': {
            '$gte': datetime(year, month, day, 0,
                             0, 0, tzinfo=timezone.utc),
            '$lte': datetime(year, month, day, 23,
                             59, 59, tzinfo=timezone.utc)
        }
    }
    datas = active_items.find(pipeline)
    res_times = []
    uids = []
    for data in datas:
        if 'request_time' not in data or 'uid' not in data:
            continue
        res_time = data['request_time']
        uid = data['uid']
        res_times.append(res_time)
        uids.append(uid)
    df1 = pd.DataFrame(res_times).rename(columns={0: 'req_time'})
    df2 = pd.DataFrame(uids).rename(columns={0: 'uid'})
    dff = pd.concat([df1, df2], axis=1)
    dff['hour'] = dff['req_time'].apply(lambda x: x.hour)
    dff_end = dff[['hour', 'uid']].groupby(['hour', 'uid']).size().reset_index()[['hour', 'uid']].groupby(
        'hour').size().reset_index().rename(columns={0: 'active_user'})
    day_hours = range(0, 24)
    df_date = pd.DataFrame(day_hours).rename(columns={0: 'hour'})
    dff_re = pd.merge(df_date, dff_end, how='left').fillna(0)
    dff_re['active_user'] = dff_re['active_user'].astype('int')
    client.close()
    return dff_re['active_user'].tolist()


def insert_mysql(day, data, conn, cursor):
    sql = f"select * from vh_active_user_count where day = '{day}'"
    cursor.execute(sql)
    exist_data = cursor.fetchone()
    if exist_data is not None:
        sql = f"delete from vh_active_user_count where day = '{day}'"
        cursor.execute(sql)
        conn.commit()
        sql = f"INSERT INTO `vh_data_statistics`.`vh_active_user_count` (`day`, `active_frame`) VALUES ('{day}', '{data}')"
    else:
        sql = f"INSERT INTO `vh_data_statistics`.`vh_active_user_count` (`day`, `active_frame`) VALUES ('{day}', '{data}')"
    cursor.execute(sql)
    conn.commit()


def handler_start(now_day, client, conn, cursor):
    if not now_day:
        now_day = datetime.now().strftime('%Y-%m-%d')
    
    data_list = get_user_activity_statistics(now_day, client)
    insert_mysql(now_day, data_list, conn, cursor)
