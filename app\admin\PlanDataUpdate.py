import pandas as pd
from config import ConfigCenter
import math
import warnings

warnings.filterwarnings("ignore")

def number_to_date(para):
    """
    日期转换
    :param para:
    :return:
    """
    delta = pd.Timedelta(str(para) + 'days')
    time = pd.to_datetime('1899-12-30') + delta
    return time



def add_plan_data(file_name,conn_statics, conn_statics_cursor, is_update):
    """
    插入数据
    :param file_name:
    :param conn_statics:
    :param conn_statics_cursor:
    :param is_update:更新1，插入0
    :return:
    """
    oss_url = ConfigCenter.config_oss_url
    try:
        df = pd.read_excel(f"{oss_url['config_oss_url']}/{file_name}",
                           usecols=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9])
    except Exception as e:
        raise Exception(f"{e},{file_name}文件读取失败,请检查文件格式是否正确且支持")
    df1 = df[3:]
    df1.columns = ['week', 'date', 'operation_duty', 'operation_duty_assist', 'remarks', 'flash_redwine_sales_plan','flash_baijiu_sales_plan',
                   'cross_border_plan', 'second_delivery_plan','main_single_plan']
    df2 = df1.drop(df1.tail(1).index).reset_index(drop=True)
    df2['date'] = df2['date'].apply(lambda x: number_to_date(x))
    df2 = df2.fillna('')
    df2['flash_redwine_sales_plan'] = df2['flash_redwine_sales_plan'].apply(lambda x: math.ceil(x))
    df2['main_single_plan'] = df2['main_single_plan'].apply(lambda x: math.floor(x))
    df2['push'] = ''
    df2['new_user'] = 0
    df2['new_user_monetary'] = 0
    df2['purchased'] = 0
    df2['login'] = 0
    df2['tail_sales_plan'] = 0
    df2['sales_plan'] = 0
    df2['single_plan'] = 0
    datas = df2.values.tolist()
    for row in datas:
        week = row[0]
        date = str(row[1]).split(' ')[0]
        operation_duty = row[2]
        operation_duty_assist = row[3]
        remarks = row[4]
        flash_redwine_sales_plan = row[5]
        flash_baijiu_sales_plan = int(row[6])
        cross_border_plan = row[7]
        second_delivery_plan = row[8]
        main_single_plan = row[9]
        push = row[10]
        new_user = row[11]
        new_user_monetary = row[12]
        purchased = row[13]
        login = row[14]
        tail_sales_plan=row[15]
        sales_plan = row[16]
        single_plan = row[17]
        if is_update == 0:
            exists_sql = f"select * from vh_sales_analysis_plan where date = '{date}'"
            conn_statics_cursor.execute(exists_sql)
            exists_data = conn_statics_cursor.fetchone()
            if exists_data is None:
                sql = f"""insert into vh_sales_analysis_plan(`week`, `date`, `operation_duty`, `operation_duty_assist`, 
                          `remarks`, `sales_plan`, `single_plan`, `cross_border_plan`, `second_delivery_plan`, `push`, 
                          `new_user`, `new_user_monetary`, `purchased`, `login`, `tail_sales_plan` , `main_single_plan` ,`flash_redwine_sales_plan`,`flash_baijiu_sales_plan`) 
                          values ('{week}', '{date}', '{operation_duty}', '{operation_duty_assist}', '{remarks}', {sales_plan},
                          {single_plan}, {cross_border_plan}, {second_delivery_plan}, '{push}', {new_user}, {new_user_monetary},
                          {purchased}, {login}, {tail_sales_plan}, {main_single_plan}, {flash_redwine_sales_plan}, {flash_baijiu_sales_plan})"""
            else:
                sql = f"""update vh_sales_analysis_plan 
                                              set week = '{week}',date = '{date}',
                                              operation_duty='{operation_duty}',
                                              operation_duty_assist = '{operation_duty_assist}',
                                              remarks = '{remarks}',
                                              cross_border_plan={cross_border_plan},
                                              second_delivery_plan={second_delivery_plan},
                                              flash_redwine_sales_plan={flash_redwine_sales_plan},
                                              flash_baijiu_sales_plan={flash_baijiu_sales_plan}
                                              where date = '{date}'
                                              """
        else:
            sql = f"""update vh_sales_analysis_plan 
                              set week = '{week}',date = '{date}',
                              operation_duty='{operation_duty}',
                              operation_duty_assist = '{operation_duty_assist}',
                              remarks = '{remarks}',
                              cross_border_plan={cross_border_plan},
                              second_delivery_plan={second_delivery_plan},
                              flash_redwine_sales_plan={flash_redwine_sales_plan},
                              flash_baijiu_sales_plan={flash_baijiu_sales_plan}
                              where date = '{date}'
                              """
        try:
            conn_statics_cursor.execute(sql)
            conn_statics.commit()
        except Exception as e:
            conn_statics.rollback()
            raise Exception('数据已存在')
    return True
