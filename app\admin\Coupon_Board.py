import json
import warnings
import concurrent
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine
from config import ChannelTypeConfig
from app.DButils.MysqlHelper import conn_str

warnings.filterwarnings("ignore")

def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine



def get_coupon_massage(coupon_type,coupon_ids):
    if coupon_type == []:
        coupon_sql = f""" select id as 'coupon_id',case classify_id when 1 then '直减券' when 2 then '满减券' when 3 then '直折券' when 4 then '满折券' end as 'coupon_type',coupon_face_value,relation_id from vh_coupon where coupon_scope = 1000 and id in {coupon_ids}"""
    else:
        if len(coupon_type) == 1:
            coupon_sql = f""" select id as 'coupon_id',case classify_id when 1 then '直减券' when 2 then '满减券' when 3 then '直折券' when 4 then '满折券' end as 'coupon_type',coupon_face_value,relation_id from vh_coupon where coupon_scope = 1000 and id in {coupon_ids} having coupon_type = '{coupon_type[0]}'"""
        else:
            coupon_sql = f""" select id as 'coupon_id',case classify_id when 1 then '直减券' when 2 then '满减券' when 3 then '直折券' when 4 then '满折券' end as 'coupon_type',coupon_face_value,relation_id from vh_coupon where coupon_scope = 1000 and id in {coupon_ids} having coupon_type in {tuple(coupon_type)}"""
    coupon_conn = nopool_conn("vh_marketing").connect()
    coupon_dff = pd.read_sql_query(coupon_sql,coupon_conn)
    coupon_conn.close()
    nopool_conn("vh_marketing").dispose()
    return coupon_dff


def get_commodities_massage(periods,commoditie,sell_type):
    if sell_type == 0:
        periods_sql = f"""select id as 'relation_id',title,price from {commoditie} where id in {periods} and is_channel = 0"""
    elif sell_type == 1:
        periods_sql = f"""select id as 'relation_id',title,price from {commoditie} where id in {periods} and is_channel = 0 and onsale_status = 2"""
    elif sell_type == 2:
        periods_sql = f"""select id as 'relation_id',title,price from {commoditie} where id in {periods} and is_channel = 0 and onsale_status != 2"""
    periods_conn = nopool_conn("vh_commodities").connect()
    periods_dff = pd.read_sql_query(periods_sql,periods_conn)
    periods_conn.close()
    nopool_conn("vh_commodities").dispose()
    return periods_dff



def get_coupon_issue_massage(start_timestamp,end_timestamp):
    coupon_issue_sql = f""" select coupon_id,sub_order_no,id as issue_id from vh_coupon_issue where review_status in (2,3,4) and audit_time between {start_timestamp} and {end_timestamp} """
    coupon_issue_conn = nopool_conn("vh_marketing").connect()
    coupon_issue_dff = pd.read_sql_query(coupon_issue_sql,coupon_issue_conn)
    coupon_issue_conn.close()
    nopool_conn("vh_marketing").dispose()
    return coupon_issue_dff



def get_coupon_orders_massage(start_timestamp,end_timestamp,issue_ids,order_table):
    orders_massage_sql = f""" select coupon_id as 'issue_id',payment_amount + preferential_reduction as 'payment_amount' from {order_table} where created_time between {start_timestamp} and {end_timestamp} and coupon_id in {issue_ids} and sub_order_status in (1,2,3) and payment_amount - refund_money != 0"""
    orders_massage_conn = nopool_conn("vh_orders").connect()
    orders_massage_dff = pd.read_sql_query(orders_massage_sql,orders_massage_conn)
    orders_massage_conn.close()
    nopool_conn("vh_orders").dispose()
    return orders_massage_dff



def get_coupon_main(start_date,end_date,coupon_type,sell_type,sort_rule,out_type,page,page_nums):
    start_timestamp,end_timestamp = int(datetime.strptime(f"{start_date} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"{end_date} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
    Cg = ChannelTypeConfig.channel_type_config
    order_tables,commodities = [Cg[1][0],Cg[3][0],Cg[4][0]],[Cg[1][1],Cg[3][1],Cg[4][1]]
    coupon_issue_massage = get_coupon_issue_massage(start_timestamp,end_timestamp)
    coupon_ids = tuple(set(list(filter(lambda x:x is not None,coupon_issue_massage.coupon_id.tolist()))))
    coupon_massage = get_coupon_massage(coupon_type,coupon_ids)
    if coupon_massage.empty:
        return {"list":[],"total":0}
    periods_list = list(filter(lambda x:x is not None,coupon_massage.relation_id.tolist()))
    periods_list = list(filter(lambda x:x != '',periods_list))
    periods = tuple(set(periods_list))
    if coupon_massage.empty:
        coupon_massage["relation_id"] = None
        coupon_massage["title"] = None
        coupon_massage["price"] = None
        coupon_massage["order_counts"] = 0
        coupon_massage["payment_amount"] = 0
        coupon_massage["usage_rate"] = 0.00
    issue_ids = tuple(set(coupon_issue_massage.issue_id.tolist()))
    with concurrent.futures.ThreadPoolExecutor(max_workers=len(commodities) * 2) as executor:
        commodities_tasks = [executor.submit(get_commodities_massage,periods,commoditie,sell_type) for commoditie in commodities]
        commodities_dff = pd.DataFrame(columns=["relation_id","title","price"])
        for commodities_future in concurrent.futures.as_completed(commodities_tasks):
            commodities_dff=commodities_dff.append(commodities_future.result()).reset_index(drop=True)
    with concurrent.futures.ThreadPoolExecutor(max_workers=len(order_tables) * 2) as executor:
        orders_tasks = [executor.submit(get_coupon_orders_massage,start_timestamp,end_timestamp,issue_ids,order_table) for order_table in order_tables]
        orders_dff = pd.DataFrame(columns=["issue_id","payment_amount"])
        for orders_future in concurrent.futures.as_completed(orders_tasks):
            orders_dff=orders_dff.append(orders_future.result()).reset_index(drop=True)
    commodities_dff["relation_id"] = commodities_dff.relation_id.astype(str)
    if sell_type == 0:
        merge_coupon_commodities = pd.merge(coupon_massage,commodities_dff,how="left",on="relation_id")
    else:
        merge_coupon_commodities = pd.merge(coupon_massage,commodities_dff,how="inner",on="relation_id")
    coupon_issue_massage["issue_id"] = coupon_issue_massage.issue_id.astype(str)
    orders_dff = pd.merge(orders_dff,coupon_issue_massage[["coupon_id","issue_id","sub_order_no"]],how="right",on="issue_id")
    coupon_issue_massage = coupon_issue_massage.groupby(by="coupon_id",as_index=False).agg({"issue_id":"count","sub_order_no":"count"})
    orders_dff = orders_dff.groupby(by="coupon_id",as_index=False).agg({"payment_amount":sum})
    merge_issue_orders = pd.merge(coupon_issue_massage,orders_dff,how="inner",on="coupon_id")
    merge_issue_orders.columns = ["coupon_id","coupon_grant_count","order_counts","payment_amount"]
    merge_issue_orders["order_counts"] = merge_issue_orders.order_counts.fillna(0)
    merge_issue_orders["payment_amount"] = merge_issue_orders.payment_amount.fillna(0)
    main_dff = pd.merge(merge_coupon_commodities,merge_issue_orders,how="inner",on="coupon_id")
    main_dff = main_dff.rename(columns={"coupon_face_value":"coupon_price","relation_id":"period"})
    main_dff["usage_rate"] = main_dff.order_counts / main_dff.coupon_grant_count * 100
    main_dff["usage_rate"] = main_dff.usage_rate.apply(lambda x:round(x,2))
    main_dff["coupon_grant_count"] = main_dff.coupon_grant_count.fillna(0)
    main_dff["order_counts"] = main_dff.order_counts.fillna(0)
    main_dff["payment_amount"] = main_dff.payment_amount.fillna(0)
    main_dff["usage_rate"] = main_dff.usage_rate.fillna(0.00)
    main_dff["payment_amount"] = main_dff.payment_amount.apply(lambda x:round(x))
    sort_field = list(sort_rule.keys())[0]
    rule = sort_rule[list(sort_rule.keys())[0]]
    if rule == 1:
        main_dff = main_dff.sort_values(by=sort_field,ascending=True)
    elif rule == -1:
        main_dff = main_dff.sort_values(by=sort_field,ascending=False)
    if out_type == 1:
        total = len(main_dff)
        over_dff = main_dff[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
        vis_json = {"list": json.loads(over_dff.to_json(orient="records")),"total": total}
        return vis_json
    elif out_type == 2:
        over_dff = main_dff.head(10)
        over_dff = over_dff[["coupon_id",sort_field]]
        vis_json = {"data": json.loads(over_dff.to_json(orient="records"))}
        return vis_json
    else:
        return {"list":[],"total":0}
    