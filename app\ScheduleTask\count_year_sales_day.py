import pymysql
import pandas as pd
from datetime import datetime
from dateutil.relativedelta import relativedelta
import json
import numpy as np
import warnings
import requests

warnings.filterwarnings("ignore")


# 年度销售统计


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_last_month():
    # 获取前一个月
    try:
        month_date = datetime.now().date() - relativedelta(months=1)
        return month_date.strftime("%Y-%m")
    except Exception as e:
        print(f"获取前一月失败,失败原因:{e}")


def get_cur_month():
    # 获取当前月
    return datetime.now().strftime("%Y-%m")


def get_cur_date():
    # 获取当前日
    return datetime.now().strftime("%d")


def deal_list_wine_info(wine_info):
    data_list = []
    for i in json.loads(wine_info):
        data_list.append(i['product_id'])
    return data_list


def deal_list_nums(nums):
    data_list = []
    for i in json.loads(nums):
        data_list.append(i['nums'])
    return data_list


def format_order_no_type_json_list(sub_order_nos_list):
    out_list = []
    for i in sub_order_nos_list:
        i_dict = {"sub_order_no":i,"order_type":1}
        out_list.append(i_dict)
    return out_list


def split_list_into_groups(lst, group_size):
    """
    列表切割
    :param lst:主列表名
    :param group_size:每份个数
    :return:
    """
    return [lst[i:i+group_size] for i in range(0, len(lst), group_size)]

    
    
def get_paymain(out_list):
    url = "https://callback.vinehoo.com/orders/orders/v3/salesreturn/getcorpbyorderno"
    payload = out_list
    headers = {
            'content-type': "application/json",
            'cache-control': "no-cache"
        }
    response = requests.request("POST", url, data=json.dumps(payload), headers=headers)
    json_dict = response.json()
    data = json_dict["data"]
    sub_order_no = []
    corp = []
    for d in data:
        sub_order_no.append(d["sub_order_no"])
        corp.append(d["corp"])
    return pd.DataFrame({"sub_order_no":sub_order_no,"corp":corp})


def get_plans(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取计划数据
    :param month:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_data_statistics")
    sql = f"""SELECT operation_duty,flash_redwine_sales_plan + flash_baijiu_sales_plan 'sales_plan',main_single_plan 'single_plan',`date`,week,remarks,cross_border_plan,second_delivery_plan FROM vh_sales_analysis_plan WHERE date LIKE '{month}%'"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df


def get_order_info(month, log_month ,table, rr_host, rr_port, rr_user, rr_password):
    """
    获取订单数据
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    sql = f"""SELECT '{log_month}' as 'month',o.sub_order_no,o.payment_amount 'actual_sales',o.package_id,ob.product_info FROM `{table}` as o left join vh_order_main as om on om.id=o.main_order_id left join vh_order_mystery_box_log as ob on ob.main_order_no=om.main_order_no WHERE o.sub_order_status IN (1,2,3) AND FROM_UNIXTIME(o.created_time,'%Y-%m') = '{month}'"""
    df = pd.read_sql(sql, conn)
    order_no_type_json_list = format_order_no_type_json_list(df.sub_order_no.tolist())
    format_order_no_type_json_lis = split_list_into_groups(order_no_type_json_list, 5000)
    payment_dff = pd.DataFrame(columns=["sub_order_no","corp"])
    for lis in format_order_no_type_json_lis:
        paymaindf = get_paymain(lis)
        payment_dff = payment_dff.append(paymaindf).reset_index(drop=True)
    dff = pd.merge(payment_dff,df,how="right",on="sub_order_no").fillna('')
    dff = dff.groupby(by="corp",as_index=False).agg({"month":"first","actual_sales":sum,"sub_order_no":"count"}).rename(columns={"sub_order_no":"actual_quantity"})
    conn.close()
    return dff


def get_cost(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取成本信息
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    conn_periods = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_commodities")
    # 获取所有订单信息
    sql = f"""SELECT package_id,order_qty,FROM_UNIXTIME(created_time,'%Y-%m-%d') 'date' FROM vh_flash_order WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
    df_order = pd.read_sql(sql, conn)
    df_all_cost = pd.DataFrame(columns=['package_id', 'costprice', 'channel_type'])
    tables = {"vh_flash_order": "vh_periods_flash_set",
              "vh_cross_order": "vh_periods_cross_set",
              "vh_tail_order": "vh_periods_leftover_set",
              "vh_second_order": "vh_periods_second_set"}
    tables_type = {"vh_flash_order": 1,
                   "vh_cross_order": 2,
                   "vh_second_order": 3,
                   "vh_tail_order": 4}
    for table in tables.keys():
        sql = f"""SELECT DISTINCT package_id FROM `{table}` WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        df_package_id = pd.read_sql(sql, conn)
        package_ids = tuple(df_package_id['package_id'].tolist())
        if len(package_ids) == 1:
            sql = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id = {package_ids[0]}"""
        elif len(package_ids) == 0:
            continue
        else:
            sql = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id IN {package_ids}"""
        df_set = pd.read_sql(sql, conn_periods)
        # 获取期数绑定产品id
        df_set['product_id'] = df_set['associated_products'].apply(lambda x: deal_list_wine_info(x))
        df_product_id = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.product_id.str.len()),
                                      'product_id': np.concatenate(df_set.product_id.values)}).reset_index(drop=True)
        # 获取期数绑定产品数量
        df_set['nums'] = df_set['associated_products'].apply(lambda x: deal_list_nums(x))
        df_nums = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.nums.str.len()),
                                'nums': np.concatenate(df_set.nums.values)}).reset_index(drop=True)
        df_nums = df_nums[['nums']]
        dff_package_info = pd.concat([df_product_id, df_nums], axis=1)
        # 获取产品成本
        sql = """SELECT product_id,costprice FROM vh_periods_product_inventory"""
        df_cost = pd.read_sql(sql, conn_periods)
        dff_cost = pd.merge(dff_package_info, df_cost, how='left')
        dff_cost = dff_cost[['package_id', 'costprice']].groupby('package_id').sum().reset_index()
        dff_cost['channel_type'] = tables_type[table]
        df_all_cost = df_all_cost.append(dff_cost).reset_index(drop=True)
    dff_end = pd.merge(df_order, df_all_cost, how='left')
    dff_end['cost'] = dff_end['costprice'] * dff_end['order_qty']
    dff_required = dff_end[['date', 'cost', 'channel_type']].groupby(["date", 'channel_type']).sum().reset_index()
    return dff_required

def get_order_cost(order_info, table, rr_host, rr_port, rr_user, rr_password):
    """
    获取成本信息
    :return:
    """
    product_info = {}
    for index, row in order_info.iterrows():
        if row['product_info'] is not None:
            if row['sub_order_no'] not in product_info:
                product_info[row['sub_order_no']] = ''
                product_info[row['sub_order_no']] = row['product_info']
    print(json.dumps(product_info))
    # print(product_info.to_dict(orient='records'))
    exit()

    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    conn_periods = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_commodities")
    # 获取所有订单信息
    sql = f"""SELECT package_id,order_qty,FROM_UNIXTIME(created_time,'%Y-%m-%d') 'date' FROM vh_flash_order WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
    df_order = pd.read_sql(sql, conn)
    df_all_cost = pd.DataFrame(columns=['package_id', 'costprice', 'channel_type'])
    tables = {"vh_flash_order": "vh_periods_flash_set",
              "vh_cross_order": "vh_periods_cross_set",
              "vh_tail_order": "vh_periods_leftover_set",
              "vh_second_order": "vh_periods_second_set"}
    tables_type = {"vh_flash_order": 1,
                   "vh_cross_order": 2,
                   "vh_second_order": 3,
                   "vh_tail_order": 4}
    for table in tables.keys():
        sql = f"""SELECT DISTINCT package_id FROM `{table}` WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        df_package_id = pd.read_sql(sql, conn)
        package_ids = tuple(df_package_id['package_id'].tolist())
        if len(package_ids) == 1:
            sql = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id = {package_ids[0]}"""
        elif len(package_ids) == 0:
            continue
        else:
            sql = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id IN {package_ids}"""
        df_set = pd.read_sql(sql, conn_periods)
        # 获取期数绑定产品id
        df_set['product_id'] = df_set['associated_products'].apply(lambda x: deal_list_wine_info(x))
        df_product_id = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.product_id.str.len()),
                                      'product_id': np.concatenate(df_set.product_id.values)}).reset_index(drop=True)
        # 获取期数绑定产品数量
        df_set['nums'] = df_set['associated_products'].apply(lambda x: deal_list_nums(x))
        df_nums = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.nums.str.len()),
                                'nums': np.concatenate(df_set.nums.values)}).reset_index(drop=True)
        df_nums = df_nums[['nums']]
        dff_package_info = pd.concat([df_product_id, df_nums], axis=1)
        # 获取产品成本
        sql = """SELECT product_id,costprice FROM vh_periods_product_inventory"""
        df_cost = pd.read_sql(sql, conn_periods)
        dff_cost = pd.merge(dff_package_info, df_cost, how='left')
        dff_cost = dff_cost[['package_id', 'costprice']].groupby('package_id').sum().reset_index()
        dff_cost['channel_type'] = tables_type[table]
        df_all_cost = df_all_cost.append(dff_cost).reset_index(drop=True)
    dff_end = pd.merge(df_order, df_all_cost, how='left')
    dff_end['cost'] = dff_end['costprice'] * dff_end['order_qty']
    dff_required = dff_end[['date', 'cost', 'channel_type']].groupby(["date", 'channel_type']).sum().reset_index()
    return dff_required


def deal_with_data(rr_host, rr_port, rr_user, rr_password):
    day = int(get_cur_date())
    if day == 1:
        month = get_last_month()
    else:
        month = get_cur_month()
    # month = '2022-05'
    month_last_year = f"{int(month.split('-')[0]) - 1}-{month.split('-')[1]}"
    df_plan = get_plans(month, rr_host, rr_port, rr_user, rr_password)
    # 获取闪购月计划销售额
    plan_sales = round(df_plan['sales_plan'].sum(), 2)
    # 获取跨境月计划销售额
    cross_border_plan = round(df_plan['cross_border_plan'].sum(), 2)
    # 获取秒发月计划销售额
    second_delivery_plan = round(df_plan['second_delivery_plan'].sum(), 2)
    # 获取计划销售单量
    plan_quantity = int(df_plan['single_plan'].sum())
    plan_dff = pd.DataFrame([])
    plan_dff["month"] = [f"""{df_plan.date[0].split("-")[0]}-{df_plan.date[0].split("-")[1]}"""]
    plan_dff["plan_sales"] = plan_sales
    plan_dff["cross_border_plan"] = cross_border_plan
    plan_dff["second_delivery_plan"] = second_delivery_plan
    plan_dff["plan_quantity"] = plan_quantity
    log_month = month
    # 获取尾货月实际销售数据
    df_tail = get_order_info(month, log_month, 'vh_tail_order', rr_host, rr_port, rr_user, rr_password)
    # 获取尾货去年当月实际销售数据
    df_tail_last = get_order_info(month_last_year, log_month, 'vh_tail_order', rr_host, rr_port, rr_user, rr_password)

    # 获取闪购月实际销售数据
    df_flash = get_order_info(month, log_month, 'vh_flash_order', rr_host, rr_port, rr_user, rr_password)
    for k, v in df_flash.iterrows():
        for kk, vv in df_tail.iterrows():
            if v['corp'] == vv['corp'] and v['month'] == vv['month']:
                df_flash.at[k, 'actual_sales'] = round(v['actual_sales'] + vv['actual_sales'], 2)
                df_flash.at[k, 'actual_quantity'] = round(v['actual_quantity'] + vv['actual_quantity'], 2)

    df_flash_last = get_order_info(month_last_year, log_month, 'vh_flash_order', rr_host, rr_port, rr_user, rr_password)
    for k, v in df_flash_last.iterrows():
        for kk, vv in df_tail_last.iterrows():
            if v['corp'] == vv['corp'] and v['month'] == vv['month']:
                df_flash_last.at[k, 'actual_sales'] = round(v['actual_sales'] + vv['actual_sales'], 2)
                df_flash_last.at[k, 'actual_quantity'] = round(v['actual_quantity'] + vv['actual_quantity'], 2)
    
    # 计划增幅
    df_flash = pd.merge(df_flash,plan_dff,how="left").merge(df_flash_last,how="left",on=["corp","month"])
    df_flash.columns = ["corp","month","cur_sales","cur_quantity","plan_sales","cross_plan_sales","second_plan_sales","plan_quantity","last_sales","last_quantity"]
    df_flash = df_flash.fillna(0)
    df_flash["planned_increases_sales"] = (df_flash.plan_sales - df_flash.last_sales) / df_flash.last_sales * 100
    df_flash["planned_increases_sales"] = df_flash.planned_increases_sales.apply(lambda x:round(x,2))
    df_flash["planned_increases_quantity"] = (df_flash.plan_quantity - df_flash.last_quantity) / df_flash.cur_quantity * 100
    df_flash["planned_increases_quantity"] = df_flash.planned_increases_quantity.apply(lambda x:round(x,2))
    # 达标率
    df_flash["compliance_sales"] = df_flash.cur_sales / df_flash.plan_sales * 100
    df_flash["compliance_sales"] = df_flash.compliance_sales.apply(lambda x:round(x,2))
    df_flash["compliance_quantity"] = df_flash.cur_quantity / df_flash.plan_quantity * 100
    df_flash["compliance_quantity"] = df_flash.compliance_quantity.apply(lambda x:round(x,2))
    # 实际增幅
    df_flash["actual_increase_sales"] = (df_flash.cur_sales - df_flash.last_sales) / df_flash.last_sales * 100
    df_flash["actual_increase_sales"] = df_flash.actual_increase_sales.apply(lambda x:round(x,2))
    df_flash["actual_increase_quantity"] = (df_flash.cur_quantity - df_flash.last_quantity) / df_flash.last_quantity * 100
    df_flash["actual_increase_quantity"] = df_flash.actual_increase_quantity.apply(lambda x:round(x,2))
    # 客单价
    df_flash["unit_price"] = df_flash.cur_sales / df_flash.cur_quantity
    df_flash["unit_price"] = df_flash.unit_price.apply(lambda x:round(x,2))
    # 客单价增幅
    df_flash["unit_price_last"] = df_flash.last_sales / df_flash.last_quantity
    df_flash["unit_price_last"] = df_flash.unit_price_last.apply(lambda x:round(x,2))
    df_flash["unit_price_increase"] = (df_flash.unit_price - df_flash.unit_price_last) / df_flash.unit_price_last * 100
    df_flash["unit_price_increase"] = df_flash.unit_price_increase.apply(lambda x:round(x,2))
    df_flash["type"] = 1
    df_flash.replace([np.inf, -np.inf], 100, inplace=True)
    df_flash = df_flash.fillna(0)
    df_flash["im_type"] = 0
    df_flash["profit"] = 0
    df_flash["data_year"] = month.split('-')[0]

    df_flash = df_flash[["corp","month","type","im_type","plan_sales","planned_increases_sales","cur_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","cur_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]]
    df_flash.columns = ["corp","date","type","im_type","plan_sales","planned_increases_sales","actual_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","actual_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]
    # 获取跨境月实际销售数据
    df_cross = get_order_info(month, log_month, 'vh_cross_order', rr_host, rr_port, rr_user, rr_password)
    # 获取跨境去年当月实际销售数据
    df_cross_last = get_order_info(month_last_year, log_month, 'vh_cross_order', rr_host, rr_port, rr_user, rr_password)
    # 计划增幅
    df_cross = pd.merge(df_cross,plan_dff,how="left").merge(df_cross_last,how="left",on=["corp","month"])
    df_cross.columns = ["corp","month","cur_sales","cur_quantity","plan_sales","cross_plan_sales","second_plan_sales","plan_quantity","last_sales","last_quantity"]
    df_cross = df_cross.fillna(0)
    df_cross["planned_increases_sales"] = (df_cross.cross_plan_sales - df_cross.last_sales) / df_cross.last_sales * 100
    df_cross["planned_increases_sales"] = df_cross.planned_increases_sales.apply(lambda x:round(x,2))
    df_cross["planned_increases_quantity"] = (df_cross.plan_quantity - df_cross.last_quantity) / df_cross.cur_quantity * 100
    df_cross["planned_increases_quantity"] = df_cross.planned_increases_quantity.apply(lambda x:round(x,2))
    # 达标率
    df_cross["compliance_sales"] = df_cross.cur_sales / df_cross.cross_plan_sales * 100
    df_cross["compliance_sales"] = df_cross.compliance_sales.apply(lambda x:round(x,2))
    df_cross["compliance_quantity"] = df_cross.cur_quantity / df_cross.plan_quantity * 100
    df_cross["compliance_quantity"] = df_cross.compliance_quantity.apply(lambda x:round(x,2))
    # 实际增幅
    df_cross["actual_increase_sales"] = (df_cross.cur_sales - df_cross.last_sales) / df_cross.last_sales * 100
    df_cross["actual_increase_sales"] = df_cross.actual_increase_sales.apply(lambda x:round(x,2))
    df_cross["actual_increase_quantity"] = (df_cross.cur_quantity - df_cross.last_quantity) / df_cross.last_quantity * 100
    df_cross["actual_increase_quantity"] = df_cross.actual_increase_quantity.apply(lambda x:round(x,2))
    # 客单价
    df_cross["unit_price"] = df_cross.cur_sales / df_cross.cur_quantity
    df_cross["unit_price"] = df_cross.unit_price.apply(lambda x:round(x,2))
    # 客单价增幅
    df_cross["unit_price_last"] = df_cross.last_sales / df_cross.last_quantity
    df_cross["unit_price_last"] = df_cross.unit_price_last.apply(lambda x:round(x,2))
    df_cross["unit_price_increase"] = (df_cross.unit_price - df_cross.unit_price_last) / df_cross.unit_price_last * 100
    df_cross["unit_price_increase"] = df_cross.unit_price_increase.apply(lambda x:round(x,2))
    df_cross["type"] = 2
    df_cross.replace([np.inf, -np.inf], 100, inplace=True)
    df_cross = df_cross.fillna(0)
    df_cross["im_type"] = 0
    df_cross["profit"] = 0
    df_cross["data_year"] = month.split('-')[0]
    df_cross["plan_quantity"] = 0
    df_cross = df_cross[["corp","month","type","im_type","cross_plan_sales","planned_increases_sales","cur_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","cur_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]]
    df_cross.columns = ["corp","date","type","im_type","plan_sales","planned_increases_sales","actual_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","actual_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]
    # 获取秒发月实际销售数据
    df_second = get_order_info(month, log_month, 'vh_second_order', rr_host, rr_port, rr_user, rr_password)
    # 获取秒发去年当月实际销售数据
    df_second_last = get_order_info(month_last_year, log_month, 'vh_second_order', rr_host, rr_port, rr_user, rr_password)
    # 计划增幅
    df_second = pd.merge(df_second,plan_dff,how="left").merge(df_cross_last,how="left",on=["corp","month"])
    df_second.columns = ["corp","month","cur_sales","cur_quantity","plan_sales","cross_plan_sales","second_plan_sales","plan_quantity","last_sales","last_quantity"]
    df_second = df_second.fillna(0)
    df_second["planned_increases_sales"] = (df_second.second_plan_sales - df_second.last_sales) / df_second.last_sales * 100
    df_second["planned_increases_sales"] = df_second.planned_increases_sales.apply(lambda x:round(x,2))
    df_second["planned_increases_quantity"] = (df_second.plan_quantity - df_second.last_quantity) / df_second.cur_quantity * 100
    df_second["planned_increases_quantity"] = df_second.planned_increases_quantity.apply(lambda x:round(x,2))
    # 达标率
    df_second["compliance_sales"] = df_second.cur_sales / df_second.second_plan_sales * 100
    df_second["compliance_sales"] = df_second.compliance_sales.apply(lambda x:round(x,2))
    df_second["compliance_quantity"] = df_second.cur_quantity / df_second.plan_quantity * 100
    df_second["compliance_quantity"] = df_second.compliance_quantity.apply(lambda x:round(x,2))
    # 实际增幅
    df_second["actual_increase_sales"] = (df_second.cur_sales - df_second.last_sales) / df_second.last_sales * 100
    df_second["actual_increase_sales"] = df_second.actual_increase_sales.apply(lambda x:round(x,2))
    df_second["actual_increase_quantity"] = (df_second.cur_quantity - df_second.last_quantity) / df_second.last_quantity * 100
    df_second["actual_increase_quantity"] = df_second.actual_increase_quantity.apply(lambda x:round(x,2))
    # 客单价
    df_second["unit_price"] = df_second.cur_sales / df_second.cur_quantity
    df_second["unit_price"] = df_second.unit_price.apply(lambda x:round(x,2))
    # 客单价增幅
    df_second["unit_price_last"] = df_second.last_sales / df_second.last_quantity
    df_second["unit_price_last"] = df_second.unit_price_last.apply(lambda x:round(x,2))
    df_second["unit_price_increase"] = (df_second.unit_price - df_second.unit_price_last) / df_second.unit_price_last * 100
    df_second["unit_price_increase"] = df_second.unit_price_increase.apply(lambda x:round(x,2))
    df_second["type"] = 3
    df_second.replace([np.inf, -np.inf], 100, inplace=True)
    df_second = df_second.fillna(0)
    df_second["im_type"] = 0
    df_second["profit"] = 0
    df_second["data_year"] = month.split('-')[0]
    df_second = df_second[["corp","month","type","im_type","second_plan_sales","planned_increases_sales","cur_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","cur_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]]
    df_second.columns = ["corp","date","type","im_type","plan_sales","planned_increases_sales","actual_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","actual_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]

    # 计划增幅
    df_tail = pd.merge(df_tail,plan_dff,how="left").merge(df_cross_last,how="left",on=["corp","month"])
    df_tail.columns = ["corp","month","cur_sales","cur_quantity","plan_sales","cross_plan_sales","second_plan_sales","plan_quantity","last_sales","last_quantity"]
    df_tail = df_tail.fillna(0)
    df_tail["planned_increases_sales"] = 0
    # 达标率
    df_tail["compliance_sales"] = 0
    # 实际增幅
    df_tail["actual_increase_sales"] = (df_tail.cur_sales - df_tail.last_sales) / df_tail.last_sales * 100
    df_tail["actual_increase_sales"] = df_tail.actual_increase_sales.apply(lambda x:round(x,2))
    df_tail["actual_increase_quantity"] = (df_tail.cur_quantity - df_tail.last_quantity) / df_tail.last_quantity * 100
    df_tail["actual_increase_quantity"] = df_tail.actual_increase_quantity.apply(lambda x:round(x,2))
    # 客单价
    df_tail["unit_price"] = df_tail.cur_sales / df_tail.cur_quantity
    df_tail["unit_price"] = df_tail.unit_price.apply(lambda x:round(x,2))
    # 客单价增幅
    df_tail["unit_price_last"] = df_tail.last_sales / df_tail.last_quantity
    df_tail["unit_price_last"] = df_tail.unit_price_last.apply(lambda x:round(x,2))
    df_tail["unit_price_increase"] = (df_tail.unit_price - df_tail.unit_price_last) / df_tail.unit_price_last * 100
    df_tail["unit_price_increase"] = df_tail.unit_price_increase.apply(lambda x:round(x,2))
    df_tail["type"] = 4
    df_tail.replace([np.inf, -np.inf], 100, inplace=True)
    df_tail = df_tail.fillna(0)
    df_tail["im_type"] = 0
    df_tail["profit"] = 0
    df_tail["plan_quantity"] = 0
    df_tail["planned_increases_quantity"] = 0
    df_tail["compliance_quantity"] = 0
    df_tail["data_year"] = month.split('-')[0]
    df_tail["tail_plan_sales"] = 0
    df_tail = df_tail[["corp","month","type","im_type","tail_plan_sales","planned_increases_sales","cur_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","cur_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]]
    df_tail.columns = ["corp","date","type","im_type","plan_sales","planned_increases_sales","actual_sales","profit","compliance_sales","actual_increase_sales","plan_quantity","planned_increases_quantity","actual_quantity","compliance_quantity","actual_increase_quantity","unit_price","unit_price_increase","data_year","last_sales","last_quantity"]
    main_dff = df_flash.append(df_cross).append(df_second).append(df_tail).reset_index(drop=True)
    return main_dff


def exits_data(date, corp, sell_type, cursor):
    """
    验证是否已存在
    :param date: 日期
    :param sell_type
    :return:
    """
    sql = f"""select * from `vh_sales_analysis_statistics` where `date` = '{date}' and `corp` = '{corp}' and `type`= {sell_type}"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from `vh_sales_analysis_statistics` where `date` = '{date}' and `corp` = '{corp}' and `type` = {sell_type}"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(*args):
    cursor = args[20]
    exits_data(args[1],args[0],args[2], cursor)
    sql = f"""insert into `vh_sales_analysis_statistics`
             (`corp`,`date`, `type`, `im_type`, `plan_sales`, `planned_increases_sales`, `actual_sales`, `profit`,
             `compliance_sales`, `actual_increase_sales`, `plan_quantity`, `planned_increases_quantity`, `actual_quantity`,
             `compliance_quantity`, `actual_increase_quantity`, `unit_price`, `unit_price_increase`,`data_year`, `last_sales`, `last_quantity`) 
             values('{args[0]}','{args[1]}',{args[2]},
             {args[3]},{args[4]},{args[5]},
             {args[6]},{args[7]},{args[8]},{args[9]},
             {args[10]},{args[11]},{args[12]},
             {args[13]},{args[14]},{args[15]},{args[16]},'{args[17]}',{args[18]},{args[19]})"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
        


def handler_year_sales(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        datas = deal_with_data(rr_host, rr_port, rr_user, rr_password)
        data_list = datas.values.tolist()
        for data in data_list:
            insert_mysql(data[0], data[1], data[2], data[3],data[4], data[5], data[6], data[7], data[8], data[9], data[10], data[11], data[12], data[13], data[14], data[15], data[16], data[17], data[18], data[19], cursor)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()