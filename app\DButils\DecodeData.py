import requests
import json
import numpy as np
import warnings
from operator import itemgetter
from config import ConfigCenter

warnings.filterwarnings('ignore')


def get_encrypt_data(raw_data):
    """
    解密操作
    :param raw_data:
    :return:
    """
    try:
        # url_test = "http://47.105.104.93:30292/v1/encrypt"
        url = ConfigCenter.decrypt_url["decrypt_url"]

        payload = {
            "orig_data": [raw_data],
            "from":"data",
            "uid":"0",
            "operator": "bqy"
        }
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request(
            "POST", url, headers=headers, data=json.dumps(payload))

        ret = response.json()
        return ret['data'][raw_data]
    except Exception as e:
        print(e)
        return raw_data

def get_encrypt_datas(raw_datas):
    """
    批量解密操作
    :param raw_datas:
    :return:
    """
    try:
        url = "https://callback.vinehoo.com/des-server/v1/decrypt"
        payload = {
            "orig_data":list(raw_datas),
        "from":"data",
        "uid":"0",
        "operator": "bqy"
        }
        headers = {
            'content-type': "application/json",
            'cache-control': "no-cache",
            'postman-token': "c380ea43-d458-1ea5-62e6-be1f1c07ece5"
        }
        response = requests.request("POST", url, data=json.dumps(payload), headers=headers)
        json_str = response.text
        new_dict = json.loads(json_str)
        aa=new_dict.get('data')
        key=list(raw_datas)
        out=itemgetter(*key)(aa)
        bb=np.array(out).reshape(raw_datas.size,1)
        return bb
    except Exception as e:
        print(e)
        return raw_datas



