import smtplib
from email.mime.text import MIMEText


def send_email(text):
    # 定义变量 MIMEText(msg,type,chartst)
    try:
        from_addr = '<EMAIL>'  # 发信方的地址信息
        password = 'bneiqzgdryhhijci'  # 发信方的授权码
        to_addr = '<EMAIL>'  # 收件方邮箱
        smtp_sever = 'smtp.qq.com'  # 发信服务器
        msg = MIMEText(f'{text}', 'plain', 'utf-8')  # 发送的文本信息
        # 使用方法
        sever = smtplib.SMTP_SSL(smtp_sever)  # 开启发信服务，这里是使用加密传输
        sever.connect(smtp_sever)  # 链接服务器（服务器，端口）
        sever.login(from_addr, password)  # 登录发信邮箱
        sever.sendmail(from_addr, to_addr, msg.as_string())  # 发送邮件
        sever.quit()  # 退出
    except Exception as e:
        print(e)
