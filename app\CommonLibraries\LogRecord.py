# -*- coding: utf-8 -*-
# 日志记录
import os
from app.CommonLibraries.GetNowTime import get_now_time


def record_error(error_function, error_info):
    """
    日志记录
    :param error_function: 错误方法
    :param error_info: 错误信息
    :return:
    """
    now_time = get_now_time('date')
    now_date = now_time.split(' ')[0]
    path_dir = os.getcwd()
    path = os.path.join(path_dir, 'ProjectLog')
    file = os.path.join(path, '{}-log.txt'.format(now_date))
    log_json = {
        "error_time": now_time,
        "error_function": error_function,
        "error_info": error_info
    }
    if os.path.exists(path) is False:
        os.makedirs(path)
    with open(file, 'a') as f:
        f.write(f"{log_json}\n")
