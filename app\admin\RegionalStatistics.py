# -*- coding: utf-8 -*-
# 区域统计
import pymysql
import pandas as pd
import requests
from app.CommonLibraries.getConfig import get_config
import json

response = requests.session()


def conn_mysql(host, port, username, password, database):
    """
    链接数据库
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=username, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_regional_statistics(start_time, end_time, conn_statics):
    """
    获取区域统计数据
    :param start_time:开始时间
    :param end_time:结束时间
    :param conn_statics 数据库连接对象
    :return:
    """
    # config_info = get_config(data_id="db.v3_all", group="vinehoo.accounts")
    # host = config_info['host']
    # port = config_info['port']
    # username = config_info['user']
    # password = config_info['password']
    # conn = conn_mysql(host=host, port=port, username=username, password=password, database="vh_data_statistics")
    sql = f""" SELECT `id`,`day`,`province`,`flash_sales`,`flash_orders`,`flash_proportion`,`flash_buy_users`,`second_sales`,`second_orders`,`second_proportion`,`second_buy_users`,`cross_sales`,`cross_orders`,`cross_proportion`,`cross_buy_users`,`tail_sales`,`tail_orders`,`tail_proportion`,`tail_buy_users` FROM vh_regional_statistics FORCE INDEX (`day`) WHERE `day` BETWEEN '{start_time}' AND '{end_time}'"""
    df = pd.read_sql(sql, conn_statics)
    if df.empty:
        return []
    if start_time == end_time:
        df = df.drop('id', axis=1)
    else:
        df1 = df[['province', 'flash_sales']].groupby('province').sum().reset_index()
        df2 = df[['province', 'flash_orders']].groupby('province').sum().reset_index()
        df3 = df[['province', 'flash_buy_users']].groupby('province').sum().reset_index()
        df4 = df[['province', 'second_sales']].groupby('province').sum().reset_index()
        df5 = df[['province', 'second_orders']].groupby('province').sum().reset_index()
        df6 = df[['province', 'second_buy_users']].groupby('province').sum().reset_index()
        df7 = df[['province', 'cross_sales']].groupby('province').sum().reset_index()
        df8 = df[['province', 'cross_orders']].groupby('province').sum().reset_index()
        df9 = df[['province', 'cross_buy_users']].groupby('province').sum().reset_index()
        df10 = df[['province', 'tail_sales']].groupby('province').sum().reset_index()
        df11 = df[['province', 'tail_orders']].groupby('province').sum().reset_index()
        df12 = df[['province', 'tail_buy_users']].groupby('province').sum().reset_index()
        df_re = pd.merge(df1, df2, how='left').merge(df3, how='left'). \
            merge(df4, how='left').merge(df5, how='left').merge(df6, how='left'). \
            merge(df7, how='left').merge(df8, how='left').merge(df9, how='left'). \
            merge(df10, how='left').merge(df11, how='left').merge(df12, how='left')
        df_re['flash_proportion'] = df_re['flash_sales'].apply(lambda x: round(x / df_re['flash_sales'].sum(), 4) * 100)
        df_re['second_proportion'] = df_re['second_sales'].apply(
            lambda x: round(x / df_re['second_sales'].sum(), 4) * 100)
        df_re['cross_proportion'] = df_re['cross_sales'].apply(lambda x: round(x / df_re['cross_sales'].sum(), 4) * 100)
        df_re['tail_proportion'] = df_re['tail_sales'].apply(lambda x: round(x / df_re['tail_sales'].sum(), 4) * 100)
        df = df_re.copy()
        df = df.fillna(0)
    res_json = json.loads(df.to_json(orient='records'))
    return res_json
