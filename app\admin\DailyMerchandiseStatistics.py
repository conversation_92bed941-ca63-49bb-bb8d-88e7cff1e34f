# -*- coding: utf-8 -*-
# 每日商品统计
import pymysql
import pandas as pd
import requests

from app.CommonLibraries.getConfig import get_config
from config import ConfigCenter, SortKeyConfig
import json

response = requests.session()


def conn_mysql(host, port, username, password, database):
    """
    链接数据库
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=username, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_daily_merchandise_info(start_time, end_time, sort_key, page, page_nums, conn_statics):
    """
    获取每日上线商品信息
    :param start_time:开始时间
    :param end_time:结束时间
    :param sort_key:排序字段
    :param page:页数
    :param page_nums:每页条数
    :return:
    """
    if sort_key == "":
        sql = f"""SELECT * FROM vh_daily_merchandise_statistics 
                          WHERE FROM_UNIXTIME(stime) BETWEEN '{start_time}' AND '{end_time}' 
                          limit {page * page_nums - page_nums},{page_nums}"""
        sql_count = f"""SELECT count(1) 'count' FROM vh_daily_merchandise_statistics 
                          WHERE FROM_UNIXTIME(stime) BETWEEN '{start_time}' AND '{end_time}' """

    elif sort_key in ("0", "1", "3", "5"):
        sql = f"""SELECT * FROM vh_daily_merchandise_statistics 
                  WHERE FROM_UNIXTIME(stime) BETWEEN '{start_time}' AND '{end_time}' 
                  ORDER BY '{SortKeyConfig.sort_key_config[int(sort_key)]}' DESC
                  limit {page * page_nums - page_nums},{page_nums}"""
        sql_count = f"""SELECT count(1) 'count' FROM vh_daily_merchandise_statistics 
                  WHERE FROM_UNIXTIME(stime) BETWEEN '{start_time}' AND '{end_time}' 
                  ORDER BY '{SortKeyConfig.sort_key_config[int(sort_key)]}' DESC"""

    elif sort_key in ("2", "4", "6"):
        sql = f"""SELECT * FROM vh_daily_merchandise_statistics 
                          WHERE FROM_UNIXTIME(stime) BETWEEN '{start_time}' AND '{end_time}' 
                          ORDER BY '{SortKeyConfig.sort_key_config[int(sort_key)]}' ASC
                          limit {page * page_nums - page_nums},{page_nums}"""
        sql_count = f"""SELECT count(1) 'count' FROM vh_daily_merchandise_statistics 
                          WHERE FROM_UNIXTIME(stime) BETWEEN '{start_time}' AND '{end_time}' 
                          ORDER BY '{SortKeyConfig.sort_key_config[int(sort_key)]}' DESC"""

    else:
        pass
    
    df = pd.read_sql(sql, conn_statics)
    df_count = pd.read_sql(sql_count, conn_statics)
    total = int(df_count['count'][0])
    df = df.drop('id', axis=1)
    res_json = {"data": json.loads(df.to_json(orient='records')), "total": total}
    return res_json
