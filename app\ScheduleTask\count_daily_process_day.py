import pymysql
import pandas as pd
from datetime import datetime
from dateutil.relativedelta import relativedelta
import json
import numpy as np
import warnings

warnings.filterwarnings("ignore")

# 日销售进度

tables_type = {"vh_flash_order": 1,
               "vh_cross_order": 2,
               "vh_second_order": 3,
               "vh_tail_order": 4}

periods_table_type = {"vh_periods_flash": 1,
                      "vh_periods_cross": 2,
                      "vh_periods_second": 3,
                      "vh_periods_leftover": 4}


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_last_month():
    # 获取前一个月
    try:
        month_date = datetime.now().date() - relativedelta(months=1)
        return month_date.strftime("%Y-%m")
    except Exception as e:
        print(f"获取前一月失败,失败原因:{e}")


def get_cur_month():
    # 获取当前月
    return datetime.now().strftime("%Y-%m")


def get_cur_date():
    # 获取当前日
    return datetime.now().strftime("%d")


def deal_list_wine_info(wine_info):
    data_list = []
    try:
        for i in json.loads(wine_info):
            data_list.append(i['product_id'])
    except:
        data_list.append('')
    return data_list


def deal_list_nums(nums):
    data_list = []
    try:
        for i in json.loads(nums):
            data_list.append(i['nums'])
    except:
        data_list.append('')
    return data_list


def get_plans(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取计划数据
    :param month:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_data_statistics")
    if month[0:4] =="2022":
        sql = f"""SELECT concat(operation_duty,"+",operation_duty_assist) "operation_duty",sales_plan,single_plan,cross_border_plan,second_delivery_plan,tail_sales_plan,`date`,week,remarks FROM vh_sales_analysis_plan WHERE date LIKE '{month}%'"""
    else:
        sql = f"""SELECT concat(operation_duty,"+",operation_duty_assist) "operation_duty",flash_redwine_sales_plan,flash_baijiu_sales_plan,main_single_plan 'single_plan',cross_border_plan,second_delivery_plan,tail_sales_plan,`date`,week,remarks FROM vh_sales_analysis_plan WHERE date LIKE '{month}%'"""    
    df = pd.read_sql(sql, conn)
    conn.close()
    return df



def get_order_info(order_table, month, periods, rr_host, rr_port, rr_user, rr_password):
    """
    获取订单数据
    :return:
    """
    req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
    dff = pd.DataFrame(columns=req_columns)
    if len(periods) == 0:
        return dff
    else:
        conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
        req_tables = {"vh_flash_order": 1, "vh_cross_order": 2, "vh_second_order": 3, "vh_tail_order": 4}
        tables_type = req_tables[order_table]
        if tables_type != 2:
            if len(periods) > 1:
                sql = f"""SELECT FROM_UNIXTIME( created_time, '%Y-%m-%d') 'date', payment_amount 'sales_money',1 'sales_order_count', coupon_id FROM {order_table} WHERE sub_order_status IN( 1, 2, 3 ) AND FROM_UNIXTIME( created_time, '%Y-%m' ) = '{month}' AND period in {periods} AND special_type != 4 """
            elif len(periods) == 1:
                sql = f"""SELECT FROM_UNIXTIME( created_time, '%Y-%m-%d') 'date', payment_amount 'sales_money',1 'sales_order_count', coupon_id FROM {order_table} WHERE sub_order_status IN( 1, 2, 3 ) AND FROM_UNIXTIME( created_time, '%Y-%m' ) = '{month}' AND period = {periods[0]} AND special_type != 4 """
        else:
            if len(periods) > 1:
                sql = f"""SELECT FROM_UNIXTIME( created_time, '%Y-%m-%d') 'date', payment_amount 'sales_money',1 'sales_order_count', 'cross' AS 'coupon_id' FROM {order_table} WHERE sub_order_status IN( 1, 2, 3 ) AND FROM_UNIXTIME( created_time, '%Y-%m' ) = '{month}' AND period in {periods} """
            elif len(periods) == 1:
                sql = f"""SELECT FROM_UNIXTIME( created_time, '%Y-%m-%d') 'date', payment_amount 'sales_money',1 'sales_order_count', 'cross' AS 'coupon_id' FROM {order_table} WHERE sub_order_status IN( 1, 2, 3 ) AND FROM_UNIXTIME( created_time, '%Y-%m' ) = '{month}' AND period = {periods[0]} """
        df = pd.read_sql(sql, conn)
        df['sell_type'] = tables_type
        dff = dff.append(df).reset_index(drop=True)
        conn.close()
        return dff
    

    
    
def get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password):
    """
    获取膨胀定金数据
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    if len(coupon_issue_ids) == 1:
        sql = f""" select coupon_issue_id AS 'coupon_id',deposit from vh_deposit_inflation_record where coupon_issue_id = '{coupon_issue_ids[0]}'"""
    else:
        sql = f""" select coupon_issue_id AS 'coupon_id',deposit from vh_deposit_inflation_record where coupon_issue_id in {coupon_issue_ids}"""
    dff = pd.read_sql_query(sql,conn)
    dff["coupon_id"] = dff.coupon_id.astype(str)
    conn.close()
    return dff
    
    
    
    

def get_order_base(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取订单基础数据
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    req_tables = {"vh_flash_order": 1, "vh_cross_order": 2, "vh_second_order": 3, "vh_tail_order": 4}
    req_columns = ["period_id"]
    dff = pd.DataFrame(columns=req_columns)
    for table in req_tables.keys():
        sql = f"""SELECT distinct period 'period_id' FROM {table} WHERE sub_order_status IN( 1, 2, 3 ) AND FROM_UNIXTIME( created_time, '%Y-%m' ) = '{month}'"""
        df = pd.read_sql(sql, conn)
        df['sell_type'] = req_tables[table]
        dff = dff.append(df).reset_index(drop=True)
    mh_sql = f"""  select period from vh_order_mystery_box_log group by `period` """
    mh_dff = pd.read_sql_query(mh_sql,conn)
    mh_periods = tuple(set(mh_dff.period.tolist()))
    dff = dff[dff.period_id.apply(lambda x:x not in mh_periods)]
    conn.close()
    return dff


def get_cost(order_table, month, periods, rr_host, rr_port, rr_user, rr_password):
    """
    获取成本信息
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    req_tables = {"vh_flash_order": 1, "vh_cross_order": 2, "vh_second_order": 3, "vh_tail_order": 4}
    tables_type = req_tables[order_table]
    conn_periods = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_commodities")
    tables = {"vh_flash_order": "vh_periods_flash_set",
              "vh_cross_order": "vh_periods_cross_set",
              "vh_tail_order": "vh_periods_leftover_set",
              "vh_second_order": "vh_periods_second_set"}
    # 获取所有订单信息
    dff_all_orders = pd.DataFrame(columns=['package_id', 'order_qty', 'date'])
    # for per_table in tables.keys():
    if tables_type != 2:
        if len(periods) != 1:
            sql = f"""SELECT sub_order_no,main_order_id,package_id,period,order_qty,FROM_UNIXTIME(created_time,'%Y-%m-%d') 'date' FROM {order_table} WHERE sub_order_status IN (1,2,3) AND special_type != 4 AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period in {periods}"""
            sql_dis_packid = f"""SELECT DISTINCT package_id FROM `{order_table}` WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period in {periods}"""
        else:
            sql = f"""SELECT sub_order_no,main_order_id,package_id,period,order_qty,FROM_UNIXTIME(created_time,'%Y-%m-%d') 'date' FROM {order_table} WHERE sub_order_status IN (1,2,3) AND special_type != 4 AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period = {periods[0]}"""
            sql_dis_packid = f"""SELECT DISTINCT package_id FROM `{order_table}` WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period = {periods[0]}"""
    else:
        if len(periods) != 1:
            sql = f"""SELECT sub_order_no,main_order_id,package_id,period,order_qty,FROM_UNIXTIME(created_time,'%Y-%m-%d') 'date' FROM {order_table} WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period in {periods}"""
            sql_dis_packid = f"""SELECT DISTINCT package_id FROM `{order_table}` WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period in {periods}"""
        else:
            sql = f"""SELECT sub_order_no,main_order_id,package_id,period,order_qty,FROM_UNIXTIME(created_time,'%Y-%m-%d') 'date' FROM {order_table} WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period = {periods[0]}"""
            sql_dis_packid = f"""SELECT DISTINCT package_id FROM `{order_table}` WHERE sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' AND period = {periods[0]}"""
    df_order = pd.read_sql(sql, conn)
    dff_all_orders = dff_all_orders.append(df_order)
    df_all_cost = pd.DataFrame(columns=['package_id', 'costprice', 'sell_type'])
    df_package_id = pd.read_sql(sql_dis_packid, conn)
    package_ids = tuple(df_package_id['package_id'].tolist())
    if len(package_ids) == 1:
        sql = f"""SELECT id 'package_id' ,associated_products FROM `{tables[order_table]}` WHERE id = {package_ids[0]}"""
    elif len(package_ids) == 0:
        sql = f"""SELECT id 'package_id' ,associated_products FROM `{tables[order_table]}` WHERE id = 0"""
    else:
        sql = f"""SELECT id 'package_id' ,associated_products FROM `{tables[order_table]}` WHERE id IN {package_ids}"""
    df_set = pd.read_sql(sql, conn_periods)
    transfer_df = pd.merge(dff_all_orders,df_set,how = "left",on = "package_id")
    main_id = tuple(set(transfer_df.main_order_id.tolist()))
    if len(main_id) > 1:
        main_order_no_sql = f""" select id as 'main_order_id',main_order_no from vh_order_main where id in {main_id} """
    elif len(main_id) == 1:
        main_order_no_sql = f""" select id as 'main_order_id',main_order_no from vh_order_main where id = {main_id[0]} """
    else:
        main_order_no_sql = f""" select id as 'main_order_id',main_order_no from vh_order_main where id = 0 """
    main_order_no_dff = pd.read_sql_query(main_order_no_sql,conn)
    transfer_df = pd.merge(transfer_df,main_order_no_dff,how="left",on="main_order_id")
    
    mainOrderNo = tuple(set(transfer_df.main_order_no.tolist()))
    if len(mainOrderNo) > 1:
        mh_sql = f"""  select main_order_no,period,product_info from vh_order_mystery_box_log where main_order_no in {mainOrderNo} """
    elif len(main_id) == 1:
        mh_sql = f"""  select main_order_no,period,product_info from vh_order_mystery_box_log where main_order_no = '{mainOrderNo[0]}' """
    else:
        mh_sql = f"""  select main_order_no,period,product_info from vh_order_mystery_box_log where main_order_no = '' """
    mh_dff = pd.read_sql_query(mh_sql,conn)
    transfer_df = pd.merge(transfer_df,mh_dff,how="left",on=["main_order_no","period"])
    for index, row in transfer_df.iterrows():
        if pd.notna(row['product_info']):
            transfer_df.at[index, 'associated_products'] = row['product_info']
    transfer_df.drop(['main_order_no','main_order_id','product_info'],axis=1,inplace=True)
    transfer_df["associated_products"]=transfer_df.associated_products.apply(lambda x:eval(x) if type(x) != float else x)
    is_any = []
    for is_a in transfer_df.associated_products:
        if is_a == []:
            a = -1
        elif len(is_a) == 1:
            a = 0
        elif len(is_a) > 1:
            a = 1
        is_any.append(a)
    transfer_df["is_any"] = is_any
    main_data_n = transfer_df[transfer_df["is_any"] == -1]
    if main_data_n.empty == False:
        n_index = main_data_n.index.tolist()
        main_data_n.drop(index=n_index,axis=0,inplace=True)
        main_data_any = transfer_df[transfer_df["is_any"] == 1]
        main_data_one = transfer_df[transfer_df["is_any"] == 0]
    else:
        main_data_any = transfer_df[transfer_df["is_any"] == 1]
        main_data_one = transfer_df[transfer_df["is_any"] == 0]

    if main_data_any.empty and main_data_one.empty:
        return pd.DataFrame({})

    if main_data_any.empty and main_data_one.empty == False:
        dff_main_data = main_data_one
        dff_main_data["product_id"] = dff_main_data.associated_products.apply(lambda x:x[0]["product_id"])
        dff_main_data["nums"] = dff_main_data.associated_products.apply(lambda x:x[0]["nums"])        
    else:
        main_data_one["product_id"] = main_data_one.associated_products.apply(lambda x:x[0]["product_id"])
        main_data_one["nums"] = main_data_one.associated_products.apply(lambda x:x[0]["nums"])    
        main_data_any = main_data_any.explode('associated_products').reset_index(drop=True)
        main_data_any["mark"] = main_data_any.associated_products.apply(lambda x:json.dumps(x))
        main_data_any = main_data_any.drop_duplicates(subset=["sub_order_no","mark"],keep="first")
        main_data_any["product_id"] = main_data_any.associated_products.apply(lambda x:x["product_id"])
        main_data_any["nums"] = main_data_any.associated_products.apply(lambda x:x["nums"])
        dff_main_data = main_data_one.append(main_data_any).reset_index(drop=True)
    mh_sql = f"""  select period from vh_order_mystery_box_log group by `period` """
    mh_dff = pd.read_sql_query(mh_sql,conn)
    mh_periods = tuple(set(mh_dff.period.tolist()))
    dff_main_data = dff_main_data[dff_main_data.period.apply(lambda x:x not in mh_periods)]
    product_id = tuple(dff_main_data.product_id.tolist())
    period = tuple(dff_main_data.period.tolist())
    if len(product_id) == 1 and len(period) == 1:
        in_sql = f"select product_id,period,costprice from vh_periods_product_inventory where product_id = {product_id[0]} and period = {period[0]}"
    else:    
        in_sql = f"select product_id,period,costprice from vh_periods_product_inventory where product_id in {product_id} and period in {period}"
    in_df = pd.read_sql_query(in_sql, conn_periods)
    OVER_ZY = pd.merge(dff_main_data, in_df, how="left", on=["period","product_id"])
    OVER_ZY["costprice"] = OVER_ZY.costprice * OVER_ZY.nums * OVER_ZY.order_qty
    OVER_ZY.drop(["package_id","order_qty","sub_order_no","period","associated_products","product_id","nums","is_any"],axis=1,inplace=True)
    OVER_ZY = OVER_ZY.groupby(by="date",as_index=False).sum()
    OVER_ZY.rename(columns={"costprice":"cost"},inplace=True)
    return OVER_ZY

def get_liquor(rr_host, rr_port, rr_user, rr_password, periods_base):
    """
    获取白酒id
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, 'vh_commodities')
    
    dff = pd.DataFrame(columns=['period_id', 'sell_type'])
    where = "product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%'"
    for table in periods_table_type.keys():
        if len(periods_base) == 1:
            sql = f"""SELECT id 'period_id' FROM `{table}` WHERE ({where}) AND id = {periods_base[0]}"""
        else:
            sql = f"""SELECT id 'period_id' FROM `{table}` WHERE ({where}) AND id in {periods_base}"""
        df = pd.read_sql(sql, conn)
        df['sell_type'] = periods_table_type[table]
        dff = dff.append(df)
    dff = dff.reset_index(drop=True)
    return dff

def get_schnapps(rr_host, rr_port, rr_user, rr_password, periods_base):
    """
    获取烈酒id
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, 'vh_commodities')
    # result = []
    # get_subcategories(conn, 22, result)
    # where = ''
    # for entry in result:
    #     if where == "":
    #         where = f"""product_category LIKE '%{entry['name']}%'"""
    #     else:
    #         where = f"""{where} or product_category LIKE '%{entry['name']}%'"""
    where = "product_category LIKE '%烈酒%' or product_category LIKE '%白兰地%' or product_category LIKE '%威士忌%' or product_category LIKE '%金酒%' or product_category LIKE '%伏特加%' or product_category LIKE '%朗姆酒%' or product_category LIKE '%龙舌兰%' or product_category LIKE '%雅文邑%' or product_category LIKE '%果渣白兰地%' or product_category LIKE '%皮斯科%' or product_category LIKE '%卡尔瓦多斯%' or product_category LIKE '%其他白兰地%' or product_category LIKE '%调和威士忌%' or product_category LIKE '%单一麦芽威士忌%' or product_category LIKE '%单一谷物威士忌%' or product_category LIKE '%其他威士忌%'"
    dff = pd.DataFrame(columns=['period_id', 'sell_type'])
    for table in periods_table_type.keys():
        if len(periods_base) == 1:
            sql = f"""SELECT id 'period_id' FROM `{table}` WHERE ({where}) AND id = {periods_base[0]}"""
        else:
            sql = f"""SELECT id 'period_id' FROM `{table}` WHERE ({where}) AND id in {periods_base}"""
        df = pd.read_sql(sql, conn)
        df['sell_type'] = periods_table_type[table]
        dff = dff.append(df)
    dff = dff.reset_index(drop=True)
    return dff

def get_foodstuff(rr_host, rr_port, rr_user, rr_password, periods_base):
    """
    获取食品id
    :return:
    """
    conn_wiki = rr_conn(rr_host, rr_port, rr_user, rr_password, 'vh_wiki')
    sql = "SELECT id,name FROM `vh_product_type` WHERE pid = 3"
    type_df = pd.read_sql(sql, conn_wiki)
    type_name = tuple(set(type_df.name.tolist()))
    # 转换为列表
    type_name_list = list(type_name)
    where = ""
    for entry in type_name_list:
        if where == "":
            where = f"""product_category LIKE '%{entry}%'"""
        else:
            where = f"""{where} or product_category LIKE '%{entry}%'"""

    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, 'vh_commodities')

    dff = pd.DataFrame(columns=['period_id', 'sell_type'])
    for table in periods_table_type.keys():
        if len(periods_base) == 1:
            sql = f"""SELECT id 'period_id' FROM `{table}` WHERE ({where}) AND id = {periods_base[0]}"""
        else:
            sql = f"""SELECT id 'period_id' FROM `{table}` WHERE ({where}) AND id in {periods_base}"""
        df = pd.read_sql(sql, conn)
        df['sell_type'] = periods_table_type[table]
        dff = dff.append(df)
    dff = dff.reset_index(drop=True)
    return dff

def get_subcategories(conn, start_id, result=None):
    if result is None:
        result = []

    # 执行查询
    if len(result) == 0:
        sql = f"SELECT * FROM vh_wiki.vh_product_type WHERE id = {start_id}"
    else:
        sql = f"SELECT * FROM vh_wiki.vh_product_type WHERE fid = {start_id}"
    df = pd.read_sql(sql, conn)
    # 将查询结果追加到 result 列表中
    result.extend(df.to_dict(orient='records'))
    # 递归查询下级
    for subcategory in df.itertuples(index=False):
        get_subcategories(conn, subcategory.id, result)


def deal_with_data(rr_host, rr_port, rr_user, rr_password):
    if datetime.now().strftime('%d') == '01':
        month = get_last_month()
    else:
        month = get_cur_month()
    df_plan = get_plans(month, rr_host, rr_port, rr_user, rr_password)
    df_order_base = get_order_base(month, rr_host, rr_port, rr_user, rr_password)
    periods_base = tuple(list(set(df_order_base['period_id'].tolist())))

    # 食品————————————————————————————————
    df_foodstuff = get_foodstuff(rr_host, rr_port, rr_user, rr_password, periods_base)
    df_order_base_sp_period = pd.merge(df_order_base, df_foodstuff)
    req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
    df_order = pd.DataFrame(columns=req_columns)
    if len(df_order_base_sp_period) > 0:
        periods = tuple(df_order_base_sp_period['period_id'].tolist())
        for table, table_type in tables_type.items():
            sub_df_order = get_order_info(table, month, periods, rr_host, rr_port, rr_user, rr_password)
            # df_order = df_order.append(sub_df_order)
            df_order = pd.concat([df_order, sub_df_order], ignore_index=True)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order['sales_money'] = df_order['sales_money'].fillna(0)
            df_order['sales_money'] = df_order['sales_money'].round(2)
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order['sales_money'] = df_order.sales_money.fillna(0)
            # 处理精度问题
            df_order["sales_money"] = round(df_order.sales_money + df_order.deposit, 2)
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end10 = pd.DataFrame(columns=col_list)
    else:
        req_columns = ["date", "cost", "mark"]
        df_cost = pd.DataFrame(columns=req_columns)
        for table, table_type in tables_type.items():
            sub_df_cost = get_cost(table, month, periods, rr_host, rr_port, rr_user, rr_password)
            if sub_df_cost.empty == False:
                df_cost = pd.concat([df_cost, sub_df_cost], ignore_index=True)

        # 对 cost 列按照日期进行分组，并求和
        df_cost = df_cost.groupby("date", as_index=False).agg({"cost": "sum", "mark": "first"})
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'tail_sales_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end10 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'gross_margin', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'single_volume_difference',"cost"]]
        
        dff_end10['monthly_compliance_quantity'] = 0
        dff_end10['poor_sales'] = 0
        dff_end10['monthly_compliance_sales'] = 0
        dff_end10['daily_compliance_sales'] = 0
        dff_end10['sell_type'] = 0
        dff_end10['is_liquor'] = 3

    # 烈酒————————————————————————————————
    df_liquor = get_schnapps(rr_host, rr_port, rr_user, rr_password, periods_base)
    df_order_base_lj_period = pd.merge(df_order_base, df_liquor)

    req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
    df_order = pd.DataFrame(columns=req_columns)
    if len(df_order_base_lj_period) > 0:
        periods = tuple(df_order_base_lj_period['period_id'].tolist())
        for table, table_type in tables_type.items():
            sub_df_order = get_order_info(table, month, periods, rr_host, rr_port, rr_user, rr_password)
            # df_order = df_order.append(sub_df_order)
            df_order = pd.concat([df_order, sub_df_order], ignore_index=True)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order['sales_money'] = df_order['sales_money'].fillna(0)
            df_order['sales_money'] = df_order['sales_money'].round(2)
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order['sales_money'] = df_order.sales_money.fillna(0)
            df_order["sales_money"] = round(df_order.sales_money + df_order.deposit, 2)
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end9 = pd.DataFrame(columns=col_list)
    else:
        req_columns = ["date", "cost", "mark"]
        df_cost = pd.DataFrame(columns=req_columns)
        for table, table_type in tables_type.items():
            sub_df_cost = get_cost(table, month, periods, rr_host, rr_port, rr_user, rr_password)
            if sub_df_cost.empty == False:
                df_cost = pd.concat([df_cost, sub_df_cost], ignore_index=True)

        # 对 cost 列按照日期进行分组，并求和
        df_cost = df_cost.groupby("date", as_index=False).agg({"cost": "sum", "mark": "first"})
        
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        # dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['tail_sales_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        # money_c = []
        # sales_money_list = dff_end['sales_money'].tolist()
        # tail_sales_plan_list = dff_end['tail_sales_plan'].tolist()
        # for i in range(1, len(dff_end) + 1):
        #     money_c.append(sum(sales_money_list[0:i]) - sum(tail_sales_plan_list[0:i]))
        #     i += 1
        # dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        # dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        # standard = []
        # for i in range(1, len(dff_end) + 1):
        #     if sum(tail_sales_plan_list[0:i]) == 0:
        #         standard.append(0)
        #     else:
        #         standard.append(round((sum(sales_money_list[0:i]) / sum(tail_sales_plan_list[0:i])) * 100, 2))
        #     i += 1
        # dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        # dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        # standard_m = []
        # for i in range(1, len(dff_end) + 1):
        #     standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
        #     i += 1
        # dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        # dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'tail_sales_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end9 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'gross_margin', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'single_volume_difference',"cost"]]
        
        dff_end9['monthly_compliance_quantity'] = 0
        dff_end9['poor_sales'] = 0
        dff_end9['monthly_compliance_sales'] = 0
        dff_end9['daily_compliance_sales'] = 0
        dff_end9['sell_type'] = 0
        dff_end9['is_liquor'] = 2

    # 白酒
    df_liquor = get_liquor(rr_host, rr_port, rr_user, rr_password, periods_base)
    df_order_base_period = pd.merge(df_order_base, df_liquor)
    # 闪购————————————————————————————————
    df_order_base_period_1_flash = df_order_base_period[df_order_base_period['sell_type'] == 1]
    if df_order_base_period_1_flash.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_1_flash['period_id'].tolist())
        df_order = get_order_info("vh_flash_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 1]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type","sales_order_count"]
        dff_end1 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_flash_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.merge(df_order, df_cost)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        if int(month[0:4]) >= 2023:
            dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['flash_baijiu_sales_plan'])* 100, 2) 
        else:
            dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['sales_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []        
        sales_money_list = dff_end['sales_money'].tolist()
        sales_money_list = dff_end['sales_money'].tolist()
        if int(month[0:4]) >= 2023:
            sales_plan_list = dff_end['flash_baijiu_sales_plan'].tolist()
        else:
            sales_plan_list = dff_end['sales_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(sales_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(sales_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(sales_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round(dff_end['sales_order_count'] / dff_end['single_plan'], 2) * 100
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'sales_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'single_plan': 'plan_quantity', 'sales_order_count': 'actual_quantity'})
        if int(month[0:4]) >= 2023:
            dff_end1 = dff_end[
                ['week', 'date', 'operation_duty', 'remark', 'flash_baijiu_sales_plan', 'actual_sales',
                 'monthly_compliance_quantity',
                 'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                 'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                 "sell_type"]]
            dff_end1['is_liquor'] = 1
        else:
            dff_end1 = dff_end[
                ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                 'monthly_compliance_quantity',
                 'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                 'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                 "sell_type"]]
            dff_end1['is_liquor'] = 1
        dff_end1.columns=['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                 'monthly_compliance_quantity',
                 'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                 'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                 "sell_type","is_liquor"]
    # 跨境————————————————————————————————
    df_order_base_period_1_cross = df_order_base_period[df_order_base_period['sell_type'] == 2]
    if df_order_base_period_1_cross.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_1_cross['period_id'].tolist())
        df_order = get_order_info("vh_cross_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 2]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end2 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_cross_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['cross_border_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []
        sales_money_list = dff_end['sales_money'].tolist()
        cross_border_plan_list = dff_end['cross_border_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(cross_border_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(cross_border_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(cross_border_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'cross_border_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end2 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'monthly_compliance_quantity',
             'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
             "sell_type"]]
        dff_end2['is_liquor'] = 1
    # 秒发————————————————————————————————
    df_order_base_period_1_second = df_order_base_period[df_order_base_period['sell_type'] == 3]
    if df_order_base_period_1_second.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_1_second['period_id'].tolist())
        df_order = get_order_info("vh_second_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 3]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end3 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_second_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['second_delivery_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []
        sales_money_list = dff_end['sales_money'].tolist()
        second_delivery_plan_list = dff_end['second_delivery_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(second_delivery_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(second_delivery_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(second_delivery_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'second_delivery_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end3 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'monthly_compliance_quantity',
             'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
             "sell_type"]]
        dff_end3['is_liquor'] = 1
    # 尾货————————————————————————————————
    df_order_base_period_1_tail = df_order_base_period[df_order_base_period['sell_type'] == 4]
    if df_order_base_period_1_tail.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_1_tail['period_id'].tolist())
        df_order = get_order_info("vh_tail_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 4]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end4 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_tail_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['tail_sales_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []
        sales_money_list = dff_end['sales_money'].tolist()
        tail_sales_plan_list = dff_end['tail_sales_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(tail_sales_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(tail_sales_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(tail_sales_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'tail_sales_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end4 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'monthly_compliance_quantity',
             'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
             "sell_type"]]
        dff_end4['is_liquor'] = 1
    # 非白酒
    df_plan = get_plans(month, rr_host, rr_port, rr_user, rr_password)
    df_liquor = get_liquor(rr_host, rr_port, rr_user, rr_password,periods_base)
    df_liquor = df_liquor[['period_id']]
    df_order_base = get_order_base(month, rr_host, rr_port, rr_user, rr_password)
    df_merge_period = pd.merge(df_order_base, df_liquor).reset_index(drop=True)
    df_re_period = df_order_base.append(df_merge_period).drop_duplicates(subset='period_id', keep=False).reset_index(
        drop=True)
    # 闪购——————————————————————————————————————————
    df_order_base_period_0_flash = df_re_period[df_re_period['sell_type'] == 1]
    if df_order_base_period_0_flash.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_0_flash['period_id'].tolist())
        df_order = get_order_info("vh_flash_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 1]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end5 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_flash_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        if int(month[0:4]) >= 2023:
            dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['flash_redwine_sales_plan']) * 100, 2)
        else:
            dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['sales_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []
        sales_money_list = dff_end['sales_money'].tolist()
        if int(month[0:4]) >= 2023:
            sales_plan_list = dff_end['flash_redwine_sales_plan'].tolist()
        else:
            sales_plan_list = dff_end['sales_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(sales_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(sales_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(sales_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'sales_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'single_plan': 'plan_quantity', 'sales_order_count': 'actual_quantity'})
        if int(month[0:4]) < 2023:
            dff_end5 = dff_end[
                ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                 'monthly_compliance_quantity',
                 'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                 'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                 "sell_type"]]
            dff_end5['is_liquor'] = 0
        else:
            dff_end5 = dff_end[
                ['week', 'date', 'operation_duty', 'remark', 'flash_redwine_sales_plan', 'actual_sales',
                 'monthly_compliance_quantity',
                 'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                 'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                 "sell_type"]]
            dff_end5['is_liquor'] = 0
        dff_end5.columns=['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                 'monthly_compliance_quantity',
                 'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                 'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                 "sell_type","is_liquor"]
    # 跨境————————————————————————————————
    df_order_base_period_0_cross = df_re_period[df_re_period['sell_type'] == 2]
    if df_order_base_period_0_cross.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_0_cross['period_id'].tolist())
        df_order = get_order_info("vh_cross_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 2]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end6 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_cross_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['cross_border_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []
        sales_money_list = dff_end['sales_money'].tolist()
        cross_border_plan_list = dff_end['cross_border_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(cross_border_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(cross_border_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(cross_border_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'cross_border_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end6 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'monthly_compliance_quantity',
             'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
             "sell_type"]]
        dff_end6['is_liquor'] = 0
    # 秒发————————————————————————————————
    df_order_base_period_0_second = df_re_period[df_re_period['sell_type'] == 3]
    if df_order_base_period_0_second.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_0_second['period_id'].tolist())
        df_order = get_order_info("vh_second_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 3]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end7 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_second_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['second_delivery_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []
        sales_money_list = dff_end['sales_money'].tolist()
        second_delivery_plan_list = dff_end['second_delivery_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(second_delivery_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(second_delivery_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(second_delivery_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'second_delivery_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end7 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'monthly_compliance_quantity',
             'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
             "sell_type"]]
        dff_end7['is_liquor'] = 0
    # 尾货————————————————————————————————
    df_order_base_period_0_tail = df_re_period[df_re_period['sell_type'] == 4]
    if df_order_base_period_0_tail.empty:
        req_columns = ["date", "sales_money", "sales_order_count", "sell_type"]
        df_order = pd.DataFrame(columns=req_columns)
    else:
        periods = tuple(df_order_base_period_0_tail['period_id'].tolist())
        df_order = get_order_info("vh_tail_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        coupon_issue_ids = tuple(set(df_order.coupon_id.tolist()))
        deposit_massage = get_deposit_massage(coupon_issue_ids, rr_host, rr_port, rr_user, rr_password)
        if deposit_massage.empty:
            df_order["deposit"] = 0
            df_order = df_order[["date","sales_money","sales_order_count","sell_type"]].groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        else:
            df_order = pd.merge(df_order,deposit_massage,how="left",on="coupon_id")
            df_order["deposit"] = df_order.deposit.fillna(0)
            df_order["sales_money"] = df_order.sales_money + df_order.deposit
            df_order = df_order.groupby(by="date",as_index=False).agg({"sales_money":sum,"sales_order_count":sum,"sell_type":"first"})
        df_order = df_order[df_order['sell_type'] == 4]
    if len(df_order) == 0:
        col_list = ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
                    'monthly_compliance_quantity',
                    'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
                    'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
                    "sell_type"]
        dff_end8 = pd.DataFrame(columns=col_list)
    else:
        df_cost = get_cost("vh_tail_order", month, periods, rr_host, rr_port, rr_user, rr_password)
        dff = pd.concat([df_order, df_cost[['cost']]], axis=1)
        dff_re = pd.merge(df_plan, dff, how='left').fillna(0)
        dff_end = dff_re[dff_re['sales_money'] != 0]
        # 销售额日达标
        dff_end['daily_compliance_sales'] = round((dff_end['sales_money'] / dff_end['tail_sales_plan']) * 100, 2)
        dff_end = dff_end.replace([np.inf, -np.inf], 0)
        # 毛利率
        dff_end['gross_margin'] = round(((dff_end['sales_money'] - dff_end['cost']) / dff_end['sales_money']) * 100,
                                        2)
        # 销售差
        money_c = []
        sales_money_list = dff_end['sales_money'].tolist()
        tail_sales_plan_list = dff_end['tail_sales_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            money_c.append(sum(sales_money_list[0:i]) - sum(tail_sales_plan_list[0:i]))
            i += 1
        dff_money_c = pd.DataFrame(money_c).rename(columns={0: 'poor_sales'})
        dff_end = pd.merge(dff_end, dff_money_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取销售额月达标
        standard = []
        for i in range(1, len(dff_end) + 1):
            if sum(tail_sales_plan_list[0:i]) == 0:
                standard.append(0)
            else:
                standard.append(round((sum(sales_money_list[0:i]) / sum(tail_sales_plan_list[0:i])) * 100, 2))
            i += 1
        dff_standard = pd.DataFrame(standard).rename(columns={0: 'monthly_compliance_sales'})
        dff_end = pd.merge(dff_end, dff_standard, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量日达标
        dff_end['daily_compliance_quantity'] = round((dff_end['sales_order_count'] / dff_end['single_plan']) * 100, 2)
        # 获取客单价
        dff_end['unit_price'] = round(dff_end['sales_money'] / dff_end['sales_order_count'])
        # 获取单量差
        nums_c = []
        sales_nums = dff_end['sales_order_count'].tolist()
        single_plan = dff_end['single_plan'].tolist()
        for i in range(1, len(dff_end) + 1):
            nums_c.append(sum(sales_nums[0:i]) - sum(single_plan[0:i]))
            i += 1
        dff_nums_c = pd.DataFrame(nums_c).rename(columns={0: 'single_volume_difference'})
        dff_end = pd.merge(dff_end, dff_nums_c, on=dff_end.index, how='left').drop('key_0', axis=1)
        # 获取单量月达标
        standard_m = []
        for i in range(1, len(dff_end) + 1):
            standard_m.append(round((sum(sales_nums[0:i]) / sum(single_plan[0:i]) * 100), 2))
            i += 1
        dff_standard_m = pd.DataFrame(standard_m).rename(columns={0: 'monthly_compliance_quantity'})
        dff_end = pd.merge(dff_end, dff_standard_m, on=dff_end.index, how='left').drop('key_0', axis=1)
        dff_end = dff_end.rename(
            columns={'remarks': 'remark', 'tail_sales_plan': 'plan_sales', 'sales_money': 'actual_sales',
                     'sales_order_count': 'actual_quantity','single_plan': 'plan_quantity'})
        dff_end8 = dff_end[
            ['week', 'date', 'operation_duty', 'remark', 'plan_sales', 'actual_sales',
             'monthly_compliance_quantity',
             'gross_margin', 'poor_sales', 'monthly_compliance_sales', 'plan_quantity', 'actual_quantity',
             'daily_compliance_quantity', 'unit_price', 'daily_compliance_sales', 'single_volume_difference',"cost",
             "sell_type"]]
        dff_end8['is_liquor'] = 0
    dff_end_all = dff_end1.append(dff_end2).append(dff_end3).append(dff_end4).append(dff_end5).append(dff_end6).append(
        dff_end7).append(dff_end8).append(dff_end9).append(dff_end10).reset_index(drop=True)
    dff_end_all['month'] = month
    return dff_end_all


def exits_data(date, is_liquor, sell_type, cursor):
    """
    验证是否已存在
    :param date: 日期
    :param is_liquor:
    :param sell_type
    :return:
    """
    sql = f"""select * from vh_daily_sales_progress where `date` = '{date}' and `is_liquor`= {is_liquor} and `sell_type` = {sell_type}"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_daily_sales_progress where `date` = '{date}' and `is_liquor`= {is_liquor} and `sell_type` = {sell_type}"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(*args):
    cursor=args[20]
    exits_data(date=args[1], is_liquor=args[18], sell_type=args[17], cursor=cursor)
    sql = f"""insert into vh_daily_sales_progress 
             (`week`, `date`, `operation_duty`, `remark`, `plan_sales`, `actual_sales`, `monthly_compliance_quantity`,
             `gross_margin`, `poor_sales`, `monthly_compliance_sales`, `plan_quantity`, `actual_quantity`,
             `daily_compliance_quantity`, `unit_price`, `daily_compliance_sales`, `single_volume_difference`,`cost`,`sell_type`,
             `is_liquor`,`month`) 
             values('{args[0]}','{args[1]}','{args[2]}','{args[3]}',{args[4]},{args[5]},{args[6]},{args[7]},{args[8]},
             {args[9]},{args[10]},{args[11]},{args[12]},{args[13]},{args[14]},{args[15]},{args[16]},{args[17]},'{args[18]}','{args[19]}')"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")


def delete_mysql_today(today,rm_host, rm_port, rm_user, rm_password):
    conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
    cursor = conn.cursor()
    delete_today_sql = f"delete from vh_daily_sales_progress where `date` = '{today}' "
    try:
        cursor.execute(delete_today_sql)
        conn.commit()
    except Exception as e:
        print(f"删除数据失败,失败信息{e}")
    finally:
        conn.close()


def handler_daily_process(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        month = datetime.now().strftime("%Y-%m")
        day = datetime.now().strftime("%d")
        today = f"{month}-{day}"
        dff = deal_with_data(rr_host, rr_port, rr_user, rr_password)
        data_list = dff.values.tolist()
        for data in data_list:
            insert_mysql(data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8], data[9]
                         , data[10], data[11], data[12], data[13], data[14], data[15], data[16], data[17], data[18],data[19],
                         cursor)
        conn.commit()
        delete_mysql_today(today,rm_host, rm_port, rm_user, rm_password)
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()