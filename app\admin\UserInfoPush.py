import pymongo
import requests
from app.CommonLibraries.getConfig import get_config
import urllib
from datetime import datetime, timezone, timedelta
import pandas as pd

response = requests.session()


def conn_mongo(mongo_auth, mongo_password, mongo_client_master, mongo_port):
    """
    链接数据库
    :return:
    """
    try:
        mongo_cli = pymongo.MongoClient(
            'mongodb://%s:%s@%s:%s' % (
                mongo_auth, mongo_password, mongo_client_master, mongo_port))
        return mongo_cli
    except ConnectionError as ce:
        print(f"连接mogodb数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_count_register_users(conn_user_cursor):
    """
    获取注册用户总数
    :param conn_user_cursor:
    :return:
    """
    sql = """ select count(*) from vh_user"""
    conn_user_cursor.execute(sql)
    user_count = conn_user_cursor.fetchone()[0]
    return user_count


def get_count_register_users_yesterday(conn_user_cursor, day):
    """
    获取昨日新增用户总数
    :param conn_user_cursor:
    :param day
    :return:
    """
    sql = f""" SELECT count(*) FROM `vh_user` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}' """
    conn_user_cursor.execute(sql)
    user_count = conn_user_cursor.fetchone()[0]
    return user_count


def get_count_register_users_order_info_yesterday(conn_orders, conn_commodities, day):
    """
    获取昨日新增用户消费信息
    :param conn_orders:
    :param conn_commodities:
    :param day
    :return:
    """
    sql = f""" SELECT uid FROM `vh_user` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}' """
    df_uid = pd.read_sql(sql, conn_orders)
    uids = tuple(df_uid['uid'].tolist())
    sql = f"""SELECT COUNT(DISTINCT uid) 'order_users',SUM(payment_amount) 'total_sales',COUNT(id) 'order_count' FROM `vh_flash_order` WHERE uid in {uids} AND sub_order_status IN (1,2,3) AND FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}'"""
    order_info = pd.read_sql(sql, conn_orders)
    order_users = order_info['order_users'][0]
    total_sales = order_info['total_sales'][0]
    order_count = order_info['order_count'][0]
    per_sales = "%.2f" % total_sales/order_users
    return


def user_info_push(conn_orders, conn_orders_cursor, conn_user, conn_user_cursor):
    now_time = datetime.now()
    yesterday_time = now_time + timedelta(days=-1)
    yesterday_time = yesterday_time.strftime('%Y-%m-%d %H:%M:%S')
    yesterday = str(yesterday_time).split(' ')[0]
    yesterday_year = int(str(yesterday_time).split('-')[0])
    yesterday_month = int(str(yesterday_time).split('-')[1])
    yesterday_day = int(str(yesterday_time).split('-')[2].split(' ')[0])
    yesterday_start = f"{yesterday} 00:00:00"
    yesterday_end = f"{yesterday} 23:59:59"
    yesterday_hour_start = int(str(yesterday_start).split(' ')[1].split(':')[0])
    yesterday_minute_start = int(str(yesterday_start).split(' ')[1].split(':')[1])
    yesterday_second_start = int(str(yesterday_start).split(' ')[1].split(':')[2])
    yesterday_hour_end = int(str(yesterday_end).split(' ')[1].split(':')[0])
    yesterday_minute_end = int(str(yesterday_end).split(' ')[1].split(':')[1])
    yesterday_second_end = int(str(yesterday_end).split(' ')[1].split(':')[2])
    config_info = get_config(data_id="db.mongodb", group="vinehoo.accounts")
    mongo_client_master = config_info['mongo_client_master']
    mongo_port = config_info['mongo_port']
    mongo_auth = urllib.parse.quote_plus(config_info['mongo_auth'])
    mongo_password = urllib.parse.quote_plus(config_info['mongo_password'])
    client = conn_mongo(mongo_auth=mongo_auth, mongo_password=mongo_password, mongo_client_master=mongo_client_master,
                        mongo_port=mongo_port)
    db = client['vinehoo_v3']
    active_items = db['gateway_logs']
    pipeline = [
        {
            '$match': {
                'request_time': {
                    '$gte': datetime(yesterday_year, yesterday_month, yesterday_day, yesterday_hour_start,
                                     yesterday_minute_start, yesterday_second_start, tzinfo=timezone.utc),
                    '$lte': datetime(yesterday_year, yesterday_month, yesterday_day, yesterday_hour_end,
                                     yesterday_minute_end, yesterday_second_end, tzinfo=timezone.utc)
                },
                'logon': True
            }
        }, {
            '$group': {
                '_id': '$uid'
            }
        }, {
            '$project': {
                'uid': '$_id'
            }
        }, {
            '$count': 'uid'
        }
    ]
    active_user = list(active_items.aggregate(pipeline))[0]['uid']
    total_user = get_count_register_users(conn_user_cursor)
    yesterday_day_registered_users = get_count_register_users_yesterday(conn_user_cursor, yesterday_day)
    print(active_user)
    text = "### 用户运营统计V3 \n\n 当前用户注册总数:1000000 \n\n ##### 新增用户情况：\n\n > - 新增: 1000人，下单: 100人，消费额: 9999元 \n\n > - 订单数: 19单，人均消费: 333元 \n\n ##### 跨境: \n\n > - 新增跨境: 2人 \n\n > - 消费额: 1555元，订单数: 2单 \n\n ##### 非当日注册新下单用户:\n\n >- 1人 \n\n > - 消费额: 110元，人均消费: 100元 \n\n ##### 昨日日活: 18000人 \n\n ##### 昨日下单前3名用户:\n\n >- 111123(订单数:8) 323232(订单数:10) \n\n ##### 昨日下单用户中，为今年第一次购买的占比：9%"
