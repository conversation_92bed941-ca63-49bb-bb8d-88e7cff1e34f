from sqlalchemy import create_engine, MetaData, Table, Column, String, insert


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database))
    return conn_engine

def conn_str(database):
    conn_str = f"""mysql+pymysql://wy_ggh:<EMAIL>/{database}"""
    return conn_str


def batch_insert_data(database, table, data_list, batch_size=500):
    engine = nopool_conn(database)
    connection = engine.connect()

    try:
        metadata = MetaData(bind=engine)
        table_obj = Table(table, metadata, autoload=True)

        # 将 data_list 按照 batch_size 拆分成多个批次
        for i in range(0, len(data_list), batch_size):
            batch_data = data_list[i:i + batch_size]

            # 使用 insert 语句的 values 方法，传入一个字典列表
            stmt = insert(table_obj).values(batch_data)

            # 执行批量插入
            connection.execute(stmt)

            print(f"成功插入第 {i // batch_size + 1} 批数据")

        print("数据批量插入成功")
    except Exception as e:
        print(f"插入数据时发生错误: {e}")
    finally:
        connection.close()

def insert_data(database, table, data):
    engine = nopool_conn(database)
    connection = engine.connect()

    try:
        metadata = MetaData(bind=engine)
        table_obj = Table(table, metadata, autoload=True)

        # 使用 insert 语句的 values 方法，传入一个字典
        stmt = insert(table_obj).values(data)

        # 执行单条插入
        connection.execute(stmt)

        print("成功插入数据")

    except Exception as e:
        print(f"插入数据时发生错误: {e}")
    finally:
        connection.close()

def query_data(database, sql):
    engine = nopool_conn(database)
    connection = engine.connect()

    try:
        # 执行 SQL 查询
        result = connection.execute(sql)

        # 获取多条数据
        rows = result.fetchall()

        return rows
    except Exception as e:
        print(f"查询数据时发生错误: {e}")
    finally:
        connection.close()

def one_query_data(database, sql):
    engine = nopool_conn(database)
    connection = engine.connect()

    try:
        # 执行 SQL 查询
        result = connection.execute(sql)

        # 获取一条数据
        row = result.fetchone()

        # row 是一个包含查询结果的字典
        return row
    except Exception as e:
        print(f"查询数据时发生错误: {e}")
    finally:
        connection.close()