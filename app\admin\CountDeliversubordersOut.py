import pandas as pd
import json
import warnings

warnings.filterwarnings("ignore")



def DeliversubordersOut(subordernodata,conn_statics):#订单号一个或者多个

    """
    验证订单号是否导出过

    """

    try:
        suborderno=eval(subordernodata)
        suborder_sql="select `sub_order_no` from `vh_out_deliversuborders_count`"
        deliver_out_suborderno= pd.read_sql(suborder_sql, conn_statics)
        suborderno_list=deliver_out_suborderno.sub_order_no.tolist()
        subout_list=[]
        outstatus_list=[]
        out_dff=pd.DataFrame([])
        for subout in suborderno:
            if subout in suborderno_list:
                outstatus=1
            elif subout not in suborderno_list:
                outstatus=0
            else:
                pass
            subout_list.append(subout)
            outstatus_list.append(outstatus)
        suboutseries=pd.Series(subout_list)
        outstatusseries=pd.Series(outstatus_list)
        out_dff["suborderno"]=suboutseries
        out_dff["outstatus"]=outstatusseries
        out_df=out_dff.T
        out_df.reset_index(inplace=True,drop=True)
        out_df.columns=out_df.loc[0].values.tolist()
        out_df.drop(index=0,inplace=True)
        out_json={
            "error_code": 0,
            "error_msg": ""
            } 
        json_data_list=[]
        for out_column in out_df.columns:
            json_dict={out_column:out_df[out_column].values.tolist()[0]}
            json_data_list.append(json_dict)
            out_json["data"]=json_data_list
        return out_json

        
    except Exception as e:
        return {"error_code":"获取订单导出标识失败","error_msg": f"{e}"}