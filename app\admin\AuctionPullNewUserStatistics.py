import json
import warnings
import numpy as np
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str


warnings.filterwarnings("ignore")


def replace_func(dff,original_column_name,replaced_column_name):
    for index, row in dff.iterrows():
        if pd.notna(row[replaced_column_name]):
            dff.at[index, original_column_name] = row[replaced_column_name]
    return dff


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def date_to_timestamp(start_date,end_date):
    start_timestamp,end_timestamp = int(datetime.strptime(f"{start_date} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"{end_date} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
    return start_timestamp,end_timestamp


def get_auction_order_massage(recorder_uids,goods_ids):
    auction_conn = nopool_conn("vh_auction").connect()
    if len(goods_ids) == 1 and len(recorder_uids) == 1:
        auction_order_sql = f""" select goods_id,uid,'是' AS is_hit from vh_orders where uid = {recorder_uids[0]} and goods_id = {goods_ids[0]}"""
    elif len(goods_ids) > 1 and len(recorder_uids) > 1:
        auction_order_sql = f""" select goods_id,uid,'是' AS is_hit from vh_orders where uid in {recorder_uids} and goods_id in {goods_ids}"""
    else:
        return pd.DataFrame(columns=["goods_id","uid","is_hit"])
    auction_order_dff = pd.read_sql_query(auction_order_sql,auction_conn)
    auction_order_dff["goods_id"] = auction_order_dff.goods_id.astype(str)
    auction_conn.close()
    nopool_conn("vh_auction").dispose()
    return auction_order_dff


def get_auction_user_massage(start_timestamp,end_timestamp):
    """
    获取首次参拍用户
    :param now:
    :return:
    """
    try:
        user_conn = nopool_conn("vh_user").connect()
        auction_conn = nopool_conn("vh_auction").connect()
        user_sql = f""" select uid,"注册" AS 'type' from vh_user where created_time between {start_timestamp} and {end_timestamp} and tripartite_source = 'vinehoo-auction_moments' """
        record_sql = f""" select uid,"首次出价" AS 'type',created_time from vh_goods_bid_record where is_vest = 0 group by `uid` having `created_time` between {start_timestamp} and {end_timestamp}"""
        record_dff = pd.read_sql_query(record_sql,auction_conn)
        user_dff = pd.read_sql_query(user_sql,user_conn)
        out_dff = record_dff[["uid","type"]].append(user_dff).reset_index(drop=True)
        uids = tuple(set(out_dff.uid.tolist()))
        if len(uids) == 0:
            return pd.DataFrame(columns=["uid","type","user_created_time","pull_new_user_time","goods_id"])
        elif len(uids) == 1:
            created_time_sql = f""" select uid,nickname,from_unixtime(created_time) AS 'user_created_time' from vh_user where uid = {uids[0]}"""
        else:
            created_time_sql = f""" select uid,nickname,from_unixtime(created_time) AS 'user_created_time' from vh_user where uid in {uids}"""
        user_created_time_dff = pd.read_sql_query(created_time_sql,user_conn)
        if len(uids) == 1:
            first_record_time_sql = f""" select uid,from_unixtime(min(created_time)) AS 'first_record_time',created_time,goods_id from vh_goods_bid_record  where uid = {uids[0]} and is_vest = 0 group by `uid` """
        else:
            first_record_time_sql = f""" select uid,from_unixtime(min(created_time)) AS 'first_record_time',created_time,goods_id from vh_goods_bid_record  where uid in {uids} and is_vest = 0 group by `uid` """
        first_record_time_dff = pd.read_sql_query(first_record_time_sql,auction_conn)
        first_record_time_dff["goods_id"] = first_record_time_dff.goods_id.astype(int).astype(str)
        first_record_time_dff = first_record_time_dff[["uid","first_record_time","goods_id"]]
        out_dff = pd.merge(out_dff,user_created_time_dff,how="left",on="uid").merge(first_record_time_dff,how="left",on="uid")
        user_conn.close()
        auction_conn.close()
        nopool_conn("vh_user").dispose()
        nopool_conn("vh_auction").dispose()
        return out_dff
    except Exception  as e:
        print(f"获取首次参拍用户获取失败:{e}")
        return -1
    

def get_goods_massage(goods_ids):
    auction_conn = nopool_conn("vh_auction").connect()
    if len(goods_ids) == 0:
        return pd.DataFrame(columns=["goods_id","category","quote"])
    elif len(goods_ids) == 1:
        auction_goods_sql = f""" select id AS 'goods_id',case category when 0 then '白酒' when 1 then '葡萄酒' when 2 then '烈酒' end as 'category',quote from vh_goods where id = {goods_ids[0]}"""
    else:
        auction_goods_sql = f""" select id AS 'goods_id',case category when 0 then '白酒' when 1 then '葡萄酒' when 2 then '烈酒' end as 'category',quote from vh_goods where id in {goods_ids}"""
    auction_goods_dff = pd.read_sql_query(auction_goods_sql,auction_conn)
    auction_goods_dff["goods_id"] = auction_goods_dff.goods_id.astype(str)
    auction_conn.close()
    nopool_conn("vh_auction").dispose()
    return auction_goods_dff


def get_graphical(df):
    new_user_type_dff = df.groupby(by="type",as_index=False).agg({"uid":"count"}).rename(columns = {"uid":"user_counts"})
    transaction_dff = df[df.type == "首次出价"]
    goods_statistics_dff = transaction_dff.groupby(by="category",as_index=False).agg({"uid":"count"}).rename(columns = {"uid":"user_counts"})
    price_title = []
    for price in transaction_dff.quote:
        if price<100:
            title = "100元以内"
        elif price>=100 and price<300:
            title = "100元~300元"
        elif price>=300 and price<500:
            title = "300元~500元"
        elif price>=500 and price<1000:
            title = "500元~1000元"
        elif price>=1000:
            title = "1000元以上"
        else:
            title = "暂无"
        price_title.append(title)
    transaction_dff["quote"] = price_title
    quote_statistics_dff = transaction_dff.groupby(by="quote",as_index=False).agg({"uid":"count"}).rename(columns = {"uid":"user_counts"})
    res_json = {"new_user_type_statistics":json.loads(new_user_type_dff.to_json(orient="records")),"goods_statistics":json.loads(goods_statistics_dff.to_json(orient="records")),"quote_statistics":json.loads(quote_statistics_dff.to_json(orient="records"))}
    return res_json


def get_main_massage(out_type,start_date,end_date,page,page_nums):
    start_timestamp,end_timestamp = date_to_timestamp(start_date,end_date)
    auction_user_massage = get_auction_user_massage(start_timestamp,end_timestamp)
    goods_ids = tuple(set([each for each in auction_user_massage.goods_id.tolist() if str(each) != "nan"]))
    uids = tuple(set(auction_user_massage.uid.tolist()))
    goods_massage = get_goods_massage(goods_ids)
    auction_order_massage = get_auction_order_massage(uids,goods_ids)
    main_dff = pd.merge(auction_user_massage,goods_massage,how="left",on="goods_id").merge(auction_order_massage,how="left",on=["uid","goods_id"])
    main_dff["is_hit"] = main_dff.type.apply(lambda x:"否" if x == "首次出价" else np.nan)
    if main_dff.empty:
        list_json = {}
        total = len(list_json)
        graphical_json = {"new_user_type_statistics":[],"goods_statistics":[],"quote_statistics":[]}
        res_json = {"list_json":{"list":list_json,"total":total},"graphical_json":graphical_json}
    else:
        main_dff["user_created_time"] = main_dff.user_created_time.astype(str)
        main_dff["first_record_time"] = main_dff.first_record_time.astype(str)
        if out_type == "首次出价":
            main_dff = main_dff[main_dff.type == "首次出价"]
        elif out_type == "注册":
            main_dff = main_dff[main_dff.type == "注册"]
        else:
            pass
        graphical_json = get_graphical(main_dff)
        total = len(main_dff)
        main_dff = main_dff[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
        main_dff = main_dff[["uid","nickname","type","user_created_time","first_record_time","goods_id","is_hit"]]
        res_json = {"list_json":{"list":json.loads(main_dff.to_json(orient="records")),"total":total},"graphical_json":graphical_json}
    return res_json