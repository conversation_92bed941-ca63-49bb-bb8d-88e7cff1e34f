# -*- coding: utf-8 -*-
# 未发货订单统计
import json
import time
import os.path
import requests
import warnings
import concurrent
import pandas as pd
from sqlalchemy import create_engine
from config import ChannelTypeConfig
from app.DButils.MysqlHelper import conn_str
from app.CommonLibraries.GetNowTime import time_str_to_timestamp, get_now_time

warnings.filterwarnings("ignore")


def nopool_conn(database: str, database_type: str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database, database_type))
    return conn_engine


def split_list_into_groups(lst, group_size):
    """
    列表切割
    :param lst:主列表名
    :param group_size:每份个数
    :return:
    """
    return [lst[i:i+group_size] for i in range(0, len(lst), group_size)]


def escape_sql_title(title):
    '''
    # 使用单引号将 title 包裹起来，并将内部的单引号替换为两个单引号，实现 SQL 转义
    '''
    escaped_title = title.replace("'", "''")
    return escaped_title


def get_dff_orders(period_type, is_overdue, is_stage, push_wms_status, estimate_delivery_time, conn_orders, conn_goods, is_push_warehouse_24h):
    delivery_time_start = estimate_delivery_time.split(" ")[
        0] + " " + "00:00:00"
    delivery_time_over = estimate_delivery_time.split(" ")[
        0] + " " + "23:59:59"
    period_type_dict = {1: "vh_flash_order", 2: "vh_cross_order",
                        3: "vh_second_order", 4: "vh_tail_order"}
    
    period_type_map = {1: 0, 2: 2, 3: 1, 4: 3}
    #真实频道
    periods_type = period_type_map[period_type]
    
    order_where = ""
    #未发货订单选择逾期时，不计算【顺手购】期数
    if is_overdue == 1:
        sql = f"""SELECT period FROM `vh_periods_add_purchase` where periods_type = {periods_type}"""
        df_add_purchase = pd.read_sql(sql, conn_goods)
        period_list = tuple(df_add_purchase.period.tolist())
        if len(period_list) > 0:
            if len(period_list) == 1:
                order_where = f""" and period != {period_list[0]}"""
            else:
                order_where = f""" and period not in {period_list}"""

    #推送仓库时间大于24小时
    if is_push_warehouse_24h == 1:
        #获取24小时前时间戳
        delivery_time_start = int(time.time()) - 86400
        order_where = f"""{order_where} and (SELECT id FROM `vh_order_remarks` WHERE `sub_order_no` = o.sub_order_no and remarks = '推送萌牙成功' and created_time >= {delivery_time_start} limit 1) is not null"""


    if period_type == 2:
        order_table = period_type_dict[period_type]
        dff_orders = pd.DataFrame(
            columns=['period', 'package_id', 'order_qty', 'predict_time'])
        sql_orders = f"""SELECT main_order_id,sub_order_no,period,package_id,order_qty,predict_time FROM `{order_table}` as o WHERE sub_order_status = 1 and is_delete = 0 {order_where}"""
        if is_stage != 0:
            if is_stage == 1:
                sql_orders = f""" {sql_orders} AND is_ts = 1"""
            else:
                sql_orders = f""" {sql_orders} AND is_ts = 0"""
        else:
            sql_orders = f""" {sql_orders} """
        # 预计发货时间区间查询
        if len(estimate_delivery_time) != 0:
            if estimate_delivery_time[0] != "" and estimate_delivery_time[1] != "":
                sql_orders = f""" {sql_orders} AND predict_time 
                BETWEEN {time_str_to_timestamp(delivery_time_start)} AND {time_str_to_timestamp(delivery_time_over)} """
            else:
                sql_orders = f""" {sql_orders} """
        else:
            sql_orders = f""" {sql_orders} """
        now_timestamp = get_now_time("timestamp")
        df_orders = pd.read_sql(sql_orders, conn_orders)  # 0.2s
        df_orders['is_overdue'] = df_orders['predict_time'].apply(
            lambda x: 1 if x < now_timestamp else 0)
        if is_overdue != 0:
            if is_overdue == 1:
                df_orders = df_orders[df_orders['is_overdue'] == 1]
            else:
                df_orders = df_orders[df_orders['is_overdue'] == 0]
        else:
            df_orders = df_orders.copy()
        if df_orders.empty:
            return dff_orders
        dff_sub_orders = dff_orders.append(df_orders).reset_index(drop=True)
        main_no_list = tuple(dff_sub_orders.main_order_id.tolist())
        if len(main_no_list) == 1:
            main_no_sql = f""" SELECT id,main_order_no FROM vh_order_main WHERE id = {main_no_list[0]}"""
        else:
            main_no_sql = f""" SELECT id,main_order_no FROM vh_order_main WHERE id in {main_no_list}"""
        main_no_df = pd.read_sql_query(main_no_sql, conn_orders)
        dff_orders = pd.merge(main_no_df, dff_sub_orders,
                              how="right", left_on="id", right_on="main_order_id")
    else:
        order_table = period_type_dict[period_type]
        #未发货订单不要计算订单定单
        if period_type == 1:
            order_where = f"""{order_where} and special_type <> 4"""

        dff_orders = pd.DataFrame(
            columns=['period', 'package_id', 'order_qty', 'predict_time'])
        sql_orders = f"""SELECT main_order_id,sub_order_no,period,package_id,order_qty,predict_time FROM {order_table} as o WHERE sub_order_status = 1 and is_delete = 0 {order_where}"""
        if is_stage != 0:
            if is_stage == 1:
                sql_orders = f""" {sql_orders} AND is_ts = 1"""
            else:
                sql_orders = f""" {sql_orders} AND is_ts = 0"""
        else:
            sql_orders = f""" {sql_orders} """
        if len(estimate_delivery_time) != 0:
            if estimate_delivery_time[0] != "" and estimate_delivery_time[1] != "":
                sql_orders = f""" {sql_orders} AND predict_time 
                BETWEEN {time_str_to_timestamp(delivery_time_start)} AND {time_str_to_timestamp(delivery_time_over)} """
            else:
                sql_orders = f""" {sql_orders} """
        else:
            sql_orders = f""" {sql_orders} """
        # 查询是否推送萌芽
        if push_wms_status != '':
            sql_orders = f""" {sql_orders} AND push_wms_status = {push_wms_status} """
        now_timestamp = get_now_time("timestamp")

        df_orders = pd.read_sql(sql_orders, conn_orders)  # 0.2s
        df_orders['is_overdue'] = df_orders['predict_time'].apply(
            lambda x: 1 if x < now_timestamp else 0)
        if is_overdue != 0:
            if is_overdue == 1:
                df_orders = df_orders[df_orders['is_overdue'] == 1]
            else:
                df_orders = df_orders[df_orders['is_overdue'] == 0]
        else:
            df_orders = df_orders.copy()
        if df_orders.empty:
            pass
        if order_table == "vh_flash_order":
            df_orders['period_type'] = 1
        elif order_table == "vh_second_order":
            df_orders['period_type'] = 3
        elif order_table == "vh_tail_order":
            df_orders['period_type'] = 4
        dff_sub_orders = dff_orders.append(df_orders).reset_index(drop=True)
        if dff_sub_orders.empty:
            return dff_sub_orders
        main_no_list = tuple(dff_sub_orders.main_order_id.tolist())
        if len(main_no_list) == 1:
            main_no_sql = f""" SELECT id,main_order_no FROM vh_order_main WHERE id = {main_no_list[0]}"""
        else:
            main_no_sql = f""" SELECT id,main_order_no FROM vh_order_main WHERE id in {main_no_list}"""
        main_no_df = pd.read_sql_query(main_no_sql, conn_orders)
        dff_orders = pd.merge(main_no_df, dff_sub_orders,
                              how="right", left_on="id", right_on="main_order_id")
    return dff_orders


def get_bottle_nums(df_bottle_info, conn_orders):
    package_ids = tuple(set(df_bottle_info.package_id.tolist()))
    main_order_nos = tuple(set(df_bottle_info.main_order_no.tolist()))
    if len(package_ids) == 0 and len(main_order_nos) == 0:
        return pd.DataFrame(columns=["sub_order_no", "main_order_no", "package_id", 'bottle_nums'])
    if len(main_order_nos) == 1:
        if len(package_ids) == 1:
            mh_sql = f""" select main_order_no,package_id,product_info from vh_order_mystery_box_log where package_id = {package_ids[0]} and main_order_no = '{main_order_nos[0]}'"""
        else:
            mh_sql = f""" select main_order_no,package_id,product_info from vh_order_mystery_box_log where package_id in {package_ids} and main_order_no = '{main_order_nos[0]}'"""
    else:
        if len(package_ids) == 1:
            mh_sql = f""" select main_order_no,package_id,product_info from vh_order_mystery_box_log where package_id = {package_ids[0]} and main_order_no in {main_order_nos}"""
        else:
            mh_sql = f""" select main_order_no,package_id,product_info from vh_order_mystery_box_log where package_id in {package_ids} and main_order_no in {main_order_nos}"""
    mh_dff = pd.read_sql_query(mh_sql, conn_orders)
    df_bottle_info = pd.merge(df_bottle_info, mh_dff, how="left", on=[
                              "package_id", "main_order_no"])
    for index, row in df_bottle_info.iterrows():
        if pd.notna(row['product_info']):
            df_bottle_info.at[index,
                              'associated_products'] = row['product_info']
    df_bottle_info = df_bottle_info[df_bottle_info.associated_products.apply(
        lambda x:x != '[]' and x != None and type(x) != float)]
    df_bottle_info["associated_products"] = df_bottle_info.associated_products.apply(
        lambda x: eval(x))
    is_any = []
    for is_a in df_bottle_info.associated_products:
        if len(is_a) == 1:
            a = 0
        elif len(is_a) > 1:
            a = 1
        is_any.append(a)
    df_bottle_info["is_any"] = is_any
    main_data_any = df_bottle_info[df_bottle_info["is_any"] == 1]
    main_data_one = df_bottle_info[df_bottle_info["is_any"] == 0]
    if main_data_any.empty:
        dff_main_data = main_data_one
        dff_main_data["product_id"] = dff_main_data.associated_products.apply(
            lambda x: x[0]["product_id"])
        dff_main_data["bottle_nums"] = dff_main_data.associated_products.apply(
            lambda x: x[0]["nums"])
    else:
        main_data_one["product_id"] = main_data_one.associated_products.apply(
            lambda x: x[0]["product_id"])
        main_data_one["bottle_nums"] = main_data_one.associated_products.apply(
            lambda x: x[0]["nums"])
        main_data_any = main_data_any.explode(
            'associated_products').reset_index(drop=True)
        main_data_any["mark"] = main_data_any.associated_products.apply(
            lambda x: json.dumps(x))
        main_data_any = main_data_any.drop_duplicates(
            subset=["sub_order_no", "mark"], keep="first")
        main_data_any["product_id"] = main_data_any.associated_products.apply(
            lambda x: x["product_id"])
        main_data_any["bottle_nums"] = main_data_any.associated_products.apply(
            lambda x: x["nums"])
        dff_main_data = main_data_one.append(
            main_data_any).reset_index(drop=True)
    product_id = tuple(dff_main_data.product_id.tolist())
    if len(product_id) == 1:
        in_sql = f"select id as 'product_id',bar_code,short_code from vh_products where id = {product_id[0]}"
        wiki_conn = nopool_conn("vh_wiki", "channel").connect()
        in_df = pd.read_sql_query(in_sql, wiki_conn)
        wiki_conn.close()
        nopool_conn("vh_wiki", "channel").dispose()
    else:
        in_sql = f"select id as 'product_id',bar_code,short_code  from vh_products  where id in {product_id}"
        wiki_conn = nopool_conn("vh_wiki", "channel").connect()
        in_df = pd.read_sql_query(in_sql, wiki_conn)
        wiki_conn.close()
        nopool_conn("vh_wiki", "channel").dispose()
    dff_main_data = pd.merge(dff_main_data, in_df, how="left", on="product_id")
    if main_data_any.empty:
        dff_main_data.drop(
            ["is_any", "product_info", "associated_products"], axis=1, inplace=True)
    else:
        dff_main_data.drop(["is_any", "mark", "product_info",
                           "associated_products"], axis=1, inplace=True)
    return dff_main_data


def get_wms_inventory(bar_codes):
    """
    根据条码获取萌芽库存
    :param bar_code_list:
    :return:
    """
    engine_wms = nopool_conn("wms_stock_1", "wms")
    conn_wms = engine_wms.connect()
    if len(bar_codes) == 1 and bar_codes[0] == "":
        wms_df = pd.DataFrame(columns=['bar_code', 'wms_inventory'])
        return wms_df
    
    if len(bar_codes) == 1:
        wms_sql = f""" select a.bar_code,concat(aa.fictitious_name,":",a.goods_count) as 'wms_inventory' from wms_fictitious_goods AS a left join wms_fictitious AS aa on a.fictitious_id = aa.fictitious_id where a.bar_code = {bar_codes[0]} """
    else:
        wms_sql = f""" select a.bar_code,concat(aa.fictitious_name,":",a.goods_count) as 'wms_inventory' from wms_fictitious_goods AS a left join wms_fictitious AS aa on a.fictitious_id = aa.fictitious_id where a.bar_code in {bar_codes} """
    wms_df = pd.read_sql_query(wms_sql, conn_wms)
    conn_wms.close()
    engine_wms.dispose()
    return wms_df

def get_cross_inventory(bar_codes):
    """
    根据条码获取萌芽库存
    :param bar_code_list:
    :return:
    """
    engine_orders = nopool_conn("vh_orders", "channel")
    conn = engine_orders.connect()

    if len(bar_codes) == 1 and bar_codes[0] == "":
        df = pd.DataFrame(columns=['bar_code', 'available_nums', 'real_nums'])
        return df
    
    if len(bar_codes) == 1:
        sql = f""" select goods_barcode as bar_code,sum(available_nums) as available_nums,sum(real_nums) as real_nums from vh_cross_inventory where goods_barcode = {bar_codes[0]} """
    else:
        sql = f""" select goods_barcode as bar_code,sum(available_nums) as available_nums,sum(real_nums) as real_nums from vh_cross_inventory where goods_barcode in {bar_codes} group by goods_barcode"""

    df = pd.read_sql_query(sql, conn)
    conn.close()
    engine_orders.dispose()
    return df
    


def get_u_plus_inventory(short_code_json, erp_type):
    #     """
    #     根据简码获取ERP库存信息
    #     :param short_code:
    #     :return:
    #     """
    if erp_type == "U8C":
        url = "https://callback.vinehoo.com/erp/erp/v3/ic/getStockInfo"
        # url = "https://test-wine.wineyun.com/erp/erp/v3/ic/getStockInfo"
    elif erp_type == "T+":
        url = "https://callback.vinehoo.com/push-t-plus/pushtplus/v3/ic/getStockInfo"
        # url = "https://test-wine.wineyun.com/push-t-plus/pushtplus/v3/ic/getStockInfo"
    else:
        return pd.DataFrame(columns=["short_code", "atp_number", "current_number"])
    if len(short_code_json["short_code"]) == 0:
        return pd.DataFrame(columns=["short_code", "atp_number", "current_number"])
    else:
        payload = short_code_json
        headers = {
            'content-type': "application/json",
            'cache-control': "no-cache"
        }
        response = requests.request(
            "POST", url, data=json.dumps(payload), headers=headers)
        json_str = response.json()
        if json_str == []:
            result = pd.DataFrame(
                columns=["short_code", "atp_number", "current_number"])
        else:
            data = json_str.get("data")
            if len(data) == 0:
                return pd.DataFrame(columns=["short_code", "atp_number", "current_number"])
            elif len(data) == 1:
                code_keys = list(data.keys())[0]
                code_data = data.get(code_keys)
                short_code = short_code_json["short_code"][0]
                atp_number = code_data.get("atp_number", 0)
                current_number = code_data.get("current_number", 0)
                result = pd.DataFrame({"short_code": [code_keys], "atp_number": [
                                      atp_number], "current_number": [current_number]})
            else:
                short_codes = []
                atp_number_list = []
                current_number_list = []
                for short_code in short_code_json["short_code"]:
                    main_numbers = data.get(
                        short_code, {"atp_number": 0, "current_number": 0})
                    atp_number = main_numbers.get("atp_number", 0)
                    current_number = main_numbers.get("current_number", 0)
                    short_codes.append(short_code)
                    atp_number_list.append(atp_number)
                    current_number_list.append(current_number)
                result = pd.DataFrame(
                    {"short_code": short_codes, "atp_number": atp_number_list, "current_number": current_number_list})
        return result


def get_paymain(order_no_json_list):
    url = "https://callback.vinehoo.com/orders/orders/v3/salesreturn/getcorpbyorderno"
    payload = order_no_json_list
    headers = {
        'content-type': "application/json",
        'cache-control': "no-cache"
    }
    response = requests.request(
        "POST", url, data=json.dumps(payload), headers=headers)
    json_dict = response.json()
    data = json_dict["data"]
    sub_order_no = []
    corp = []
    for d in data:
        sub_order_no.append(d["sub_order_no"])
        corp.append(d["corp"])
    return pd.DataFrame({"sub_order_no": sub_order_no, "corp": corp})


def get_package_sales_info(periods, commodities_conn, orders_conn, period_type):
    """
    获取期数获取对应的套餐销售数据
    :param periods:
    :param commodities_conn:
    :param orders_conn:
    :param period_type:销售类型 1-闪购，2-跨境，3-秒发，4-尾货，5-兔头
    :return:
    """
    period_type = int(period_type)
    if len(periods) == 1:
        if period_type == 1:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_flash_set WHERE period_id = {periods[0]}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_flash_order WHERE period = {periods[0]} AND sub_order_status =1"""
        elif period_type == 2:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_cross_set WHERE period_id = {periods[0]}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_cross_order WHERE period = {periods[0]} AND sub_order_status =1"""
        elif period_type == 3:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_second_set WHERE period_id = {periods[0]}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_second_order WHERE period = {periods[0]} AND sub_order_status =1"""
        elif period_type == 4:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_leftover_set WHERE period_id = {periods[0]}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_tail_order WHERE period = {periods[0]} AND sub_order_status =1"""
        elif period_type == 5:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_rabbit_set WHERE period_id = {periods[0]}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_rabbit_order WHERE period = {periods[0]} AND sub_order_status =1"""
    else:
        if period_type == 1:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_flash_set WHERE period_id in {periods}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_flash_order WHERE period in {periods} AND sub_order_status =1"""
        elif period_type == 2:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_cross_set WHERE period_id in {periods}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_cross_order WHERE period in {periods} AND sub_order_status =1"""
        elif period_type == 3:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_second_set WHERE period_id in {periods}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_second_order WHERE period in {periods} AND sub_order_status =1"""
        elif period_type == 4:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_leftover_set WHERE period_id in {periods}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_tail_order WHERE period in {periods} AND sub_order_status =1"""
        elif period_type == 5:
            sql_set = f"""SELECT period_id,id 'package_id',package_name FROM vh_periods_rabbit_set WHERE period_id in {periods}"""
            sql_order = f"""SELECT package_id,order_qty FROM vh_rabbit_order WHERE period in {periods} AND sub_order_status =1"""
    df1 = pd.read_sql(sql_set, commodities_conn)
    df2 = pd.read_sql(sql_order, orders_conn)
    dff = pd.merge(df1, df2, on='package_id')
    dff = dff.drop('package_id', axis=1)
    dff_end = dff.groupby(['period_id', 'package_name']).sum().reset_index()
    return dff_end


def deal_package_info(dff, periods, gevent_name):
    """
    匹配套餐信息
    :return:
    """
    try:
        package_sales_json = {}
        for period in periods:
            dff_end = dff[dff['period_id'] == period]
            package_sales = []
            for per_value in dff_end.values.tolist():
                package_sales.append(f"{per_value[1]}: {per_value[2]}")
            package_sales_json[period] = package_sales
        return package_sales_json
    except Exception as e:
        print(e)


def deal_package_info_2(dff):
    """
    匹配套餐信息
    :return:
    """
    try:
        # 方案2，效率不如方案一
        package_sales = []
        for per_value in dff.values.tolist():
            package_sales.append(f"{per_value[1]}: {per_value[2]}")
        return package_sales
    except Exception as e:
        print(e)


def get_undelivered_statistics_gevent(args, conn_goods, conn_orders, df_orders,
                                      commodities_table, order_table, gevent_name, df_goods):
    """
    协程协助处理闪购，秒发，尾货数据
    :return:
    """
    # try:
    dff_re_end = pd.DataFrame(
        columns=['yq_orders', 'wyq_orders', 'period', 'title', 'supplier', 'onsale_time', 'sold_out_time',
                 'predict_shipment_time', 'available_number', 'exist_number', 'wms_inventory', 'set_sales_info',
                 'period_type'])
    period_type = int(args['period_type'])
    is_export = args['is_export']
    if df_goods.empty:
        return dff_re_end
    df_goods['onsale_time'] = df_goods['onsale_time'].astype('str')
    df_goods['sold_out_time'] = df_goods['sold_out_time'].astype('str')
    package_ids = tuple(list(set(df_orders['package_id'].tolist())))
    if len(package_ids) != 1:
        sql_bottle = f"""SELECT a.sub_order_no,a.period,b.main_order_no,aa.id 'package_id',aa.associated_products FROM {commodities_table}_set AS aa right join vh_orders.{order_table} AS a on aa.id = a.package_id left join vh_orders.vh_order_main AS b on a.main_order_id = b.id WHERE aa.id in {package_ids}"""
    else:
        sql_bottle = f"""SELECT a.sub_order_no,a.period,b.main_order_no,aa.id 'package_id',aa.associated_products FROM {commodities_table}_set AS aa right join vh_orders.{order_table} AS a on aa.id = a.package_id left join vh_orders.vh_order_main AS b on a.main_order_id = b.id WHERE aa.id = {package_ids[0]}"""
    df_bottle_info = pd.read_sql(sql_bottle, conn_goods)  # 0.1s
    df_bottle_info = get_bottle_nums(df_bottle_info, conn_orders)
    df_bottle_info = df_bottle_info[[
        'sub_order_no', 'package_id', 'bar_code', 'short_code', 'bottle_nums']]
    df_orders = pd.merge(df_orders, df_bottle_info, how='left', on=[
                         'sub_order_no', 'package_id']).fillna(0)
    df_orders.loc[df_orders.duplicated(
        subset='sub_order_no', keep='first'), 'sub_order_no'] = None
    df_orders['bottle_nums'] = df_orders['bottle_nums'].astype('int')
    df_orders['sales_nums'] = df_orders['bottle_nums'] * df_orders['order_qty']
    df_orders = df_orders.drop('package_id', axis=1).drop(
        'order_qty', axis=1).drop('bottle_nums', axis=1)
    # 未发货订单数
    # 逾期订单
    df_yq_orders = df_orders[df_orders['is_overdue'] == 1]
    dff_yq_orders = pd.merge(df_yq_orders, df_goods)
    # 逾期订单数
    if period_type == 2 and is_export == 1:
        dff_yq_orders1 = dff_yq_orders[['sub_order_no']].groupby('sub_order_no').size().reset_index().rename(
            columns={0: 'yq_orders'})
        dff_yq_orders2 = dff_yq_orders[['sub_order_no', 'sales_nums']].groupby('sub_order_no').sum().reset_index().rename(
            columns={'sales_nums': 'yq_sale_nums'})
    else:
        dff_yq_orders1 = dff_yq_orders.groupby('period', as_index=False).agg({"sub_order_no": "count"}).rename(
            columns={"sub_order_no": 'yq_orders'})
        dff_yq_orders2 = dff_yq_orders[['period', 'sales_nums']].groupby('period').sum().reset_index().rename(
            columns={'sales_nums': 'yq_sale_nums'})
    dff_yq_orders = pd.merge(dff_yq_orders1, dff_yq_orders2)
    # 未逾期订单
    df_wyq_orders = df_orders[df_orders['is_overdue'] == 0]
    dff_wyq_orders = pd.merge(df_wyq_orders, df_goods)
    if period_type == 2 and is_export == 1:
        dff_wyq_orders1 = dff_wyq_orders[['sub_order_no']].groupby('sub_order_no').size().reset_index().rename(
            columns={0: 'wyq_orders'})
        dff_wyq_orders2 = dff_wyq_orders[['sub_order_no', 'sales_nums']].groupby('sub_order_no').sum().reset_index().rename(
            columns={'sales_nums': 'wyq_sale_nums'})
    else:
        dff_wyq_orders1 = dff_wyq_orders.groupby('period', as_index=False).agg({"sub_order_no": "count"}).rename(
            columns={"sub_order_no": 'wyq_orders'})
        dff_wyq_orders2 = dff_wyq_orders[['period', 'sales_nums']].groupby('period').sum().reset_index().rename(
            columns={'sales_nums': 'wyq_sale_nums'})
    dff_wyq_orders = pd.merge(dff_wyq_orders1, dff_wyq_orders2)
    dff_end = dff_yq_orders.append(dff_wyq_orders).fillna(0)
    dff_end = pd.merge(dff_end, df_orders)
    dff_re = pd.merge(dff_end, df_goods, how='left')
    package_periods = tuple(list(set(dff_re['period'].tolist())))
    dff_package_sales_info = get_package_sales_info(
        package_periods, conn_goods, conn_orders, period_type)
    dff_re['set_sales_info'] = dff_re['period'].apply(
        lambda x: deal_package_info_2(dff_package_sales_info[dff_package_sales_info['period_id'] == x]))  # 1.3s
    dff_re['predict_time'] = dff_re['predict_time'].apply(
        lambda x: time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(x))))
    dff_re['period_type'] = period_type
    if period_type == 2:
        out_re = dff_re.drop_duplicates(subset='sub_order_no', keep='first')
        dff_re = dff_re.drop_duplicates(subset='period', keep='first')
        return dff_re, out_re
    else:
        dff_re = dff_re.drop_duplicates(subset='period', keep='first')
        return dff_re
    # except Exception as e:
    #     record_error("get_undelivered_statistics_gevent", f"协程执行失败,失败信息----{e}")


def get_undelivered_statistics(args):
    """
    获取未发货订单信息
    :param args:
    :param commodities_pool:
    :param orders_pool:
    :return:
    """
    start_time = time.time()
    period_type = int(args['period_type'])
    page = args['page']
    page_nums = args['page_nums']
    estimate_delivery_time = args['estimate_delivery_time']
    is_overdue = args['is_overdue']
    is_stage = args['is_stage']
    period = args['period']
    period_name = args['period_name']
    stime = args['stime']
    etime = args['etime']
    supplier_name = args['supplier_name']
    is_export = args['is_export']
    push_wms_status = args['push_wms_status']
    tu = args['dingtalk-uid']
    import_type = args['import_type']
    is_supplier_delivery = args['is_supplier_delivery']
    is_push_warehouse_24h = args['is_push_warehouse_24h']
    buyer_id = args['buyer_id']
    if period is None:
        period = ""
    if push_wms_status is None:
        push_wms_status = ""
    engine_orders = nopool_conn("vh_orders", "channel")
    engine_goods = nopool_conn("vh_commodities", "channel")
    conn_goods = engine_goods.connect()
    conn_orders = engine_orders.connect()
    # 频道配置
    channel_config = ChannelTypeConfig.channel_type_config
    dff_orders = get_dff_orders(period_type, is_overdue, is_stage,
                                push_wms_status, estimate_delivery_time, conn_orders, conn_goods, is_push_warehouse_24h)
    if dff_orders.empty:
        return {"data": [], "total": 0, "wfh_orders": 0, "yq_orders": 0}
    wfh_orders = len(dff_orders)
    yq_orders = len(dff_orders[dff_orders['is_overdue'] == 1])
    commodities_re_periods = tuple(dff_orders['period'].tolist())
    if len(commodities_re_periods) == 1:
        sql_goods = f"""  SELECT id 'period',
                                                title,
                                                supplier,
                                                buyer_name,
                                                is_supplier_delivery,
                                                CASE import_type 
                                                WHEN 0 THEN '自采' 
                                                WHEN 1 THEN '地采' 
                                                WHEN 2 THEN '跨境' 
                                                ELSE '其它' 
                                                END 'import_type',
                                                FROM_UNIXTIME(onsale_time) 'onsale_time',
                                                FROM_UNIXTIME(sold_out_time) 'sold_out_time'
                                         FROM {channel_config[period_type][1]} where id = {commodities_re_periods[0]}"""
    else:
        sql_goods = f"""  SELECT id 'period',
                                        title,
                                        supplier,
                                        buyer_name,
                                        is_supplier_delivery,
                                        CASE import_type 
                                        WHEN 0 THEN '自采' 
                                        WHEN 1 THEN '地采' 
                                        WHEN 2 THEN '跨境' 
                                        ELSE '其它' 
                                        END 'import_type',
                                        FROM_UNIXTIME(onsale_time) 'onsale_time',
                                        FROM_UNIXTIME(sold_out_time) 'sold_out_time'
                                 FROM {channel_config[period_type][1]} where id in {commodities_re_periods}"""
    if period != "" and str(period) != '0':
        sql_goods = f"""{sql_goods} AND id = {period}"""
    else:
        sql_goods = f"""{sql_goods}"""
    if period_name != "":
        period_name = escape_sql_title(period_name)
        sql_goods = f""" {sql_goods} AND title like '%%{period_name}%%'"""
    else:
        sql_goods = f""" {sql_goods} """
    if len(stime) != 0:
        if stime[0] != "" and stime[1] != "":
            sql_goods = f""" {sql_goods} AND onsale_time BETWEEN {time_str_to_timestamp(stime[0])} AND {time_str_to_timestamp(stime[1])} """
        else:
            sql_goods = f""" {sql_goods} """
    else:
        sql_goods = f""" {sql_goods} """
    if len(etime) != 0:
        if etime[0] != "" and etime[1] != "":
            sql_goods = f""" {sql_goods} AND sold_out_time BETWEEN {time_str_to_timestamp(etime[0])} AND {time_str_to_timestamp(etime[1])} """
        else:
            sql_goods = f""" {sql_goods} """
    else:
        sql_goods = f""" {sql_goods} """
    if supplier_name != "" and supplier_name is not None:
        sql_goods = f""" {sql_goods} AND supplier like '%%{supplier_name}%%'"""
    else:
        sql_goods = f""" {sql_goods} """

    if import_type != "" and import_type is not None:
        sql_goods = f""" {sql_goods} AND import_type = {import_type}"""
    else:
        sql_goods = f""" {sql_goods} """

    if is_supplier_delivery != "" and is_supplier_delivery is not None:
        sql_goods = f""" {sql_goods} AND is_supplier_delivery = {is_supplier_delivery}"""
    else:
        sql_goods = f""" {sql_goods} """
    if buyer_id != "" and str(buyer_id) != '0':
        sql_goods = f"""{sql_goods} AND buyer_id = {buyer_id}"""
    else:
        sql_goods = f"""{sql_goods}"""
    sql_goods = f"""{sql_goods}"""
    df_goods = pd.read_sql(sql_goods, conn_goods)  # 0.5s
    dff_orders = pd.merge(dff_orders, df_goods[['period']])
    dff_orders_re_period = dff_orders.sort_values(
        'period', ascending=False)[['period']]
    total = len(dff_orders_re_period)
    dff_orders_re_period = dff_orders.drop_duplicates(subset='period', keep='first').reset_index(drop=True).sort_values(
        'period', ascending=False)[['period']]
    total = len(dff_orders_re_period.drop_duplicates(
        subset='period', keep='first'))
    if is_export != 1:
        dff_orders_re_period = dff_orders_re_period[(page * page_nums - page_nums):page * page_nums].reset_index(
            drop=True)
    re_orders = pd.merge(dff_orders, dff_orders_re_period)
    df_re_goods = pd.merge(
        df_goods, dff_orders_re_period).reset_index(drop=True)
    if period_type == 2 and is_export != 1:
        dff_re_end = get_undelivered_statistics_gevent(args, conn_goods,
                                                       conn_orders, re_orders,
                                                       channel_config[period_type][1],
                                                       channel_config[period_type][0],
                                                       '1', df_re_goods)[0]
    elif period_type == 2 and is_export == 1:
        dff_re_end = get_undelivered_statistics_gevent(args, conn_goods,
                                                       conn_orders, re_orders,
                                                       channel_config[period_type][1],
                                                       channel_config[period_type][0],
                                                       '1', df_re_goods)[1]
    else:
        dff_re_end = get_undelivered_statistics_gevent(args, conn_goods,
                                                       conn_orders, re_orders,
                                                       channel_config[period_type][1],
                                                       channel_config[period_type][0],
                                                       '1', df_re_goods)
    # 获取期数对应条码及简码
    if dff_re_end.empty:
        return {"data": [], "total": 0, "wfh_orders": 0, "yq_orders": 0}
    dff_re_end = dff_re_end.sort_values('period', ascending=False)
    conn_goods.close()
    conn_orders.close()
    engine_goods.dispose()
    engine_orders.dispose()
    bar_codes = tuple(dff_re_end.bar_code.tolist())
    cross_inventory_dff = get_cross_inventory(bar_codes)
    cross_inventory_dff = cross_inventory_dff.groupby(
        by="bar_code", as_index=False).agg({"available_nums": list,"real_nums": list})
    dff_re_end = pd.merge(dff_re_end, cross_inventory_dff,
                          how="left", on="bar_code")
    dff_re_end["available_nums"] = dff_re_end.available_nums.fillna("无")
    dff_re_end['available_nums'] = dff_re_end['available_nums'].apply(lambda x: ','.join(map(str, x)))
    dff_re_end['available_nums'] = "<" + dff_re_end.bar_code.astype(
        str) + ">" + " " + dff_re_end.available_nums.astype(str)
    dff_re_end["real_nums"] = dff_re_end.real_nums.fillna("无")
    dff_re_end['real_nums'] = dff_re_end['real_nums'].apply(lambda x: ','.join(map(str, x)))
    dff_re_end['real_nums'] = "<" + dff_re_end.bar_code.astype(
        str) + ">" + " " + dff_re_end.real_nums.astype(str)
    
    wms_inventory_dff = get_wms_inventory(bar_codes)
    wms_inventory_dff = wms_inventory_dff.groupby(
        by="bar_code", as_index=False).agg({"wms_inventory": list})
    dff_re_end = pd.merge(dff_re_end, wms_inventory_dff,
                          how="left", on="bar_code")
    dff_re_end["wms_inventory"] = dff_re_end.wms_inventory.fillna("无")
    dff_re_end['wms_inventory'] = dff_re_end.wms_inventory.apply(
        lambda x: ",".join(x))
    dff_re_end['wms_inventory'] = "<" + dff_re_end.bar_code.astype(
        str) + ">" + " " + dff_re_end.wms_inventory.astype(str)
    if is_export == 1:
        dff_re_end = dff_re_end.drop(
            'bar_code', axis=1).drop('short_code', axis=1)
        if period_type != 2:
            dff_re_end = dff_re_end.groupby('period', as_index=False).agg(
                {
                    'yq_orders': 'first',
                    'yq_sale_nums': 'first',
                    'wyq_orders': 'first',
                    'wyq_sale_nums': 'first',
                    'predict_time': 'first',
                    'is_overdue': 'first',
                    'period_type': 'first',
                    'sales_nums': 'first',
                    'title': 'first',
                    'supplier': 'first',
                    'buyer_name': 'first',
                    'import_type': 'first',
                    'onsale_time': 'first',
                    'sold_out_time': 'first',
                    'set_sales_info': 'first',
                    'wms_inventory': set,
                    'available_nums':set,
                    'real_nums':set,
                    'is_supplier_delivery': 'first'
                }
            )
            dff_re_end = dff_re_end.sort_values(
                by="onsale_time", ascending=False)
            dff_re_end['wms_inventory'] = dff_re_end.wms_inventory.apply(
                lambda x: ",".join(list(x)))
            dff_re_end['available_nums'] = dff_re_end.available_nums.apply(
                lambda x: ",".join(list(x)))
            dff_re_end['real_nums'] = dff_re_end.real_nums.apply(
                lambda x: ",".join(list(x)))
        else:
            dff_re_end = dff_re_end.sort_values(
                by="onsale_time", ascending=False)
            dff_re_end['wms_inventory'] = dff_re_end.wms_inventory.apply(
                lambda x: ",".join([x]))
            dff_re_end['available_nums'] = dff_re_end.available_nums.apply(
                lambda x: ",".join([x]))
            dff_re_end['real_nums'] = dff_re_end.real_nums.apply(
                lambda x: ",".join([x]))
    else:
        dff_re_end["order_type"] = 1
        order_no_json_list = [{"sub_order_no": order_no, "order_type": order_type}
                              for order_no, order_type in zip(dff_re_end.sub_order_no, dff_re_end.order_type)]
        order_no_json_list_dff = get_paymain(order_no_json_list)
        dff_re_end = pd.merge(dff_re_end, order_no_json_list_dff)
        corp_dict = {"001": "029", "005": "008"}
        corp_name_dict = {"001": "科技公司", "005": "渝中区微醺酒业商行"}
        dff_re_end["corp_name"] = dff_re_end.corp.apply(
            lambda x: corp_name_dict.get(x, "云酒公司"))
        dff_re_end["corp"] = dff_re_end.corp.apply(
            lambda x: corp_dict.get(x, x))
        technology_json = {"short_code": list(
            set(dff_re_end[dff_re_end.corp == "029"].short_code.tolist())), "corp": "029"}
        cloud_json = {"short_code": list(
            set(dff_re_end[dff_re_end.corp == "002"].short_code.tolist())), "corp": "002"}
        tipsy = {"short_code": list(
            set(dff_re_end[dff_re_end.corp == "008"].short_code.tolist())), "corp": "008"}
        cloud_json_list = [cloud_json, tipsy]
        U_dff = get_u_plus_inventory(technology_json, "U8C")
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(cloud_json_list) * 2) as executor:
            tasks = [executor.submit(get_u_plus_inventory, short_code_json, "T+")
                     for short_code_json in cloud_json_list]
            T_dff = pd.DataFrame(
                columns=["short_code", "atp_number", "current_number"])
            for future in concurrent.futures.as_completed(tasks):
                T_dff = T_dff.append(future.result()).reset_index(drop=True)
        erp_dff = U_dff.append(T_dff).reset_index(drop=True)
        dff_re_end = pd.merge(dff_re_end, erp_dff, how="left", on="short_code")
        dff_re_end["atp_number"] = dff_re_end.atp_number.fillna(0)
        dff_re_end["current_number"] = dff_re_end.current_number.fillna(0)
        dff_re_end['available_number'] = dff_re_end.atp_number.apply(
            lambda x: str(x))
        dff_re_end['exist_number'] = dff_re_end.current_number.apply(
            lambda x: str(x))
        dff_re_end['available_number'] = dff_re_end.corp_name.astype(str) + ":" + dff_re_end.available_number.astype(str)
        dff_re_end['exist_number'] = dff_re_end.corp_name.astype(str) + ":" + dff_re_end.exist_number.astype(str)
        
        corp_dff = dff_re_end[["short_code", "available_number", "exist_number"]].groupby(
            by="short_code", as_index=False).agg({"available_number": set, "exist_number": set})
        corp_dff["available_number"] = corp_dff.available_number.apply(
            lambda x: ",".join(list(x)))
        corp_dff["exist_number"] = corp_dff.exist_number.apply(
            lambda x: ",".join(list(x)))
        corp_dff["available_number"] = "<" + corp_dff.short_code + \
            ">" + " " + corp_dff.available_number
        corp_dff["exist_number"] = "<" + corp_dff.short_code + \
            ">" + " " + corp_dff.exist_number
        dff_re_end = dff_re_end.drop(
            ["available_number", "exist_number"], axis=1)
        dff_re_end = pd.merge(dff_re_end, corp_dff,
                              how="left", on="short_code")
        dff_re_end = dff_re_end.drop(
            'bar_code', axis=1).drop('short_code', axis=1)
        if period_type != 2:
            dff_re_end = dff_re_end.groupby('period', as_index=False).agg(
                {
                    'yq_orders': 'first',
                    'yq_sale_nums': 'first',
                    'wyq_orders': 'first',
                    'wyq_sale_nums': 'first',
                    'predict_time': 'first',
                    'is_overdue': 'first',
                    'period_type': 'first',
                    'sales_nums': 'first',
                    'title': 'first',
                    'supplier': 'first',
                    'buyer_name': 'first',
                    'import_type': 'first',
                    'onsale_time': 'first',
                    'sold_out_time': 'first',
                    'set_sales_info': 'first',
                    'available_number': set,
                    'exist_number': set,
                    'wms_inventory': set,
                    'available_nums':set,
                    'real_nums':set,
                    'is_supplier_delivery': 'first'
                }
            )
            dff_re_end = dff_re_end.sort_values(
                by="onsale_time", ascending=False)
            dff_re_end['available_number'] = dff_re_end.available_number.apply(
                lambda x: list(x))
            dff_re_end['exist_number'] = dff_re_end.exist_number.apply(
                lambda x: list(x))
            dff_re_end['wms_inventory'] = dff_re_end.wms_inventory.apply(
                lambda x: list(x))
            dff_re_end['available_nums'] = dff_re_end.available_nums.apply(
                lambda x: list(x))
            dff_re_end['real_nums'] = dff_re_end.real_nums.apply(
                lambda x: list(x))
        else:
            dff_re_end = dff_re_end.sort_values(
                by="onsale_time", ascending=False)
            dff_re_end['available_number'] = dff_re_end.available_number.apply(lambda x: [
                                                                               x])
            dff_re_end['exist_number'] = dff_re_end.exist_number.apply(lambda x: [
                                                                       x])
            dff_re_end['wms_inventory'] = dff_re_end.wms_inventory.apply(lambda x: [
                                                                         x])
            dff_re_end['available_nums'] = dff_re_end.available_nums.apply(lambda x: [
                                                                         x])
            dff_re_end['real_nums'] = dff_re_end.real_nums.apply(lambda x: [
                                                                         x])
    if dff_re_end.empty:
        return {"data": [], "total": 0, "wfh_orders": 0, "yq_orders": 0}
    else:
        if is_export != 1:
            res_json = {'data': json.loads(dff_re_end.to_json(orient='records')), 'wfh_orders': wfh_orders,
                        'yq_orders': yq_orders,
                        'total': total}
            return res_json
        else:
            # 记录代码结束时间
            end_time = time.time()

            # 计算运行时间
            run_time = end_time - start_time
            print(f"代码运行时间：{run_time:.4f} 秒")
            # 导出 excel
            table_name = export_excel(json.loads(
                dff_re_end.to_json(orient='records')), period_type)
            # d_t = get_dingtalk_accesstoken()
            d_t = get_wecom_accesstoken()
            # 上传文件到钉钉服务器
        if d_t:
            file = os.path.dirname(os.path.abspath(__file__))
            file_url = os.path.join(os.path.abspath(
                file + os.path.sep + "../../"), table_name)
            # rfm = upload_file_to_dingtalk(file_url)
            rfm = upload_file_to_wecom(file_url)
            if rfm:
                # sendf = {'dingtalk_uid': tu, 'media_id': rfm}
                # headers = {}
                # send_res = requests.post(
                #     'https://callback.vinehoo.com/dingtalk-system-notice/dingtalk/v3/sysnotice/sendfile',
                #     headers=headers, data=sendf)
                # sendr = send_res.json()
                sendf = {"content": rfm, "userid": tu,
                         "msgtype": "file", "agentid": 0}
                sendr = send_file_to_wecom(tu, rfm)
                print(sendr)
        res_json = {'data': 1, 'wfh_orders': wfh_orders,
                    'yq_orders': yq_orders,
                    'total': total}
        return res_json


def export_excel(export_list, period_type: int):
    table_name_dict = {1: "闪购", 2: "跨境", 3: "秒发", 4: "尾货"}
    table_name = f"""{table_name_dict[period_type]}未发货订单.xlsx"""
    pf_failed = format_excel(export_list, period_type)
    pf_failed.to_excel(f"""./{table_name}""", index=False)
    return table_name


def format_excel(export_data: list, period_type: int):
    # 将列名替换为中文
    if period_type == 2:
        columns_map = {
            "main_order_no": "主单号",
            "sub_order_no": "子单号",
            "period": "期数",
            "title": "商品名称",
            "onsale_time": "上架时间",
            "predict_time": "预计发货时间",
            "yq_orders": "逾期订单数量",
            "yq_sale_nums": "逾期瓶数",
            "wms_inventory": "萌芽库存",
            "buyer_name": "采购人",
            "import_type": "采购类型",
            "supplier":"供应商", 
        }
        # 指定字段顺序
        order = [
            "main_order_no",
            "sub_order_no",
            "period",
            "title",
            "onsale_time",
            "predict_time",
            "yq_orders",
            "yq_sale_nums",
            "wms_inventory",
            "buyer_name",
            "import_type",
            "supplier"
        ]
    else:
        columns_map = {
            "period": "期数",
            "title": "商品名称",
            "onsale_time": "上架时间",
            "predict_time": "预计发货时间",
            "yq_orders": "逾期订单数量",
            "yq_sale_nums": "逾期瓶数",
            "wms_inventory": "萌芽库存",
            "buyer_name": "采购人",
            "import_type": "采购类型",
            "supplier":"供应商", 
        }
        # 指定字段顺序
        order = [
            "period",
            "title",
            "onsale_time",
            "predict_time",
            "yq_orders",
            "yq_sale_nums",
            "wms_inventory",
            "buyer_name",
            "import_type",
            "supplier"
        ]
    # 将字典列表转换为DataFrame
    pf = pd.DataFrame(list(export_data))
    pf = pf[order]
    pf.rename(columns=columns_map, inplace=True)
    # 替换空单元格
    pf.fillna(" ", inplace=True)
    return pf


def get_dingtalk_accesstoken():
    # 获取钉钉 token
    res = requests.get(
        "https://callback.vinehoo.com/dingtalk-system-notice/dingtalk/v3/sysnotice/accesstoken")
    res_json = res.json()
    if res_json['error_code'] != 0:
        return ''
    else:
        return res_json['access_token']


'''
    审批数据是否来自与企业微信审批
    @author: vber
    @date: 2022-09-14
'''


def is_from_wecom(approval_data: dict) -> bool:
    if approval_data.__contains__("from"):
        return approval_data["from"] == "wecom"
    else:
        return False


'''
获取企业微信中台应用accesstoken
@author: vber
@date: 2022-09-13
'''


def get_wecom_accesstoken() -> str:
    try:
        req = requests.request(
            "GET", f"https://callback.vinehoo.com/wechat/wechat/v3/wecom/accesstoken")
        print("req:", req.text)
        data = req.json()
    except Exception as e:
        print(e)
        return ''

    if data["error_code"] == 0:
        return data["access_token"]
    else:
        return ""


'''
    上传文件到企业微信
    @author: vber
    @date: 2022-09-13
'''


def upload_file_to_wecom(file) -> str:
    access_token = get_wecom_accesstoken()
    print("access_token:{}".format(access_token))
    if access_token == "":
        return ""

    url = f"https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={access_token}&type=file"

    payload = {'type': 'file'}
    file_name = os.path.basename(file)

    files = [
        ('media', (file_name, open(file, 'rb'),
                   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'))
    ]
    headers = {}

    print("pre post")
    res = requests.post(url, headers=headers, data=payload, files=files)
    print(res.text)
    if res.json()['errcode'] != 0:
        raise Exception(res.json()['errmsg'])
    return res.json()['media_id']


'''
    上传文件到钉钉
    @author: vber
    @date: 2022-09-13
'''


def upload_file_to_dingtalk(file) -> str:
    # access_token = get_wecom_accesstoken()
    access_token = get_dingtalk_accesstoken()
    print("access_token:{}".format(access_token))
    if access_token == "":
        return ""

    # url = f"https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={access_token}&type=file"
    url = f"https://oapi.dingtalk.com/media/upload?access_token={access_token}&type=file"

    payload = {'type': 'file'}
    file_name = os.path.basename(file)

    files = [
        ('media', (file_name, open(file, 'rb'),
                   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'))
    ]
    headers = {}

    print("pre post")
    res = requests.post(url, headers=headers, data=payload, files=files)
    print(res.text)
    if res.json()['errcode'] != 0:
        raise Exception(res.json()['errmsg'])
    return res.json()['media_id']


def send_file_to_wecom(uid: str, media_id: str) -> int:
    payload = {
        "content": media_id,
        "userid": uid,
        "msgtype": "file",
        "agentid": 0
    }
    headers = {"content-type": "application/json"}

    res = requests.post(f"https://callback.vinehoo.com/wechat/wechat/v3/wecom/app/send", headers=headers,
                        data=json.dumps(payload))
    resjson = res.json()
    print(f"resjson:{resjson}")
    print(f"uid:{uid}")
    if resjson['error_code'] != 0:
        raise Exception(resjson['error_msg'])
    return resjson['error_code']
