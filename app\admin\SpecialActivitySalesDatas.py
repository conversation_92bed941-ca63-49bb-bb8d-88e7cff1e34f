import pandas as pd
import warnings
import json
import time
from config import ChannelTypeConfig
from app.DButils.MysqlHelper import data_statistic_pool as statistic_pool, orders_pool, marketing_pool, users_pool, \
    commodities_pool
from concurrent.futures import ThreadPoolExecutor

warnings.filterwarnings("ignore")


def get_uid_status(uids, ordesr_tables):
    """
    获取下单记录
    :param uids:客户id元组
    :param ordesr_tables:订单表pool
    :return:
    """
    try:
        results = []

        def get(table):
            if len(uids) == 1:
                sub_order_sql = f"""
                                select distinct uid,created_time 'order_created_time' from {table} where uid = {uids[0]} and sub_order_status in (1,2,3) and  payment_amount - refund_money != 0

                                """
            elif len(uids) > 1:
                sub_order_sql = f"""
                                select distinct uid,created_time 'order_created_time' from {table} where uid in {uids} and sub_order_status in (1,2,3) and  payment_amount - refund_money != 0

                                """
            else:
                pass
            results.append(pdQuery(sub_order_sql, orders_pool))

        with ThreadPoolExecutor(max_workers=len(ordesr_tables)) as executor:
            executor.map(get, ordesr_tables, )

        sub_orders_df = pd.concat(results, axis=0)
        if sub_orders_df.empty:
            return 0
        else:
            return sub_orders_df
    except Exception as e:
        return {"error_code": -1, "error_msg": f"下单记录获取失败{e}", "status": "fail", "data": []}


def get_commodities(period_tup):
    """
    获取商品信息
    :param period_tup:期数元组
    """
    try:
        commodities_talbe = [ChannelTypeConfig.channel_type_config[1][1], ChannelTypeConfig.channel_type_config[2][1],
                             ChannelTypeConfig.channel_type_config[3][1], ChannelTypeConfig.channel_type_config[4][1]]
        results = []

        def get(table):
            if len(period_tup) > 1:
                commodities_sql = f"select id,title from {table} where id in {period_tup}"
            elif len(period_tup) == 1:
                commodities_sql = f"select id,title from {table} where id = {period_tup[0]}"
            else:
                pass
            results.append(pdQuery(commodities_sql, commodities_pool))

        with ThreadPoolExecutor(max_workers=len(commodities_talbe)) as executor:
            executor.map(get, commodities_talbe, )

        commodities_main = pd.concat(results,
                                     axis=0)
        if commodities_main.empty:
            return 0
        else:
            return commodities_main
    except Exception as e:
        return {"error_code": -1, "error_msg": f"商品信息获取失败{e}", "status": "fail", "data": []}


def get_regional_name(regional_id_tup):
    """
    根据地区id获取地区名称
    :param regional_id_tup:区域id元组
    :return:
    """
    try:
        if len(regional_id_tup) > 1:
            regional_name_sql = f"select id,`name` from vh_regional where id in {regional_id_tup}"
        elif len(regional_id_tup) == 1:
            regional_name_sql = f"select id,`name` from vh_regional where id = {regional_id_tup[0]}"
        else:
            pass
        regional_name_df = pdQuery(regional_name_sql, users_pool)
        if regional_name_df.empty:
            return 0
        else:
            return regional_name_df
    except Exception as e:
        return {"error_code": -1, "error_msg": f"地区信息获取失败{e}", "status": "fail", "data": []}


def pdQuery(sql, pool):
    conn, cursor = pool.get_connection()  # 使用 get_connection 方法获取连接和游标
    try:
        df = pd.read_sql_query(sql, conn)
    except Exception as e:
        print(f"Query execution failed: {e}")
        df = pd.DataFrame()
    finally:
        pool.dispose(cursor, conn)  # 释放资源，把连接返回到连接池
    return df


def GetOrderList(time_range, source_type, is_new_user, is_validorder, period, page, page_nums, out_type):
    """
    主页数据入口
    :param time_range: 时间区间
    :param source_type: 投放渠道
    :param is_new_user: 是否为新顾客(1-是,0-否)
    :param is_validorder: 是否为有效订单(1-是,0-否)
    :param out_type: 输出格式(json,data)
    :return:
    """
    try:
        ordesr_tables = [ChannelTypeConfig.channel_type_config[1][0], ChannelTypeConfig.channel_type_config[2][0],
                         ChannelTypeConfig.channel_type_config[3][0], ChannelTypeConfig.channel_type_config[4][0]]
        if source_type == "":
            raise Exception('请选择需要查询的专题活动。')

        mark_sql = f""" select * from vh_nowuser_mark where is_open = 1 and activity_id > 0 and eventmark = '{source_type}'"""
        mark_df = pdQuery(mark_sql, statistic_pool)
        if mark_df.empty:
            raise Exception('未发现标识信息，请确保标识已录入并开启！')

        activity_id = mark_df['activity_id'][0]
        activity_sql = f""" select * from vh_special_activity where id = {activity_id}"""
        activity_df = pdQuery(activity_sql, marketing_pool)

        if activity_df.empty:
            raise Exception('专题活动信息查询失败！')

        activity_stime = activity_df.start_at[0]
        activity_etime = activity_df.end_at[0]
        activity_type = activity_df.activity_type[0]
        mark_df["mark"] = mark_df.platformmark + "-" + mark_df.eventmark
        if source_type != "":
            mark_dict = {}
            for k, v in zip(mark_df.eventmark, mark_df.mark):
                mark_dict[k] = v
            mark = mark_dict[source_type]
        else:
            pass

        begin_time = time_range[0].split("T")[0]
        over_time = time_range[1].split("T")[0]
        if begin_time == "" and over_time != "":
            o_time_str = f"{over_time} 23:59:59"
            o_time = pd.to_datetime(o_time_str)
            last_time = int(time.mktime(o_time.timetuple()))
        elif begin_time != "" and over_time == "":
            b_time_str = f"{begin_time} 00:00:00"
            b_time = pd.to_datetime(b_time_str)
            first_time = int(time.mktime(b_time.timetuple()))
        elif begin_time != "" and over_time != "":
            b_time_str = f"{begin_time} 00:00:00"
            o_time_str = f"{over_time} 23:59:59"
            b_time = pd.to_datetime(b_time_str)
            o_time = pd.to_datetime(o_time_str)
            first_time = int(time.mktime(b_time.timetuple()))
            last_time = int(time.mktime(o_time.timetuple()))
        else:
            pass

        main_id_sql = f"""
                        select main_order_id from vh_order_source_log where concat(source_platform,'-',source_event) = '{mark}'

                        """
        main_id_df = pdQuery(main_id_sql, orders_pool)
        main_id = tuple(main_id_df.main_order_id.tolist())
        if len(main_id) == 0:
            main_ids = '0'
        else:
            main_ids = ', '.join(map(str, main_id))

        where = ""
        # 是否有效订单
        if is_validorder == "0":
            where = "and sub_order_status in (0,4)"
        elif is_validorder == "1":
            where = "and sub_order_status in (1,2,3)"

        # 时间范围
        if begin_time == "" and over_time != "":
            where = f"""{where} and and created_time <= {last_time} """
        elif begin_time != "" and over_time == "":
            where = f"""{where} and and created_time >= {first_time} """
        elif begin_time != "" and over_time != "":
            where = f"""{where} and created_time between {first_time} and {last_time} """

        # 外部专题浏览用户
        view_uids = ''
        if activity_type == 1:
            user_field_sql = f"""
                select uid from vh_user_fields where field_name = 'view_activity_id' and field_value = {activity_id}
            """
            user_field_df = pdQuery(user_field_sql, users_pool)
            if len(user_field_df) > 0:
                view_uids = ', '.join(map(str, tuple(user_field_df.uid.tolist())))

        results = []

        def query_table(table):
            sub_order_sql = f"""
                select sub_order_no,main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in ({main_ids}) and payment_amount - refund_money != 0 {where}
                """
            sub_order_df = pdQuery(sub_order_sql, orders_pool)
            sub_order_df["is_validorder"] = 1

            # 专题活动中产生过订单的用户在专题有效期内产生的其他频道的订单
            if len(sub_order_df) > 0:
                sub_nos = "', '".join(map(str, tuple(sub_order_df.sub_order_no.tolist())))
                uids = ', '.join(map(str, tuple(sub_order_df.uid.tolist())))
                sub_order_sql = f"""
                    select sub_order_no,main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where uid in({uids}) and created_time between {activity_stime} and {activity_etime} and sub_order_no not in('{sub_nos}') and payment_amount - refund_money != 0 {where}
                    """
                other_order_df = pdQuery(sub_order_sql, orders_pool)
                if len(other_order_df) > 0:
                    sub_order_df = pd.concat([sub_order_df, other_order_df], axis=0)

            # 浏览了外部专题活动的用户(包括新，老)，在外部（内购）专题活动有效期内产生的其他频道的订单
            if activity_type == 1 and view_uids != '':
                sub_where = where
                if len(sub_order_df) > 0:
                    sub_nos = "', '".join(map(str, tuple(sub_order_df.sub_order_no.tolist())))
                    sub_where = f"""{sub_where} and sub_order_no not in('{sub_nos}') """
                sub_order_sql = f"""
                    select sub_order_no,main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where uid in({view_uids}) and created_time between {activity_stime} and {activity_etime} and payment_amount - refund_money != 0 {sub_where}
                    """
                other_order_df = pdQuery(sub_order_sql, orders_pool)
                if len(other_order_df) > 0:
                    sub_order_df = pd.concat([sub_order_df, other_order_df], axis=0)

            results.append(sub_order_df)

        with ThreadPoolExecutor(max_workers=len(ordesr_tables)) as executor:
            executor.map(query_table, ordesr_tables)

        # 合并所有查询结果
        sub_orders_df = pd.concat(results, axis=0)

        if sub_orders_df.empty:
            return {"orders": [], "total": 0}

        # 查询主订单
        main_order_ids = ', '.join(map(str, tuple(sub_orders_df.main_order_id.tolist())))
        main_order_sql = f"""
            select id, province_id, city_id, district_id, address
            from vh_order_main
            where id in ({main_order_ids})
        """
        main_order_df = pdQuery(main_order_sql, orders_pool)
        if main_order_df.empty:
            return {"orders": [], "total": 0}

        main_orders_dff = pd.merge(sub_orders_df, main_order_df, how="left", left_on="main_order_id", right_on="id")

        main_orders_dff.sort_values(by=["下单时间"], ascending=False, axis=0, inplace=True)
        """
        获取顾客信息
        """
        uids = tuple(set(main_orders_dff.uid.tolist()))
        uid_str = ', '.join(map(str, uids))
        users_sql = f"""
            select uid,telephone_encrypt,from_unixtime(created_time) 'user_created_time',reg_from from vh_user where uid in ({uid_str})
            """
        users_df = pdQuery(users_sql, users_pool)
        main_df = pd.merge(main_orders_dff, users_df, how="left")

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = {
                'province': executor.submit(get_regional_name, tuple(main_df.province_id.tolist())),
                'city': executor.submit(get_regional_name, tuple(main_df.city_id.tolist())),
                'district': executor.submit(get_regional_name, tuple(main_df.district_id.tolist()))
            }
        for key, future in futures.items():
            if key == 'province':
                province_name = future.result()
            elif key == 'city':
                city_name = future.result()
            elif key == 'district':
                district_name = future.result()

        if type(province_name) != int and type(city_name) != int and type(district_name) != int:
            main_dff = pd.merge(main_df, province_name, how="left", left_on="province_id", right_on="id").merge(
                city_name, how="left", left_on="city_id", right_on="id").merge(district_name, how="left",
                                                                               left_on="district_id", right_on="id")
        elif province_name == 0:
            main_dff = pd.merge(main_df, city_name, how="left", left_on="city_id", right_on="id").merge(district_name,
                                                                                                        how="left",
                                                                                                        left_on="district_id",
                                                                                                        right_on="id")
        elif province_name == 0 and city_name == 0:
            main_dff = pd.merge(main_df, district_name, how="left", left_on="district_id", right_on="id")
        elif province_name == 0 and city_name == 0 and district_name == 0:
            main_dff[["province_name", "city_name", "district_name"]] = ["暂无省信息", "暂无市信息", "暂无区信息"]
        else:
            pass
        main_dff["area"] = main_dff.name_x + main_dff.name_y + main_dff.name + main_dff.address
        commodities_main_df = get_commodities(tuple(main_dff.period.tolist()))
        main_data = pd.merge(main_dff, commodities_main_df, how="left", left_on="period", right_on="id")
        orders_user_data = get_uid_status(tuple(main_data.uid.tolist()), ordesr_tables)
        if type(orders_user_data) == int:
            main_data["user_type"] = "新用户"
        else:
            new_uid = set()
            for uid, created_time in zip(main_data.uid, main_data.created_time):
                uid_order = orders_user_data[orders_user_data.uid == uid]
                hav_order = uid_order[uid_order.order_created_time < created_time]
                if hav_order.empty or uid in new_uid:
                    new_uid.add(uid)

            user_type = ["新用户" if uid in new_uid else "老用户" for uid in main_data.uid]
            main_data["user_type"] = user_type
        if is_new_user == "":
            Over_Main_Df = main_data
        elif is_new_user == "0":
            Over_Main_Df = main_data[main_data.user_type == "老用户"]
        elif is_new_user == "1":
            Over_Main_Df = main_data[main_data.user_type == "新用户"]
        else:
            pass

        if period != "":
            Over_Main_Df.period = Over_Main_Df.period.astype(type(period))
            Over_Main_Df = Over_Main_Df[Over_Main_Df.period == period]
        total = len(Over_Main_Df)
        if page == "":
            page = "1"
        if page_nums == "":
            page_nums = "10"
        else:
            pass
        if page and page_nums == "all":
            Over_Main_Df = Over_Main_Df.copy()
        else:
            Over_Main_Df = Over_Main_Df[
                           (int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(
                drop=True)
        if out_type == "data":
            Over_Main_Df.drop(
                ["main_order_id", "id", "created_time", "province_id", "city_id", "district_id", "id_x", "id_y", "id_x",
                 "id_y", "name_x", "name_y", "name", "address"], axis=1, inplace=True)
            Over_Main_Dff = Over_Main_Df[
                ["下单时间", "uid", "title", "period", "area", "telephone_encrypt", "order_qty",
                 "payment_amount - refund_money", "user_type", "user_created_time", "is_validorder"]]
            Over_Main_Dff.rename(
                columns={"下单时间": "order_created_time", "payment_amount - refund_money": "payment_amount"},
                inplace=True)
            Over_Out = Over_Main_Dff
        elif out_type == "json":
            Over_Main_Df.drop(
                ["main_order_id", "id", "created_time", "province_id", "city_id", "district_id", "id_x", "id_y", "id_x",
                 "id_y", "name_x", "name_y", "name", "address"], axis=1, inplace=True)
            Over_Main_Dff = Over_Main_Df[
                ["下单时间", "title", "reg_from", "period", "area", "telephone_encrypt", "uid", "order_qty",
                 "payment_amount - refund_money", "user_type", "user_created_time"]]
            Over_Main_Dff.rename(
                columns={"下单时间": "order_created_time", "payment_amount - refund_money": "payment_amount"},
                inplace=True)
            Over_Main_Dff["order_created_time"] = Over_Main_Dff.order_created_time.astype(str)
            Over_Main_Dff["user_created_time"] = Over_Main_Dff.user_created_time.astype(str)
            reg_from_dict = {0: "未知", 1: "android", 2: "ios", 3: "酒云网小程序", 4: "h5", 5: "PC", 6: "抖音小程序",
                             7: "后台添加", 8: "酒历小程序", 9: "公社小程序", 10: "iPad", 11: "门店小程序"}
            Over_Main_Dff["reg_from"] = Over_Main_Dff.reg_from.apply(lambda x: reg_from_dict[x])
            Over_Out = {"orders": json.loads(Over_Main_Dff.to_json(orient="records")),
                        "total": total
                        }
        return Over_Out
    except Exception as e:
        return {"error_code": -1, "error_msg": f"数据获取失败，{e}", "status": "fail", "data": []}


def GetOutUserDatas(time_range, source_type, is_new_user, is_validorder, period):
    """
    统计数据入口
    :param time_range: 时间区间
    :param source_type: 投放渠道
    :param is_new_user: 是否为新顾客(1-是,0-否)
    :param is_validorder: 是否为有效订单(1-是,0-否)
    :return:    
    """
    try:
        Main_Order = GetOrderList(time_range, source_type, is_new_user, is_validorder, period, "all", "all", "data")
        if type(Main_Order) == dict:
            return Main_Order
        elif Main_Order.empty:
            return {"error_code": -1, "error_msg": "未发现相应数据,无法生成可视化!", "total": 0, "data": []}
        else:
            Effective_Order = Main_Order[Main_Order.is_validorder == 1]
            Void_Order = Main_Order[Main_Order.is_validorder == 0]
            if Effective_Order.empty:
                Effective_Order_Count = 0
            else:
                Effective_Order_Count = Effective_Order.order_created_time.count()
            if Void_Order.empty:
                Void_count = 0
            else:
                Void_count = Void_Order.order_created_time.count()
            Add_count = Effective_Order_Count + Void_count

            Effective_Percentage = 0
            if Effective_Order_Count > 0 and Add_count > 0:
                Effective_Percentage = Effective_Order_Count / (Effective_Order_Count + Void_count) * 100

            Void_Percentage = 0
            if Void_count > 0 and Add_count > 0:
                Void_Percentage = Void_count / (Effective_Order_Count + Void_count) * 100

            Add_Percentage = Effective_Percentage + Void_Percentage
            out_user = Main_Order[["user_type", "uid"]]
            out_user.drop_duplicates(keep="first", inplace=True)
            out_user_count = out_user.groupby(by="user_type", as_index=False).count()
            out_user_count.rename(columns={"uid": "user_count"}, inplace=True)
            out_user_count["user_proportion"] = out_user_count.user_count.apply(
                lambda x: x / sum(out_user_count.user_count.tolist()) * 100)
            out_user_orders_count = Main_Order[["user_type", "title"]].groupby(by="user_type", as_index=False).count()[
                "title"]
            out_user_count["user_orders_count"] = out_user_orders_count
            out_user_count["user_orders_proportion"] = out_user_count.user_orders_count.apply(
                lambda x: x / sum(out_user_count.user_orders_count.tolist()) * 100)
            user_orders_ment = \
                Main_Order[["user_type", "payment_amount"]].groupby(by="user_type", as_index=False).sum()[
                    "payment_amount"]
            out_user_count["user_orders_ment"] = user_orders_ment
            out_user_count["user_orders_ment_proportion"] = out_user_count.user_orders_ment.apply(
                lambda x: x / sum(out_user_count.user_orders_ment.tolist()) * 100)
            out_user_count.loc[2] = ["合计", sum(out_user_count.user_count.tolist()),
                                     sum(out_user_count.user_proportion.tolist()),
                                     sum(out_user_count.user_orders_count.tolist()),
                                     sum(out_user_count.user_orders_proportion.tolist()),
                                     sum(out_user_count.user_orders_ment.tolist()),
                                     sum(out_user_count.user_orders_ment_proportion.tolist())]
            out_user_count["user_proportion"] = out_user_count.user_proportion.apply(lambda x: '%.2f' % x)
            out_user_count["user_orders_proportion"] = out_user_count.user_orders_proportion.apply(lambda x: '%.2f' % x)
            out_user_count["user_orders_ment"] = out_user_count.user_orders_ment.apply(lambda x: '%.2f' % x)
            out_user_count["user_orders_ment_proportion"] = out_user_count.user_orders_ment_proportion.apply(
                lambda x: '%.2f' % x)
            out_user_count.set_index("user_type", inplace=True)
            Effective_Void_count_json = {
                "有效订单": int(Effective_Order_Count),
                "无效订单": int(Void_count),
                "合计": int(Add_count)
            }
            Effective_Void_Proportion_json = {
                "有效订单占比": '%.2f' % Effective_Percentage,
                "无效订单占比": '%.2f' % Void_Percentage,
                "合计": '%.2f' % Add_Percentage
            }
            total = len(out_user_count.T) + 2
            out_user_count_json = {
                "user_count": out_user_count.T.iloc[0].to_dict(),
                "user_proportion": out_user_count.T.iloc[1].to_dict(),
                "user_orders_count": out_user_count.T.iloc[2].to_dict(),
                "user_orders_proportion": out_user_count.T.iloc[3].to_dict(),
                "user_orders_ment": out_user_count.T.iloc[4].to_dict(),
                "user_orders_ment_proportion": out_user_count.T.iloc[5].to_dict(),
                "Effective_Void_count": Effective_Void_count_json,
                "Effective_Void_proportion": Effective_Void_Proportion_json,
                "total": total
            }
        return out_user_count_json
    except Exception as e:
        return {"error_code": -1, "error_msg": f"统计数据获取失败{e}", "status": "fail", "data": []}


def GetOutGoodsDatas(time_range, source_type, is_new_user, is_validorder, page, page_nums, period):
    """
    统计数据入口
    :param time_range: 时间区间
    :param source_type: 投放渠道
    :param is_new_user: 是否为新顾客(1-是,0-否)
    :param is_validorder: 是否为有效订单(1-是,0-否)
    :param period: 期数
    :return:    
    """
    Main_Order = GetOrderList(time_range, source_type, is_new_user, is_validorder, period, "all", "all", "data")
    if type(Main_Order) == dict:
        return Main_Order
    elif Main_Order.empty:
        return {"error_code": -1, "error_msg": "未发现相应数据,无法生成可视化!", "total": 0, "data": []}
    else:
        Main_Order = Main_Order.groupby(by="period", as_index=False).agg(
            {"title": "first", "order_qty": "size", "payment_amount": sum})
        Main_Order = Main_Order.sort_values(by=["order_qty", "payment_amount"], ascending=False).reset_index(drop=True)
        Main_Order["Order_Percentage"] = Main_Order.order_qty.apply(
            lambda x: round(x / (sum(Main_Order.order_qty)) * 100, 2))
        Main_Order["Amount_Percentage"] = Main_Order.payment_amount.apply(
            lambda x: round(x / (sum(Main_Order.payment_amount)) * 100, 2))
        if page == "":
            page = "1"
        if page_nums == "":
            page_nums = "10"
        total = len(Main_Order)
        Main_Order_Show = Main_Order[
                          (int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(
            drop=True)
        Main_Order_Show = Main_Order_Show[
            ["period", "title", "order_qty", "Order_Percentage", "payment_amount", "Amount_Percentage"]]
        if len(Main_Order) >= 9:
            Main_Data_Nine = Main_Order[["title", "order_qty", "payment_amount"]].head(9)
            Main_Data_Other = Main_Order.drop(index=Main_Data_Nine.index.tolist(), axis=0).reset_index(drop=True)[
                ["order_qty", "payment_amount"]]
            Main_Data_Other["title"] = "other"
            Main_Data_Other = Main_Data_Other.groupby(by="title", as_index=False).agg(
                {"order_qty": sum, "payment_amount": sum})
            Data_Show = Main_Data_Nine.append(Main_Data_Other).reset_index(drop=True)
        else:
            Data_Show = Main_Order[["title", "order_qty", "payment_amount"]]
        Data_Show = Data_Show.rename(columns={"order_qty": "Order_Percentage", "payment_amount": "Amount_Percentage"})
        Order_Data_Show = Data_Show[["title", "Order_Percentage"]]
        Amount_Data_Show = Data_Show[["title", "Amount_Percentage"]]
        res_json = {
            "Orders": json.loads(Main_Order_Show.to_json(orient="records")),
            "Visualization": {
                "Order_Percentage": json.loads(Order_Data_Show.to_json(orient="records")),
                "Amount_Percentage": json.loads(Amount_Data_Show.to_json(orient="records"))
            },
            "Orders_total": total
        }
        return res_json


def GetOrderSalesDatas(time_range, source_type, is_new_user, is_validorder, page, page_nums, period, Statistics_type):
    if Statistics_type == "0":
        return GetOutUserDatas(time_range, source_type, is_new_user, is_validorder, period)
    elif Statistics_type == "1":
        return GetOutGoodsDatas(time_range, source_type, is_new_user, is_validorder, page, page_nums, period)
    else:
        return {"error_code": -1, "error_msg": f"参数不对", "status": "fail", "data": {}}
