import pandas as pd
import pymysql
from app.CommonLibraries.getConfig import get_config
from dateutil.relativedelta import relativedelta
import calendar
import logging
import datetime
"""
每月销售额统计

"""

def conn_mysql(mysql_host, mysql_port, mysql_user, mysql_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=mysql_host, port=mysql_port, user=mysql_user, password=mysql_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def exits_data(date,cursor):
    """
    验证是否已存在
    :param date: 日期
    :return:
    """
    sql = f"""select * from vh_monthly_sales_sum where `下单时间` = '{date}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_monthly_sales_sum where `下单时间` = '{date}'"""
        cursor.execute(delete_sql)
    else:
        pass
    
def get_sql_conn(read_id,write_id,group):
    """
    获取数据库读写链接
    """
    read_info = get_config(data_id=read_id, group=group)
    read_host = read_info['host']
    read_port = read_info['port']
    read_user = read_info['user']
    read_password = read_info['password']
    write_info = get_config(data_id=write_id, group=group)
    write_host = write_info['host']
    write_port = write_info['port']
    write_user = write_info['user']
    write_password = write_info['password']
    conn_dict={"read":{"host":read_host,"port":read_port,"user":read_user,"password":read_password},"write":{"host":write_host,"port":write_port,"user":write_user,"password":write_password}}
    return conn_dict
    
def get_payment_amount(date_time):
    if len(str(date_time)) == 4:
        year = date_time.split('-')[0]
    else:
        one_month=pd.to_datetime(date_time)+ relativedelta(months=-1)
        over_time=datetime.datetime(one_month.year, one_month.month, calendar.monthrange(one_month.year, one_month.month)[1])+datetime.timedelta(hours=23,minutes=59,seconds=59)
        over_date=pd.to_datetime(f'{over_time.year}-{over_time.month}-{over_time.day} {over_time.hour}:{over_time.minute}:{over_time.second}')
        three_month=pd.to_datetime(date_time)+ relativedelta(months=-3)
        start_time=datetime.datetime(three_month.year, three_month.month, 1)
        start_date=pd.to_datetime(f'{start_time.year}-{start_time.month}-{start_time.day} {start_time.hour}:{start_time.minute}:{start_time.second}')
    conn_dict=get_sql_conn("db.data.v3_all","db.data.write.v3_all","vinehoo.accounts")
    read_conn_dict=conn_dict["read"]
    write_conn_dict=conn_dict["write"]
    conn = conn_mysql(read_conn_dict["host"], read_conn_dict["port"], read_conn_dict["user"], read_conn_dict["password"], 'vh_orders')
    conn_out = conn_mysql(write_conn_dict["host"], write_conn_dict["port"], write_conn_dict["user"], write_conn_dict["password"], 'vh_data_statistics')
    cursor = conn_out.cursor()
    if len(str(date_time)) == 4:
        sql = f"""
                select
                            e.`下单时间`,
                            e.`闪购`,
                            e.`秒发`,
                            e.`跨境`,
                            f.`尾货`
                        from
                        (select
                            c.`下单时间`,
                            c.`闪购`,
                            c.`秒发`,
                            d.`跨境`
                        from
                        (select
                            a.`下单时间`,
                            a.`闪购`,
                            b.`秒发`
                        from
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '闪购'
                        from
                            vh_flash_order
                        where 
                            year(FROM_UNIXTIME(created_time))={year}
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                        )a
                        left join
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '秒发'
                        from
                            vh_second_order
                        where 
                            year(FROM_UNIXTIME(created_time))={year}
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                            )b
                        on
                            a.`下单时间`
                                =
                            b.`下单时间`)c
                        left join
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '跨境'
                        from
                            vh_cross_order
                        where 
                            year(FROM_UNIXTIME(created_time))={year}
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                            )d
                        on
                            c.`下单时间`
                                =
                            d.`下单时间`)e
                        left join
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '尾货'
                        from
                            vh_tail_order
                        where 
                            year(FROM_UNIXTIME(created_time))={year}
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                            )f
                        on
                            e.`下单时间`
                            =
                            f.`下单时间`
                    """
    else:
        sql = f"""
                select
                            e.`下单时间`,
                            e.`闪购`,
                            e.`秒发`,
                            e.`跨境`,
                            f.`尾货`
                        from
                        (select
                            c.`下单时间`,
                            c.`闪购`,
                            c.`秒发`,
                            d.`跨境`
                        from
                        (select
                            a.`下单时间`,
                            a.`闪购`,
                            b.`秒发`
                        from
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '闪购'
                        from
                            vh_flash_order
                        where 
                            FROM_UNIXTIME(created_time,'%Y-%m-%d %H:%i:%s') between '{start_date}' and '{over_date}'
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                        )a
                        left join
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '秒发'
                        from
                            vh_second_order
                        where 
                            FROM_UNIXTIME(created_time,'%Y-%m-%d %H:%i:%s') between '{start_date}' and '{over_date}'
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                        )b
                        on
                            a.`下单时间`
                                =
                            b.`下单时间`)c
                        left join
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '跨境'
                        from
                            vh_cross_order
                        where 
                            FROM_UNIXTIME(created_time,'%Y-%m-%d %H:%i:%s') between '{start_date}' and '{over_date}'
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                        )d
                        on
                            c.`下单时间`
                                =
                            d.`下单时间`)e
                        left join
                        (select
                            FROM_UNIXTIME(created_time,'%Y-%m') '下单时间',
                            (sum(payment_amount)-sum(refund_money)) '尾货'
                        from
                            vh_tail_order
                        where 
                            FROM_UNIXTIME(created_time,'%Y-%m-%d %H:%i:%s') between '{start_date}' and '{over_date}'
                        and
                            sub_order_status in (1,2,3)
                        group by
                            `下单时间`
                        )f
                        on
                            e.`下单时间`
                            =
                            f.`下单时间`
           """
    df = pd.read_sql(sql, conn)
    df.fillna(value=0, inplace=True,axis=0,limit=1)
    conn.close()
    sql_c = f"""

    CREATE TABLE IF NOT EXISTS `vh_monthly_sales_sum`(
    `下单时间` VARCHAR(20) PRIMARY KEY,
    `闪购` FLOAT,
    `秒发` FLOAT,
    `跨境` FLOAT,
    `尾货` FLOAT)

    """
    cursor.execute(sql_c)
    date_List = df.下单时间.values.tolist()
    flash_List = df.闪购.values.tolist()
    second_List = df.秒发.values.tolist()
    cross_List = df.跨境.values.tolist()
    tail_List = df.尾货.values.tolist()
    commit_insert_list = [(date_List[i], flash_List[i], second_List[i], cross_List[i], tail_List[i]) for i in
                          range(len(date_List))]
    commit_update_list = [(flash_List[i], second_List[i], cross_List[i], tail_List[i], date_List[i]) for i in
                          range(len(date_List))]
    sql_insert = "insert into `vh_monthly_sales_sum` (`下单时间`,`闪购`,`秒发`,`跨境`,`尾货`) values(%s,%s,%s,%s,%s)"
    sql_update = "update `vh_monthly_sales_sum`  set `闪购` =(%s),`秒发` =(%s),`跨境` =(%s),`尾货` =(%s)  where `下单时间`=(%s)"
    with conn_out.cursor() as cursor:
        try:
            for date in date_List:
                exits_data(date,cursor)
            cursor.executemany(sql_insert, commit_insert_list)
            conn_out.commit()
        except:
            conn_out.rollback()
            try:
                cursor.executemany(sql_update, commit_update_list)
                conn_out.commit()
            except Exception as e:
                logging.exception("exception")
                conn_out.rollback()
    cursor.close()
    conn_out.close()
    return 'ok'