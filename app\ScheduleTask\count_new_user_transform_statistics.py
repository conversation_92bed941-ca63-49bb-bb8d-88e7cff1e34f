import pymysql
import warnings
import concurrent
import pandas as pd
from sqlalchemy import create_engine
from config import ChannelTypeConfig
from datetime import datetime,timedelta
from app.DButils.MysqlHelper import conn_str
from app.CommonLibraries.getConfig import get_config

warnings.filterwarnings("ignore")

rm_config= get_config("db.data.write.v3_all", "vinehoo.accounts")
rm_host = rm_config['host']
rm_port = rm_config['port']
rm_user = rm_config['user']
rm_password= rm_config['password']

def get_last_day():
    last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    return last_day


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    
def get_new_user_orders(table,timestamp_start,timestamp_end,new_user_uid):
    orders_conn = nopool_conn("vh_orders").connect()
    if len(new_user_uid) == 1:
        orders_sql = f"""select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid,1 as 'count',payment_amount - refund_money as 'payment_amount' from {table} where created_time between {timestamp_start} and {timestamp_end} and sub_order_status in (1,2,3) and payment_amount - refund_money != 0 and uid = {new_user_uid[0]}"""
    else:
        orders_sql = f"""select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid,1 as 'count',payment_amount - refund_money as 'payment_amount' from {table} where created_time between {timestamp_start} and {timestamp_end} and sub_order_status in (1,2,3) and payment_amount - refund_money != 0 and uid in {new_user_uid}"""  
    df = pd.read_sql_query(orders_sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return df



def get_all_orders(table,timestamp_start,timestamp_end):
    orders_conn = nopool_conn("vh_orders").connect()
    orders_sql = f"""select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid,1 as 'count',payment_amount - refund_money as 'payment_amount' from {table} where created_time between {timestamp_start} and {timestamp_end} and sub_order_status in (1,2,3) and payment_amount - refund_money != 0""" 
    df = pd.read_sql_query(orders_sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return df



def get_new_user_transform_massage(date):
    Cg = ChannelTypeConfig.channel_type_config
    tables = [Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]
    timestamp_start,timestamp_end = int(datetime.strptime(f"{date} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"{date} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
    new_user_sql = f""" select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid from vh_user where created_time between {timestamp_start} and {timestamp_end} group by `uid`"""
    user_conn = nopool_conn("vh_user").connect()
    new_user_df = pd.read_sql_query(new_user_sql,user_conn)
    user_conn.close()
    nopool_conn("vh_user").dispose()
    if new_user_df.empty:
        new_created_df = pd.DataFrame({"date":[date],"created_user_count":[0],"created_user_orders_count":[0],"new_orders":[0],"new_user_payment_amount":[0],"all_user_count":[0],"all_user_orders_count":[0],"all_orders":[0],"all_user_payment_amount":[0]})
        return new_created_df
    else:
        new_user_uid = tuple(set(new_user_df.uid.tolist()))
        new_user_df = new_user_df.groupby(by=["date"],as_index=False).agg({"uid":'count'})
        new_user_df.columns = ["date","created_user_count"]
    with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        tasks = [executor.submit(get_new_user_orders,table,timestamp_start,timestamp_end,new_user_uid) for table in tables]
        new_user_orders_df = pd.DataFrame(columns=["date","uid","count","payment_amount"])
        for future in concurrent.futures.as_completed(tasks):
            new_user_orders_df=new_user_orders_df.append(future.result()).reset_index(drop=True)
    with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        tasks = [executor.submit(get_all_orders,table,timestamp_start,timestamp_end) for table in tables]
        all_orders_df = pd.DataFrame(columns=["date","uid","count","payment_amount"])
        for future in concurrent.futures.as_completed(tasks):
            all_orders_df=all_orders_df.append(future.result()).reset_index(drop=True)
    if new_user_orders_df.empty and all_orders_df.empty:
        new_user_df["created_user_orders_count"] = 0
        new_user_df["new_orders"] = 0
        new_user_df["new_user_payment_amount"] = 0
        new_user_df["all_user_orders_count"] = 0
        new_user_df["all_orders"] = 0
        new_user_df["all_user_payment_amount"] = 0
        res_dff = new_user_df.copy()
    elif new_user_orders_df.empty and all_orders_df.empty == False:
        all_order_users = all_orders_df[["date","uid"]].groupby(by="date")["uid"].nunique().reset_index()
        all_order_users.columns = ["date","all_user_orders_count"]
        all_orders_df = all_orders_df.groupby(by="date",as_index=False).agg({"count":sum,"payment_amount":sum})
        all_orders_df.columns = ["date","all_orders","all_user_payment_amount"]
        all_orders_df = pd.merge(new_user_df,all_order_users,how="left",on="date").merge(all_orders_df,how="left",on="date")
        all_orders_df["created_user_orders_count"] = 0
        all_orders_df["new_orders"] = 0
        all_orders_df["new_user_payment_amount"] = 0
        all_orders_df = all_orders_df[["date","created_user_count","new_orders","new_user_payment_amount","created_user_orders_count","all_orders","all_user_payment_amount","all_user_orders_count"]]
        res_dff = all_orders_df.copy()
    elif new_user_orders_df.empty == False and all_orders_df.empty:
        new_order_users = new_user_orders_df[["date","uid"]].groupby(by="date")["uid"].nunique().reset_index()
        new_order_users.columns = ["date","created_user_orders_count"]
        new_user_orders_df = new_user_orders_df.groupby(by="date",as_index=False).agg({"count":sum,"payment_amount":sum})
        new_user_orders_df.columns = ["date","new_orders","new_user_payment_amount"]
        new_user_orders_df = pd.merge(new_user_df,new_order_users,how="left",on="date").merge(new_user_orders_df,how="left",on="date")
        new_user_orders_df["all_user_orders_count"] = 0
        new_user_orders_df["all_orders"] = 0
        new_user_orders_df["all_user_payment_amount"] = 0
        new_user_orders_df = new_user_orders_df[["date","created_user_count","new_orders","new_user_payment_amount","created_user_orders_count","all_orders","all_user_payment_amount","all_user_orders_count"]]
        res_dff = new_user_orders_df.copy()
    else:
        new_order_users = new_user_orders_df[["date","uid"]].groupby(by="date")["uid"].nunique().reset_index()
        new_order_users.columns = ["date","created_user_orders_count"]
        new_user_orders_df = new_user_orders_df.groupby(by="date",as_index=False).agg({"count":sum,"payment_amount":sum})
        new_user_orders_df.columns = ["date","new_orders","new_user_payment_amount"]
        all_order_users = all_orders_df[["date","uid"]].groupby(by="date")["uid"].nunique().reset_index()
        all_order_users.columns = ["date","all_user_orders_count"]
        all_orders_df = all_orders_df.groupby(by=["date"],as_index=False).agg({"count":sum,"payment_amount":sum})
        all_orders_df.columns = ["date","all_orders","all_user_payment_amount"]
        res_dff = pd.merge(new_user_df,new_user_orders_df,how="left",on="date").merge(new_order_users,how="left",on="date").merge(all_orders_df,how="left",on="date").merge(all_order_users,how="left",on="date")
        res_dff = res_dff[["date","created_user_count","new_orders","new_user_payment_amount","created_user_orders_count","all_orders","all_user_payment_amount","all_user_orders_count"]]
    res_dff = res_dff.fillna(0)
    res_dff["new_price"] = res_dff.new_user_payment_amount/res_dff.created_user_orders_count
    res_dff["new_price"] = res_dff.new_price.apply(lambda x:round(x))
    res_dff["all_price"] = res_dff.all_user_payment_amount/res_dff.all_user_orders_count
    res_dff["all_price"] = res_dff.all_price.apply(lambda x:round(x))
    res_dff = res_dff[["date","created_user_count","new_orders","new_user_payment_amount","created_user_orders_count","new_price","all_orders","all_user_payment_amount","all_user_orders_count","all_price"]].fillna(0)
    return res_dff


def exits_data(date,cursor):
    """
    验证是否已存在
    :param date: 日期
    :param is_liquor:
    :param sell_type
    :return:
    """
    sql = f"""select date from vh_user_transform_statistics where `date` = '{date}' """
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_user_transform_statistics where `date` = '{date}'"""
        cursor.execute(delete_sql)
    else:
        pass
    
    
def insert_mysql(*args):
    cursor = args[10]
    exits_data(date=args[0],cursor=cursor)
    sql = f"""INSERT INTO vh_user_transform_statistics 
             (`date`,`created_user_count`,`new_orders`,`new_user_payment_amount`,`created_user_orders_count`,`new_price`,`all_orders`,`all_user_payment_amount`,`all_user_orders_count`,`all_price`) 
             VALUES ('{args[0]}',{args[1]},{args[2]},{args[3]},{args[4]},{args[5]},{args[6]},{args[7]},{args[8]},{args[9]})"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")

        
        
def handler_new_user_transform_daily(date,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        dff = get_new_user_transform_massage(date)
        data_list = dff.values.tolist()
        for data in data_list:
            if data[1] == 0:
                return '无用户注册信息'
            else:
                insert_mysql(data[0], data[1], data[2], data[3],data[4], data[5], data[6], data[7], data[8], data[9],cursor)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()
    
    
def get_new_user_transform_statistics():    
    return handler_new_user_transform_daily(get_last_day(),rm_host, rm_port, rm_user, rm_password)