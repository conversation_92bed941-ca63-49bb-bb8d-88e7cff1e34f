import csv
import requests
import json
import urllib.parse
import hashlib
import time
import secrets

def sort_query_string(query_string):
    params = query_string.split('&')
    params.sort()
    return '&'.join(params)

def md5str(s):
    return hashlib.md5(s.encode()).hexdigest()

def generate_random_string(length=16):
    return secrets.token_hex(length)

def generate_headers(query_string):
    t = str(int(time.time()))
    noucestr = generate_random_string()  # Randomly generated string
    sorted_query = sort_query_string(query_string)
    # print("sorted_query:",sorted_query, noucestr)

    # Generating the sign value
    sign = md5str(sorted_query + t + noucestr)

    return {
        't': t,
        'nouncestr': noucestr,
        'sign': sign,
        'Vinehoo-Client':'python-search-keywords',
        'Vinehoo-Client-Version':'1.0',
        'vinehoo-uid':'328584'
    }

def fetch_data(keywords):
    base_url = "https://callback.vinehoo.com/clients-api/openapi/v3/search/aggregation"
    # base_url = 'https://vinehoo.com/api/openapi/v3/search/aggregation'
    # base_url = 'http://127.0.0.1:8888/openapi/v3/search/aggregation'
    query_params = {
        'page': 1,
        'limit': 1,
        'channel': 'commodity',
        'search_type': 'goods'
    }
    encoded_query_string = urllib.parse.urlencode(query_params)
    full_query_string = f"{encoded_query_string}&keywords={keywords}"
    full_url = f'{base_url}?{full_query_string}'

    headers = generate_headers(full_query_string)
    response = requests.get(full_url, headers=headers)
    data = json.loads(response.text)
    # print(data)
    # Check if the 'list' array is empty
    return bool(data.get('data', {}).get('list'))

def process_keywords(data):
    result = []
    total = len(data)

    for i, (keyword, count) in enumerate(data.items(), 1):
        status = 0
        if not fetch_data(keyword):
            status = 1
        result.append({'keywords': keyword, 'search_count': count, 'status': status})
        print(f"{i}/{total}")

    return result

# def read_csv(file_path):
#     with open(file_path, mode='r', encoding='utf-8') as file:
#         reader = csv.DictReader(file)
#         return list(reader)

# def write_csv(file_path, data):
#     with open(file_path, mode='w', encoding='utf-8', newline='') as file:
#         writer = csv.DictWriter(file, fieldnames=['Keyword', 'Count'])
#         writer.writeheader()
#         writer.writerows(data)

# def process_keywords(file_path):
#     data = read_csv(file_path)
#     failed_keywords = []

#     for row in data:
#         keyword = row['Keyword']
#         count = row['Count']
#         if not fetch_data(keyword):
#             failed_keywords.append({'Keyword': keyword, 'Count': count})

#     write_csv('无结果.csv', failed_keywords)


# process_keywords('20231226-keywords.csv')
# print(fetch_data("白酒"))
