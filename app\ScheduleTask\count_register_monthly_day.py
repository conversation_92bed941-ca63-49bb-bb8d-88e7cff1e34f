import pymysql
import calendar
import warnings
import unittest
import concurrent
import pandas as pd
from datetime import datetime
from config import ChannelTypeConfig
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str
from dateutil.relativedelta import relativedelta


warnings.filterwarnings("ignore")


# 注册用户月统计


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_last_month(month_number):
    # 获取前month_number个月
    try:
        month_date = datetime.strptime(get_cur_month(),'%Y-%m') - relativedelta(months=month_number)
        return month_date.strftime("%Y-%m")
    except Exception as e:
        print(f"获取前一月失败,失败原因:{e}")


def get_cur_month():
    # 获取查询时间(月)
    return datetime.strftime(datetime.now()- relativedelta(months=1),"%Y-%m")


def get_re_users(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取注册用户
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_user")
    sql = f"""SELECT uid FROM `vh_user` WHERE FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df

"""
    统计月活跃用户数量
    将当月每日活跃用户数量相加
    @author:杜伟
    @date:2022-09-08
"""

def get_order_users(table,start,end):
    conn = nopool_conn("vh_orders").connect()
    sql = f""" select uid from {table} where sub_order_status in (1,2,3) and payment_amount - refund_money != 0 and created_time between {start} and {end} group by `uid`"""
    df = pd.read_sql_query(sql,conn)
    conn.close()
    nopool_conn("vh_orders").dispose()
    return df.fillna(0)


def get_monthly_buyuers_count() -> dict:
    result = dict()
    Cg = ChannelTypeConfig.channel_type_config
    tables = [Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]
    cur_start_time = datetime.strptime(f"{get_cur_month()}-01 00:00:00","%Y-%m-%d %H:%M:%S")
    cur_start = int(cur_start_time.timestamp())
    cur_end = int(datetime(cur_start_time.year, cur_start_time.month, calendar.monthrange(cur_start_time.year, cur_start_time.month)[1],23,59,59).timestamp())
    last_start_time = datetime.strptime(f"{get_last_month(1)}-01 00:00:00","%Y-%m-%d %H:%M:%S")
    last_start = int(last_start_time.timestamp())
    last_end = int(datetime(last_start_time.year, last_start_time.month, calendar.monthrange(last_start_time.year, last_start_time.month)[1],23,59,59).timestamp())
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=40) as executor:
        cur_tasks = [executor.submit(get_order_users,table,cur_start,cur_end) for table in tables]
        last_tasks = [executor.submit(get_order_users,table,last_start,last_end) for table in tables]
        cur_df = pd.DataFrame(columns=["uid"])
        last_df = cur_df.copy()
        
        for cur_future,last_future in zip(concurrent.futures.as_completed(cur_tasks),concurrent.futures.as_completed(last_tasks)):
            cur_df=cur_df.append(cur_future.result()).reset_index(drop=True)
            last_df = last_df.append(last_future.result()).reset_index(drop=True)
            
        prev_month = last_df.drop_duplicates(subset="uid",keep="first").uid.count()
        cur_month = cur_df.drop_duplicates(subset="uid",keep="first").uid.count()
        result["prev_month"] = prev_month
        result["cur_month"] = cur_month
    return result

def get_order_nums(tuple_uid, month, rr_host, rr_port, rr_user, rr_password):
    """
    获取订单数,销售额
    :param tuple_uid:
    :param month:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    Cg = ChannelTypeConfig.channel_type_config
    tables = [Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]
    orders_list = []
    money_list = []
    for table in tables:
        sql = f"""SELECT COUNT(1) 'order_nums',SUM(payment_amount) 'payment_amount'
                  FROM {table} 
                  WHERE sub_order_status IN (1,2,3) 
                  AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'
                  AND uid IN {tuple_uid}"""
        df = pd.read_sql(sql, conn)
        orders_list.append(df['order_nums'].sum())
        money_list.append(df['payment_amount'].sum())
    order_nums = sum(orders_list)
    payment_amount = sum(money_list)
    conn.close()
    return order_nums, payment_amount


def deal_with_data(rr_host, rr_port, rr_user, rr_password):
    last_month = get_last_month(1)
    last_two_month = get_last_month(2)
    cur_month = get_cur_month()
    if str(datetime.today()).split('-')[2] == '01':
        cur_month = last_month
        last_month = last_two_month
    # 获取上月注册数
    df_last = get_re_users(cur_month, rr_host, rr_port, rr_user, rr_password)
    uid_last_list = df_last['uid'].tolist()
    registered_users_last = len(uid_last_list)
    uid_last_tuple = tuple(uid_last_list)
    # 获取上上月注册数
    df_two_last = get_re_users(last_month, rr_host, rr_port, rr_user, rr_password)
    uid_two_last_list = df_two_last['uid'].tolist()
    registered_users_two_last = len(uid_two_last_list)
    uid_two_last_tuple = tuple(uid_two_last_list)
    # 计算用户注册增长率
    if registered_users_two_last == 0:
        growth_rate_users = 0
    else:
        growth_rate_users = round((registered_users_last - registered_users_two_last) / registered_users_two_last,
                                  2) * 100
    # 获取上月注册用户订单信息
    order_info_last = get_order_nums(uid_last_tuple, cur_month, rr_host, rr_port, rr_user, rr_password)
    order_nums_last = order_info_last[0]
    purchase_amount_last = order_info_last[1]
    # 获取上上月注册用户订单信息
    order_info_two_last = get_order_nums(uid_two_last_tuple, last_month, rr_host, rr_port, rr_user, rr_password)
    order_nums_two_last = order_info_two_last[0]
    purchase_amount_two_last = order_info_two_last[1]
    # 计算消费金额增长率
    if purchase_amount_two_last == 0:
        growth_rate_amount = 0
    else:
        growth_rate_amount = round((purchase_amount_last - purchase_amount_two_last) / purchase_amount_two_last,
                                   4) * 100
    monthly_active_users = 0
    monthly_buyuers = 0
    data_year = cur_month.split('-')[0]
    insert_json = {
        "date": cur_month,
        "registered_users": registered_users_last,
        "growth_rate_users": growth_rate_users,
        "order_nums": order_nums_last,
        "purchase_amount": purchase_amount_last,
        "growth_rate_amount": growth_rate_amount,
        "monthly_buyuers" : monthly_buyuers,
        "monthly_active_users": monthly_active_users,
        "data_year": data_year
    }
    return insert_json


def exits_data(date, cursor):
    """
    验证是否已存在
    :param date: 日期
    :param cursor
    :return:
    """
    sql = f"""select * from vh_sales_analysis_monthly_user where `date` = '{date}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_sales_analysis_monthly_user where `date` = '{date}'"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(insert_json, rm_host, rm_port, rm_user, rm_password,buyuers_count:dict):
    conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
    cursor = conn.cursor()
    dict_keys = list(insert_json.keys())
    exits_data(insert_json[dict_keys[0]], cursor)
    sql = f"""insert into vh_sales_analysis_monthly_user 
             (`date`, `registered_users`, `growth_rate_users`, `order_nums`, `purchase_amount`, `growth_rate_amount`, 
             `monthly_buyuers`,`monthly_active_users`,`data_year`) 
             values('{insert_json[dict_keys[0]]}',{insert_json[dict_keys[1]]},{insert_json[dict_keys[2]]},
             {insert_json[dict_keys[3]]},{insert_json[dict_keys[4]]},{insert_json[dict_keys[5]]},
             {insert_json[dict_keys[6]]},'{insert_json[dict_keys[7]]}','{insert_json[dict_keys[8]]}')"""
    try:
        cursor.execute(sql)
        update_prev_sql = f"UPDATE vh_sales_analysis_monthly_user SET monthly_buyuers = {buyuers_count['prev_month']} WHERE `date` = '{get_last_month(1)}'"
        update_cur_sql = f"UPDATE vh_sales_analysis_monthly_user SET monthly_buyuers = {buyuers_count['cur_month']} WHERE `date` = '{get_cur_month()}'"
        cursor.execute(update_prev_sql)
        cursor.execute(update_cur_sql)
        conn.commit()
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
    finally:
        conn.close()


def handler_register_monthly(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        buyuers_count = get_monthly_buyuers_count()
        insert_json = deal_with_data(rr_host, rr_port, rr_user, rr_password)
        insert_mysql(insert_json, rm_host, rm_port, rm_user, rm_password,buyuers_count)
        return 1
    except Exception as e:
        print(e)
        return -1

class Test(unittest.TestCase):
    def test(self):
        # r = get_monthly_active_user_count("rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com",3306,"vinehoodev","ziAJWCLwOVs29NbB")
        handler_register_monthly("rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com",3306,"vinehoodev","ziAJWCLwOVs29NbB")

        