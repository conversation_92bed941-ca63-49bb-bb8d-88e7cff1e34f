import pymysql
import pandas as pd
from datetime import datetime, timedelta
import warnings
from datetime import datetime
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str
from app.CommonLibraries.DateToWeek import date_to_week

warnings.filterwarnings("ignore")

# 商家秒发用户日统计

def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")



def get_last_day():
    last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    return last_day


def get_re_users(day, rr_host, rr_port, rr_user, rr_password):
    """
    获取注册用户
    :return:
    """
    user_conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_user")
    user_sql = f"""SELECT FROM_UNIXTIME(created_time,'%Y-%m-%d') AS 'date',uid AS 'registrants_number',tripartite_source AS 'delivery_store_log' FROM `vh_user` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}'"""
    df = pd.read_sql(user_sql, user_conn)
    user_conn.close()
    return df


def get_sales(day, registrants_uids, rr_host, rr_port, rr_user, rr_password):
    """
    获取销售数据
    :param day:
    :param table:
    :param tuple_uid:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    if registrants_uids == "*":
        sql = f"""SELECT
                    from_unixtime(created_time,"%Y-%m-%d") AS date,
                    uid AS 'buyuers',
                    delivery_store_id,
                    delivery_store_name,
                    merchant_id,
                    payment_amount AS 'merchant_second_sales' 
                 FROM vh_merchant_second_order
                 WHERE sub_order_status IN (1,2,3) and from_unixtime(created_time,"%Y-%m-%d") = '{day}'"""
    else:
        if len(registrants_uids) == 1:
            sql = f"""SELECT
                    from_unixtime(created_time,"%Y-%m-%d") AS date,
                    uid AS 'order_users',
                    delivery_store_id,
                    delivery_store_name,
                    merchant_id,
                    payment_amount AS 'merchant_second_sales' 
                 FROM vh_merchant_second_order
                 WHERE sub_order_status IN (1,2,3) and from_unixtime(created_time,"%Y-%m-%d") = '{day}' and uid = {registrants_uids[0]}"""
        else:
            sql = f"""SELECT
                    from_unixtime(created_time,"%Y-%m-%d") AS date,
                    uid AS 'order_users',
                    delivery_store_id,
                    delivery_store_name,
                    merchant_id,
                    payment_amount AS 'merchant_second_sales' 
                 FROM vh_merchant_second_order
                 WHERE sub_order_status IN (1,2,3) and from_unixtime(created_time,"%Y-%m-%d") = '{day}' and uid in {registrants_uids}"""
    df = pd.read_sql_query(sql,conn)
    conn.close()
    return df


def get_new_order_users(day,rr_host, rr_port, rr_user, rr_password):

    """
    统计前一天新购用户
    :param time:
    :return:
    """
    orders_conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    start_time=int((pd.to_datetime(day +" "+"00:00:00")-timedelta(hours=8)).timestamp())
    end_time=int((pd.to_datetime(day +" "+"23:59:59")-timedelta(hours=8)).timestamp())
    new_order_users_sql=f"""select from_unixtime(created_time,"%Y-%m-%d") AS date,delivery_store_id,delivery_store_name,merchant_id,`uid` AS 'new_users' from `vh_merchant_second_order` where sub_order_status in (1,2,3) and created_time between {start_time} and {end_time}"""
    new_order_users_main_df=pd.read_sql_query(new_order_users_sql,orders_conn)
    if new_order_users_main_df.empty:
        return pd.DataFrame(columns=["date","delivery_store_id","delivery_store_name","merchant_id","new_users"])
    else:
        new_order_uid_list=tuple(set(new_order_users_main_df.new_users.tolist()))
        if len(new_order_uid_list) == 0:
            old_order_users_df=pd.DataFrame(columns=["date","delivery_store_id","delivery_store_name","merchant_id","new_users"])
        elif len(new_order_uid_list) == 1:
            old_order_users_sql=f"""select `uid` from `vh_merchant_second_order` where sub_order_status in (1,2,3) and created_time < {start_time} and uid = {new_order_uid_list[0]}"""
            old_order_users_df=pd.read_sql_query(old_order_users_sql,orders_conn)
        elif len(new_order_uid_list) > 1:
            old_order_users_sql=f"""select `uid` from `vh_merchant_second_order` where sub_order_status in (1,2,3) and created_time < {start_time} and uid in {new_order_uid_list}"""
            old_order_users_df=pd.read_sql_query(old_order_users_sql,orders_conn)
        now_old_uid=old_order_users_df.uid.tolist()
        if len(now_old_uid) == 0:
            new_order_user=new_order_users_main_df.copy()
        elif len(now_old_uid) == 1:
            new_order_user=new_order_users_main_df[new_order_users_main_df.new_users != now_old_uid[0]]
        if len(now_old_uid) > 1:
            new_order_user=new_order_users_main_df[new_order_users_main_df.new_users.apply(lambda x:x not in tuple(now_old_uid))]
        else:
            pass
        return new_order_user


def get_log_dff(rr_host, rr_port, rr_user, rr_password):
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_data_statistics")
    sql = f""" select delivery_store_log,delivery_store_name,delivery_store_id,merchant_id from vh_merchant_log_record"""
    dff = pd.read_sql_query(sql,conn)
    conn.close()
    return dff


def deal_with_data(last_day,rr_host, rr_port, rr_user, rr_password):
    log_dff = get_log_dff(rr_host, rr_port, rr_user, rr_password)
    log_dff["date"] = last_day
    re_users = get_re_users(last_day, rr_host, rr_port, rr_user, rr_password)
    re_uids = tuple(set(re_users.registrants_number.tolist()))
    re_users = pd.merge(log_dff,re_users,how="left",on=["date","delivery_store_log"])
    re_users = re_users.groupby(by=["date","delivery_store_log","delivery_store_name","delivery_store_id","merchant_id"],as_index=False)["registrants_number"].nunique()
    sales_dff = get_sales(last_day,"*", rr_host, rr_port, rr_user, rr_password)
    sales_buyers_dff = sales_dff.groupby(by=["date","delivery_store_id","delivery_store_name","merchant_id"],as_index=False)["buyuers"].nunique()
    sales_dff = sales_dff.groupby(by=["date","delivery_store_id","delivery_store_name","merchant_id"],as_index=False).agg({"merchant_second_sales":sum})
    sales_dff = pd.merge(sales_buyers_dff,sales_dff,how="left",on=["date","delivery_store_id","delivery_store_name","merchant_id"])
    sales_dff = pd.merge(sales_dff,re_users,how="outer",on=["date","delivery_store_id","delivery_store_name","merchant_id"])
    sales_dff["merchant_id"] = sales_dff.merchant_id.fillna("")
    sales_dff["delivery_store_log"] = sales_dff.delivery_store_log.fillna("")
    sales_dff = sales_dff.fillna(0)
    new_order_users = get_new_order_users(last_day,rr_host, rr_port, rr_user, rr_password)
    new_order_users = new_order_users.groupby(by=["date","delivery_store_id","delivery_store_name","merchant_id"],as_index=False)["new_users"].nunique()
    main_dff = pd.merge(sales_dff,new_order_users,how="left",on=["date","delivery_store_id","delivery_store_name","merchant_id"])
    main_dff["new_users"] = main_dff.new_users.fillna(0)
    if len(re_uids) == 0:
        main_dff["order_users"] = 0
    else:
        order_users_dff = get_sales(last_day,re_uids, rr_host, rr_port, rr_user, rr_password)
        if order_users_dff.empty:
            main_dff["order_users"] = 0
        else:
            order_users_dff = order_users_dff[["date","delivery_store_id","delivery_store_name","merchant_id","order_users"]].groupby(by=["date","delivery_store_id","delivery_store_name","merchant_id"],as_index=False)["order_users"].nunique()
            main_dff = pd.merge(main_dff,order_users_dff,how="left",on=["date","delivery_store_id","delivery_store_name","merchant_id"])
    main_dff["week"] = main_dff.date.apply(lambda x:f"星期{date_to_week(x)}")
    main_dff["month"] = main_dff.date.apply(lambda x:x.split("-")[0] + "-" + x.split("-")[1])
    main_dff["year"] = main_dff.date.apply(lambda x:x.split("-")[0])
    main_dff = main_dff[["date","week","delivery_store_id","delivery_store_name","merchant_id","delivery_store_log","merchant_second_sales","registrants_number","order_users","new_users","buyuers","month","year"]]
    main_dff = main_dff.groupby(by=["date","week","delivery_store_id","delivery_store_name","merchant_id","delivery_store_log"],as_index=False).agg({"merchant_second_sales":sum,"registrants_number":sum,"order_users":sum,"new_users":sum,"buyuers":sum,"month":"first","year":"first"})
    return main_dff




def exits_data(date, delivery_store_id, merchant_id, cursor):
    """
    验证是否已存在
    :param date: 日期
    :param is_liquor:
    :param sell_type
    :return:
    """
    sql = f"""select id from `vh_merchant_second_daily_user` where `date` = '{date}' and `delivery_store_id` = '{delivery_store_id}' and `merchant_id` = '{merchant_id}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from `vh_merchant_second_daily_user` where `date` = '{date}' and `delivery_store_id` = '{delivery_store_id}' and `merchant_id` = '{merchant_id}'"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(*args):
    cursor=args[13]
    exits_data(date=args[0], delivery_store_id=args[2], merchant_id=args[4], cursor=cursor)
    sql = f"""insert into `vh_merchant_second_daily_user`
             (`date`, `week`, `delivery_store_id`, `delivery_store_name`, `merchant_id`, `delivery_store_log`, `merchant_second_sales`,
             `registrants_number`, `order_users`, `new_users`, `buyuers`, `month`, `year`) 
             values('{args[0]}','{args[1]}',{args[2]},'{args[3]}','{args[4]}','{args[5]}',{args[6]},{args[7]},{args[8]},
             {args[9]},{args[10]},'{args[11]}','{args[12]}')"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")



def handler_merchant_second_daily_process(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        last_day = get_last_day()
        dff = deal_with_data(last_day,rr_host, rr_port, rr_user, rr_password)
        data_list = dff.values.tolist()
        for data in data_list:
            insert_mysql(data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8], data[9]
                         , data[10], data[11], data[12],
                         cursor)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()