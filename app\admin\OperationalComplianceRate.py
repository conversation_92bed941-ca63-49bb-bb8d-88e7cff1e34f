# -*- coding: utf-8 -*-
# 运营达标率
import pymysql
import pandas as pd
import requests
from app.CommonLibraries.getConfig import get_config
import json

response = requests.session()


def conn_mysql(host, port, username, password, database):
    """
    链接数据库
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=username, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_operation_complete_info(month, conn_statics):
    """
    获取运营达标相关数据
    :param month:
    :param conn_statics
    :return:
    """
    sql = f"""SELECT * FROM `vh_sales_analysis_target` WHERE date = '{month}'"""
    df = pd.read_sql(sql, conn_statics)
    df = df.drop('id', axis=1)
    res_json = json.loads(df.to_json(orient='records'))
    return res_json
