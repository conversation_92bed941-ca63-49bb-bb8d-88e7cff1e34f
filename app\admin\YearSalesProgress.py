# -*- coding: utf-8 -*-
# 年销售进度
import pymysql
import pandas as pd
import requests
import json

response = requests.session()


def conn_mysql(host, port, username, password, database):
    """
    链接数据库
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=username, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_year_sales_progress(year, sell_type, corp, conn_statics):
    """
    获取年销售进度数据
    :param year:年份
    :param sell_type:销售类型
    :return:
    """
    # config_info = get_config(data_id="db.v3_all", group="vinehoo.accounts")
    # host = config_info['host']
    # port = config_info['port']
    # username = config_info['user']
    # password = config_info['password']
    # conn = conn_mysql(host=host, port=port, username=username, password=password, database="vh_data_statistics")
    where = ""
    if corp != "0":
        where = f""" and corp = '{corp}'"""

    if sell_type != 0:
        where += f""" and type = {sell_type}"""

    sql = f"""SELECT * FROM `vh_sales_analysis_statistics` WHERE data_year = '{year}' {where}"""
    df = pd.read_sql(sql, conn_statics)
    df = df.drop('id', axis=1).drop('data_year', axis=1).drop('im_type', axis=1)

    split = {}
    for index, row in df.iterrows():
        if row['date'] not in split:
            split[row['date']] = []
        split[row['date']].append(row)
    
    result = []
    for date, rows in split.items():
        info = {}
        plan_sales_row = {}
        plan_quantity_row = {}
        for row in rows:
            if sell_type == 0 and row['type'] == 4:
                continue

            for column, value in row.items():
                if column not in info:
                    info[column] = value
                elif isinstance(value, (int, float)):
                    if column == "plan_sales":
                        plan_sales_row[row['type']] = float(value)
                    elif column == "plan_quantity":
                        plan_quantity_row[row['type']] = float(value)
                    else:
                        info[column] = round(info[column] + value ,2)

        info['type'] = sell_type
        if len(rows) > 1:
            plan_sales = 0 
            for k,v in plan_sales_row.items():
                plan_sales = round(plan_sales + v,2)
            info['plan_sales'] = plan_sales
            plan_quantity = 0 
            for k,v in plan_quantity_row.items():
                plan_quantity = round(plan_quantity + v,2)
            info['plan_quantity'] = plan_quantity
            
            # 计划增幅
            if info['plan_sales'] != 0 and info['last_sales'] != 0:
                info["planned_increases_sales"] = (info['plan_sales'] - info['last_sales']) / info['last_sales'] * 100
                info["planned_increases_sales"] = round(info["planned_increases_sales"], 2)
            else:
                info["planned_increases_sales"] = 0

            if info['plan_quantity'] != 0 and info['last_quantity'] != 0:
                info["planned_increases_quantity"] = (info['plan_quantity'] - info['last_quantity']) / info['actual_quantity'] * 100
                info["planned_increases_quantity"] = round(info["planned_increases_quantity"], 2)
            else:
                info["planned_increases_quantity"] = 0

            # 达标率
            if info['actual_sales'] != 0 and info['plan_sales'] != 0:
                info["compliance_sales"] = info['actual_sales'] / info['plan_sales'] * 100
                info["compliance_sales"] = round(info["compliance_sales"], 2)
            else:
                info["compliance_sales"] = 0

            # 实际增幅
            if info['actual_sales'] != 0 and info['last_sales'] != 0:
                info["actual_increase_sales"] = (info['actual_sales'] - info['last_sales']) / info['last_sales'] * 100
                info["actual_increase_sales"] = round(info["actual_increase_sales"], 2)
            else:
                info["actual_increase_sales"] = 0

            # 实际单量增幅
            if info['actual_quantity'] != 0 and info['last_quantity'] != 0:
                info["actual_increase_quantity"] = (info['actual_quantity'] - info['last_quantity']) / info['last_quantity'] * 100
                info["actual_increase_quantity"] = round(info["actual_increase_quantity"], 2)
            else:
                info["actual_increase_quantity"] = 0

            # 客单价增幅
            if info['last_sales'] != 0 and info['last_quantity'] != 0:
                info["unit_price_last"] = info['last_sales'] / info['last_quantity']
                info["unit_price_last"] = round(info["unit_price_last"], 2)
            else:
                info["unit_price_last"] = 0

            if info['unit_price'] != 0 and info['unit_price_last'] != 0:
                info["unit_price_increase"] = (info['unit_price'] - info['unit_price_last']) / info['unit_price_last'] * 100
                info["unit_price_increase"] = round(info["unit_price_increase"], 2)
            else:
                info["unit_price_increase"] = 0
        result.append(info)

    # res_json = json.dumps(result)
    return result
    # res_json = json.loads(result.to_json(orient='records'))
    # return res_json






