#coding:utf-8
'''
    用户下单频次画像
'''

import pandas as pd
from sqlalchemy import create_engine
import matplotlib.pyplot as plt
from io import BytesIO
import base64
from app.CommonLibraries.getConfig import get_config
import json


rr_config = get_config("db.data.v3_all", "vinehoo.accounts")
rr_host = rr_config['host']
rr_port = rr_config['port']
rr_user = rr_config['user']
rr_password = rr_config['password']


def read_mysql_data(uid, start_date):
    """
    从 MySQL 数据库中读取数据
    
    参数：
    uid: int，用户 ID
    start_date: str，开始日期，格式为 'YYYY-MM-DD'
    
    返回值：
    DataFrame，包含查询结果
    """

    user = rr_user
    password = rr_password
    host = rr_host
    port = rr_port
    database = "vh_orders"
    
    # 创建 SQLAlchemy 引擎
    engine = create_engine(f'mysql+pymysql://{user}:{password}@{host}:{port}/{database}')
    
    # 构建 SQL 查询语句
    sql = f"""
    SELECT DATE(FROM_UNIXTIME(payment_time)) AS date,
           SUM(total) AS total
    FROM (
      SELECT payment_time, COUNT(*) AS total
      FROM vh_flash_order
      WHERE uid = {uid}
        AND payment_time >= UNIX_TIMESTAMP('{start_date}')
        AND sub_order_status in (1,2,3)
        AND payment_amount - refund_money != 0
      GROUP BY payment_time
      UNION ALL
      SELECT payment_time, COUNT(*) AS total
      FROM vh_second_order
      WHERE uid = {uid}
        AND payment_time >= UNIX_TIMESTAMP('{start_date}')
        AND sub_order_status in (1,2,3)
        AND payment_amount - refund_money != 0
      GROUP BY payment_time
      UNION ALL
      SELECT payment_time, COUNT(*) AS total
      FROM vh_cross_order
      WHERE uid = {uid}
        AND payment_time >= UNIX_TIMESTAMP('{start_date}')
        AND sub_order_status in (1,2,3)
        AND payment_amount - refund_money != 0
      GROUP BY payment_time
      UNION ALL
      SELECT payment_time, COUNT(*) AS total
      FROM vh_tail_order
      WHERE uid = {uid}
        AND payment_time >= UNIX_TIMESTAMP('{start_date}')
        AND sub_order_status in (1,2,3)
        AND payment_amount - refund_money != 0
      GROUP BY payment_time
      
    ) AS t
    GROUP BY date;
    """
    
    # 使用 pandas 读取 MySQL 数据
    df = pd.read_sql(sql, engine)
    
    # 关闭连接
    engine.dispose()
    
    # 返回结果
    return df

def plot_order_frequency(orders):
    """
    绘制每月订单数量的时间序列图
    
    参数：
    orders: DataFrame，包含订单数据
    
    返回值：
    None
    """
    if orders.empty:
        return {"error_code": -1, "error_msg": "用户订单数据为空", "status": "success", "data": []}
    else:
      # 将日期和时间转换为 datetime 格式
      orders['date'] = pd.to_datetime(orders['date'])
      
      # 按日期对订单进行分组，并计算每天的订单数量
      daily_orders = orders.resample('D', on='date').count()
      
      # 将数据聚合到每月，并计算每月的订单数量
      monthly_orders = daily_orders.resample('M').sum()
      
      # 绘制时间序列图
      plt.figure(figsize=(15, 6))
      plt.plot(monthly_orders.index, monthly_orders['total'])
      plt.xlabel('Date')
      plt.ylabel('Number of orders')
      plt.title('Order frequency')
  #     plt.show()
      # 将图像转换为Base64格式的字符串
      buffer = BytesIO()
      plt.savefig(buffer, format='png')
      buffer.seek(0)
      image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

      # 输出Base64格式的字符串
      return {"error_code":0, "error_msg":"","data":f"data:image/png;base64,{image_base64}"}


'''
    获取用户下单画像主函数
    @param uid 用户UID
    @param start_date 开始日期，格式：2020-01-01
'''
def get_user_order_frequency(uid: int, start_date:str) -> dict:
    return plot_order_frequency(read_mysql_data(uid=uid, start_date=start_date))