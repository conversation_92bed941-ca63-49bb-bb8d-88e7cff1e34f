import json
import pymysql
import warnings
import pandas as pd
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str
from app.CommonLibraries.getConfig import get_config

warnings.filterwarnings("ignore")

def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine
    
def get_write_config():
    rm_config = get_config("db.data.write.v3_all", "vinehoo.accounts")
    rm_host,rm_port,rm_user,rm_password = rm_config['host'],rm_config['port'],rm_config['user'],rm_config['password']
    return rm_host,rm_port,rm_user,rm_password

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    
def get_vest_comment_config():
    vest_admin_sql = f""" select id as 'admin_id',realname,dept_id,department from vh_admins where status = 1 and is_delete = 1 and dept_id is not null and dept_id != '' """
    depatment_sql = f""" select dept_id,department from vh_admins where status = 1 and is_delete = 1 and dept_id is not null and dept_id != '' group by `dept_id` """
    vest_admin_conn = nopool_conn("vh_authority").connect()
    depatment_dff = pd.read_sql_query(depatment_sql,vest_admin_conn)
    vest_admin_dff = pd.read_sql_query(vest_admin_sql,vest_admin_conn)
    vest_admin_conn.close()
    nopool_conn("vh_authority").dispose()
    vest_admin_dff["dept_id"] = vest_admin_dff.dept_id.apply(lambda x:json.loads(x)[0])
    depatment_dff["dept_id"] = depatment_dff.dept_id.apply(lambda x:json.loads(x)[0])
    return vest_admin_dff,depatment_dff

def exits_data(id_log, insert_type,cursor):
    """
    验证是否已存在
    :param admin_id:管理员ID
    :return:
    """
    if insert_type == 1:
        sql = f"""select admin_id from `vh_vest_comment_config` where `admin_id` = {id_log}"""
        cursor.execute(sql)
        data = cursor.fetchone()
        if data is not None:
            delete_sql = f"""delete from `vh_vest_comment_config` where `admin_id` = {id_log}"""
            cursor.execute(delete_sql)
    elif insert_type == 2:
        sql = f"""select dept_id from `vh_department_list` where `dept_id` = {id_log}"""
        cursor.execute(sql)
        data = cursor.fetchone()
        if data is not None:
            delete_sql = f"""delete from `vh_department_list` where `dept_id` = {id_log}"""
            cursor.execute(delete_sql)
    else:
        pass

def insert_mysql(*args):
    insert_type=args[0]
    cursor=args[1]
    if insert_type == 1:
        open_type=args[6]
        exits_data(id_log=args[2], insert_type=insert_type,cursor=cursor)
        if open_type == 0:
            sql = f"""insert into `vh_vest_comment_config`
                     (`admin_id`, `realname`, `dept_id`, `department`,`is_open`) 
                     values({args[2]},'{args[3]}','{args[4]}','{args[5]}',0)"""
        elif open_type == 1:
            sql = f"""insert into `vh_vest_comment_config`
                     (`admin_id`, `realname`, `dept_id`, `department`,`is_open`) 
                     values({args[2]},'{args[3]}','{args[4]}','{args[5]}',1)"""
    elif insert_type == 2:
        exits_data(id_log=args[2], insert_type=insert_type,cursor=cursor)
        sql = f"""insert into `vh_department_list`
                 (`dept_id`, `department`) 
                 values('{args[2]}','{args[3]}')"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")

def handler_vest_comment_config_process():
    """
    入口函数
    :return:
    """
    try:
        vest_conn = nopool_conn("vh_data_statistics").connect()
        vest_comment_sql = f""" select admin_id,is_open from vh_vest_comment_config"""
        vest_comment_dff = pd.read_sql_query(vest_comment_sql,vest_conn)
        vest_conn.close()
        nopool_conn("vh_data_statistics").dispose()
        vest_admin_dff = get_vest_comment_config()[0]
        depatment_dff = get_vest_comment_config()[1]
        vest_admin_list = vest_admin_dff.values.tolist()
        depatment_list = depatment_dff.values.tolist()
        rm_host, rm_port, rm_user, rm_password = get_write_config()
        conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        for vest_admin in vest_admin_list:
            if vest_comment_dff[vest_comment_dff.admin_id == vest_admin[0]].empty:
                insert_mysql(1,cursor,vest_admin[0], vest_admin[1], vest_admin[2], vest_admin[3],0)
            else:
                open_type = vest_comment_dff[vest_comment_dff.admin_id == vest_admin[0]].is_open.iloc[0]
                insert_mysql(1,cursor,vest_admin[0], vest_admin[1], vest_admin[2], vest_admin[3],open_type)
        conn.commit()
        conn.close()
        conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        for depatment in depatment_list:
            insert_mysql(2,cursor,depatment[0], depatment[1])
        conn.commit()
        conn.close()
        return 1
    except Exception as e:
        print(e)
        return -1    
    
def update_open_state(admin_id,is_open):
    rm_host, rm_port, rm_user, rm_password = get_write_config()
    conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
    cursor = conn.cursor()
    open_sql = f""" update `vh_vest_comment_config` set `is_open` = {is_open} where `admin_id` = {admin_id}"""
    try:
        cursor.execute(open_sql)
        conn.commit()
        conn.close()
        return "ok"
    except Exception as e:
        return {"error_code": -1, "error_msg": f"马甲评论人员开启失败,失败信息:{e}", "status": "fail"}

