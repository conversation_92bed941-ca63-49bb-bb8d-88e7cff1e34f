from app.keywords.mysql import query_data
from flask_restful import Api, Resource, reqparse, request
import uvicorn
import pytz
from datetime import datetime, timedelta
from collections import defaultdict


class QuerySearchKeywordsList(Resource):
    def get(self):
        try:
            # 使用 request.args 获取 GET 请求的参数
            date = request.args.get('date', '')
            search_status = str(request.args.get('search_status', ''))
            page = int(request.args.get('page', 1))
            limit = int(request.args.get('limit', 10))

            if not date:
                return {"error_code":-1,"error_msg":"查询日期不能为空"}

            offset_main = (page - 1) * limit

            where_clauses = []
            if date != '':
                where_clauses.append(f"date = '{date}'")


            where_clause = "WHERE " + " AND ".join(where_clauses) if where_clauses else ""

            
            sql = f'SELECT * FROM vh_search_keywords {where_clause} order by search_count desc'
            data = query_data('vh_data_statistics', sql)

            # 总条数
            total = len(data)

            # 有结果数
            with_results_count = 0
            # 有结果率
            with_results_rate = 0
            if len(data) > 0:
                for v in data:
                    if v['status'] == 0:
                        with_results_count += 1
            if with_results_count>0:
                with_results_rate = round(with_results_count/total, 2) * 100

            list_result = []
            for v in data:
                if search_status != '' and search_status != str(v['status']):
                    continue

                list_result.append({
                    "id":v['id'],
                    "date":str(v['date']),
                    "keywords":v['keywords'],
                    "search_count":v['search_count'],
                    "status":v['status'],
                })

            # 获取分页数据
            datalist = list_result[offset_main: offset_main + limit]

            result = {
                "error_code":0,
                "error_msg":"",
                "data":{
                    "total":len(list_result),
                    "total_count":total,
                    "with_results_count":with_results_count,
                    "with_results_rate":f"{with_results_rate}%",
                    "list":datalist,
                },
            }

            return result
        
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}

class QuerySearchKeywordsCurve(Resource):
    def get(self):
        # 获取昨天的日期并设置时区
        local_tz = pytz.timezone('Asia/Shanghai')
        currentday = datetime.now()
        # 使用 request.args 获取 GET 请求的参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        if not start_date or not end_date:
            return {"error_code":-1,"error_msg":"开始时间或结束时间不能为空"}
        
        start_date = currentday.strptime(start_date, "%Y-%m-%d")
        end_date = currentday.strptime(end_date, "%Y-%m-%d")
        start_time = start_date.timestamp()
        end_time = end_date.timestamp()
        dateList = [start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)]
        date_list = [date.strftime("%Y-%m-%d") for date in dateList]

        # 查询范围所有数据
        sql = f"SELECT * FROM vh_search_keywords_statistics where date between '{start_time}' and '{end_time}' order by date asc"

        data = query_data('vh_data_statistics', sql)
        data_map = defaultdict(lambda: {"total_count": 0, "with_results_count": 0, "with_results_rate": "0%"})
        for val in data:
            # 将时间戳转换为日期对象
            date_object = datetime.fromtimestamp(val['date'], tz=pytz.utc).astimezone(local_tz).date()
            # 将日期对象格式化为字符串
            date_string = date_object.strftime('%Y-%m-%d')
            data_map[date_string] = {
                "total_count":val['total_count'],
                "with_results_count":val['with_results_count'],
                "with_results_rate":val['with_results_rate'],
            }

        row = {}
        for v in date_list:
            row[v] = {'date': str(v), 'total': 0, 'with_results_count': 0, 'with_results_rate':'0%'}

            if data_map.get(v):
                row[v]['total'] = data_map[v]['total_count']
                row[v]['with_results_count'] = data_map[v]['with_results_count']
                row[v]['with_results_rate'] = f"{data_map[v]['with_results_rate']}%"
            


        result = {
            "error_code":0,
            "error_msg":"",
            "data":{
                "list":row,
            },
        }
        return result

