import json
import pymysql
import warnings
import concurrent
import pandas as pd
from datetime import datetime, timedelta
from app.DButils.MysqlHelper import conn_str
from sqlalchemy import create_engine

warnings.filterwarnings("ignore")


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def get_starttimestamp(time_str):
    start = int(datetime.strptime(f"{time_str} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp())
    return start

def get_endtimestamp(time_str):
    end = int(datetime.strptime(f"{time_str} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
    return end

def get_users(start_stamp,end_stamp,logo):
    user_sql = f""" select uid,from_unixtime(created_time) as 'users_createdtime',telephone_encrypt,tripartite_source from vh_user where tripartite_source like '%%{logo}%%' and created_time between {start_stamp} and {end_stamp} """
    users_conn = nopool_conn("vh_user").connect()
    user_df = pd.read_sql_query(user_sql,users_conn)
    users_conn.close()
    nopool_conn("vh_user").dispose()
    return user_df

def get_province():
    """
    获取省份
    :return:
    """
    sql = f"""SELECT id 'province_id',`name` 'province' FROM `vh_regional` WHERE type = 0"""
    users_conn = nopool_conn("vh_user").connect()
    df = pd.read_sql(sql, users_conn)
    users_conn.close()
    nopool_conn("vh_user").dispose()
    return df


def get_sales(start_int,end_int,table):
    """
    获取销售数据
    :param day:
    :param table:
    :return:
    """
    sql = f"""SELECT b.province_id, a.payment_amount, a.order_qty, a.uid 
              FROM {table} a 
              LEFT JOIN vh_order_main b ON a.main_order_id = b.id 
              WHERE a.created_time BETWEEN {start_int} AND {end_int}
              AND a.sub_order_status IN( 1, 2, 3 )"""
    orders_conn = nopool_conn("vh_orders").connect()
    df = pd.read_sql(sql, orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return df


def collect_data(start_str,end_str):
    """
    处理数据
    :return:
    """
    
    # 商家秒发
    sales_info = get_sales(get_starttimestamp(start_str),get_endtimestamp(end_str),'vh_merchant_second_order')
    if sales_info.empty:
        dff_end_merchant_second = pd.DataFrame(columns=["province","merchant_second_sales","merchant_second_orders","merchant_second_proportion","merchant_second_buy_users"])
    province_info = get_province()
    # 销售额
    df_sales = sales_info[['province_id', 'payment_amount']].groupby('province_id').sum().reset_index()
    if sales_info.empty:
        df_sales['payment_amount'] = 0
    # 销售数量
    df_nums = sales_info[['province_id']].groupby('province_id').size().reset_index().rename(columns={0: 'orders'})
    # 销售人数
    df_users = sales_info[['province_id', 'uid']].groupby(['province_id', 'uid']).size().reset_index().rename(
        columns={0: 'sale_users'}).groupby('province_id').size().reset_index().rename(columns={0: 'sale_users'})
    dff = pd.merge(df_sales, df_nums, how='left').merge(df_users, how='left')
    dff_end = pd.merge(dff, province_info, how='left')
    dff_end_merchant_second = dff_end.rename(
        columns={'payment_amount': 'merchant_second_sales', 'orders': 'merchant_second_orders', 'sale_users': 'merchant_second_buy_users'})
    total_sales = df_sales['payment_amount'].sum()
    dff_end_merchant_second['merchant_second_proportion'] = dff_end_merchant_second['merchant_second_sales'].apply(
        lambda x: round(x / total_sales, 4) * 100)
    dff_end_merchant_second = dff_end_merchant_second[["province","merchant_second_sales","merchant_second_orders","merchant_second_proportion","merchant_second_buy_users"]]
    return dff_end_merchant_second


def New_user_data(start_str,end_str):
    start_stamp,end_stamp = get_starttimestamp(start_str),get_endtimestamp(end_str)
    logos = ["jichangdian","kexiluwankedian","lvchengbaihedian","xjyuyuandian","yangzijiangludian","zhongyabeiludian","cqguanghuandian","md"]
    with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
        user_tasks = [executor.submit(get_users,start_stamp,end_stamp,logo) for logo in logos]
        User_Data = pd.DataFrame(columns=["uid","users_createdtime","telephone_encrypt","tripartite_source"])
        for future in concurrent.futures.as_completed(user_tasks):
            User_Data=User_Data.append(future.result()).reset_index(drop=True)
    log_sql = f""" select delivery_store_log as 'merchant_log',delivery_store_name as 'merchant_name' from vh_merchant_log_record """
    statistics_conn = nopool_conn("vh_data_statistics").connect()
    log_dff = pd.read_sql_query(log_sql,statistics_conn)
    log_dff = log_dff.set_index("merchant_log")
    log_dict = log_dff.T.to_dict("list")
    User_Data["tripartite_source"] = User_Data.tripartite_source.apply(lambda x:log_dict[x][0])
    User_Data["users_createdtime"] = User_Data.users_createdtime.apply(lambda x:str(x))
    return User_Data
    

def get_merchantsecondregion(start_str,end_str,is_collect):
    if is_collect == 0:
        dff = New_user_data(start_str,end_str)
    elif is_collect == 1:
        dff = collect_data(start_str,end_str)
    total = len(dff)
    res_json = {"orders":json.loads(dff.to_json(orient="records")),
                                "total":total
                                }
    return res_json