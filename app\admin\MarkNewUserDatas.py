import pandas as pd
import warnings
import json
import time
import pymysql 
from config import ChannelTypeConfig
from app.CommonLibraries.getConfig import get_config

warnings.filterwarnings("ignore")

config_read_info = get_config("db.data.v3_all", "vinehoo.accounts")
mysql_read_host = config_read_info['host']
mysql_read_port = config_read_info['port']
mysql_read_user = config_read_info['user']
mysql_read_password = config_read_info['password']


def conn_mysql(host, port, user, password, database):
    """
    链接数据库
    :param host:
    :param port:
    :param user:
    :param password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_uid_status(uids,ordesr_tables,reconn_orders):
    """
    获取下单记录
    :param uids:客户id元组
    :param ordesr_tables:订单表pool
    :return:
    """
    try:
        count = -1
        for table in ordesr_tables:
            count += 1
            if len(uids) == 1:
                sub_order_sql = f"""
                                select distinct uid,created_time 'order_created_time' from {table} where uid = {uids[0]} and sub_order_status in (1,2,3) and  payment_amount - refund_money != 0

                                """
            elif len(uids) > 1:
                sub_order_sql = f"""
                                select distinct uid,created_time 'order_created_time' from {table} where uid in {uids} and sub_order_status in (1,2,3) and  payment_amount - refund_money != 0

                                """
            else:
                pass
            sub_order_df = pd.read_sql_query(sub_order_sql,reconn_orders)
            if count == 0:
                sub_order_flash = sub_order_df
            elif count == 1:
                sub_order_cross = sub_order_df
            elif count == 2:
                sub_order_second = sub_order_df
            elif count == 3:
                sub_order_tail = sub_order_df
            else:
                pass
        sub_orders_df = pd.concat([sub_order_flash,sub_order_cross,sub_order_second,sub_order_tail],axis=0)
        if sub_orders_df.empty:
            return 0
        else:
            return sub_orders_df
    except Exception as e:
        return {"error_code": -1, "error_msg": f"下单记录获取失败{e}", "status": "fail", "data":[]}


def get_commodities(period_tup,reconn_commodities):
    """
    获取商品信息
    :param period_tup:期数元组
    """
    try:
        commodities_pool = [ChannelTypeConfig.channel_type_config[1][1],ChannelTypeConfig.channel_type_config[2][1],ChannelTypeConfig.channel_type_config[3][1],ChannelTypeConfig.channel_type_config[4][1]]
        count=-1
        for commoditie in commodities_pool:
            count+=1
            if len(period_tup) > 1:
                commodities_sql=f"select id,title from {commoditie} where id in {period_tup}"
            elif len(period_tup) == 1:
                commodities_sql=f"select id,title from {commoditie} where id = {period_tup[0]}"
            else:
                pass
            commodities_df=pd.read_sql_query(commodities_sql,reconn_commodities)
            if count==0:
                commodities_flash=commodities_df
            elif count==1:
                commodities_cross=commodities_df
            elif count==2:
                commodities_second=commodities_df
            elif count==3:
                commodities_leftover=commodities_df
            else:
                pass
        commodities_main=pd.concat([commodities_flash,commodities_cross,commodities_second,commodities_leftover],axis=0)
        if commodities_main.empty:
            return 0
        else:
            return commodities_main
    except Exception as e:
        return {"error_code": -1, "error_msg": f"商品信息获取失败{e}", "status": "fail", "data":[]}


def get_regional_name(regional_id_tup,reconn_user):
    """
    根据地区id获取地区名称
    :param regional_id_tup:区域id元组
    :return:    
    """
    try:
        if len(regional_id_tup) > 1:
            regional_name_sql=f"select id,`name` from vh_regional where id in {regional_id_tup}"
        elif len(regional_id_tup) == 1:
            regional_name_sql=f"select id,`name` from vh_regional where id = {regional_id_tup[0]}"
        else:
            pass
        regional_name_df= pd.read_sql_query(regional_name_sql, reconn_user)
        if regional_name_df.empty:
            return 0
        else:
            return regional_name_df
    except Exception as e:
        return {"error_code": -1, "error_msg": f"地区信息获取失败{e}", "status": "fail", "data":[]}

def GetOutOrder(time_range,source_type,is_new_user,is_validorder,period,page,page_nums,out_type):
    """
    主页数据入口
    :param time_range: 时间区间
    :param source_type: 投放渠道
    :param is_new_user: 是否为新顾客(1-是,0-否)
    :param is_validorder: 是否为有效订单(1-是,0-否)
    :param out_type: 输出格式(json,data)
    :return:    
    """
    try:
        reconn_statistics = conn_mysql(mysql_read_host, mysql_read_port, mysql_read_user, mysql_read_password, "vh_data_statistics")
        reconn_orders = conn_mysql(mysql_read_host, mysql_read_port, mysql_read_user, mysql_read_password, "vh_orders")
        reconn_user = conn_mysql(mysql_read_host, mysql_read_port, mysql_read_user, mysql_read_password, "vh_user")
        reconn_commodities = conn_mysql(mysql_read_host, mysql_read_port, mysql_read_user, mysql_read_password, "vh_commodities")
        ordesr_tables = [ChannelTypeConfig.channel_type_config[1][0],ChannelTypeConfig.channel_type_config[2][0],ChannelTypeConfig.channel_type_config[3][0],ChannelTypeConfig.channel_type_config[4][0]]
        mark_sql = f""" select platformmark,eventmark,source_type from vh_nowuser_mark where is_open = 1"""
        mark_df = pd.read_sql_query(mark_sql,reconn_statistics)
        if mark_df.empty:
            {"error_code": -1, "error_msg": "未发现标识信息,请确保标识已录入并开启!", "total": 0, "data":[]}
        else:
            mark_df["mark"] = mark_df.platformmark + "-" + mark_df.eventmark
            mark_list = tuple(mark_df.mark.tolist())
            if source_type != "":
                mark_dict = {}
                for k,v in zip(mark_df.eventmark,mark_df.mark):
                    mark_dict[k] = v
                mark = mark_dict[source_type]
            else:
                pass
            begin_time=time_range[0].split("T")[0]
            over_time=time_range[1].split("T")[0]
            if begin_time == "" and over_time != "":
                o_time_str = f"{over_time} 23:59:59"
                o_time = pd.to_datetime(o_time_str)
                last_time = int(time.mktime(o_time.timetuple()))
            elif begin_time != "" and over_time == "":
                b_time_str = f"{begin_time} 00:00:00"
                b_time = pd.to_datetime(b_time_str)
                first_time = int(time.mktime(b_time.timetuple()))
            elif begin_time != "" and over_time != "":
                b_time_str = f"{begin_time} 00:00:00"
                o_time_str = f"{over_time} 23:59:59"
                b_time = pd.to_datetime(b_time_str)
                o_time = pd.to_datetime(o_time_str)
                first_time = int(time.mktime(b_time.timetuple()))
                last_time = int(time.mktime(o_time.timetuple()))
            else:
                pass
            if source_type == "":
                """
                获取主订单id
                """
                if len(mark_list) == 1:
                    main_id_sql=f"""
                            select main_order_id from vh_order_source_log where concat(source_platform,'-',source_event) = '{mark_list[0]}'

                            """
                elif len(mark_list) > 1:
                    main_id_sql=f"""
                                select main_order_id from vh_order_source_log where concat(source_platform,'-',source_event) in {mark_list}

                                """
                else:
                    pass
            else:
                main_id_sql=f"""
                            select main_order_id from vh_order_source_log where concat(source_platform,'-',source_event) = '{mark}'

                            """
            main_id_df=pd.read_sql_query(main_id_sql,reconn_orders)
            main_id = tuple(main_id_df.main_order_id.tolist())
            if len(main_id) == 0:
                return {"orders":[],"total":0}
            else:
                """
                获取订单信息
                """
                if len(main_id) == 1:
                    main_order_sql = f"""
                            select id,province_id,city_id,district_id,address from vh_order_main where id = {main_id[0]}
                                        
                                """
                else:
                    main_order_sql = f"""
                            select id,province_id,city_id,district_id,address from vh_order_main where id in {main_id}
                                        
                                """
                main_order_df = pd.read_sql_query(main_order_sql,reconn_orders)
                if main_order_df.empty:
                    return {"orders":[],"total":0}
                else:
                    pass
                if period == "":
                    if is_validorder == "":
                        count = -1
                        for table in ordesr_tables:
                            count += 1
                            if begin_time == "" and over_time != "":
                                if len(main_id) == 1:
                                     sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and created_time <= {last_time} and payment_amount - refund_money != 0

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and created_time <= {last_time} and payment_amount - refund_money != 0

                                        """
                            elif begin_time != "" and over_time == "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and created_time >= {first_time} and payment_amount - refund_money != 0

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and created_time >= {first_time} and payment_amount - refund_money != 0

                                        """
                            elif begin_time != "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0

                                        """
                            else:
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and payment_amount - refund_money != 0

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and payment_amount - refund_money != 0

                                        """
                            sub_order_df = pd.read_sql_query(sub_order_sql,reconn_orders)
                            is_val = []
                            for val in sub_order_df.sub_order_status:
                                if val in (1,2,3):
                                    va = 1
                                else:
                                    va = 0
                                is_val.append(va)
                            sub_order_df["is_validorder"] = is_val
                            sub_order_df.drop("sub_order_status",axis=1,inplace=True)
                            if count == 0:
                                sub_order_flash = sub_order_df
                            elif count == 1:
                                sub_order_cross = sub_order_df
                            elif count == 2:
                                sub_order_second = sub_order_df
                            elif count == 3:
                                sub_order_tail = sub_order_df
                            else:
                                pass
                        sub_orders_df = pd.concat([sub_order_flash,sub_order_cross,sub_order_second,sub_order_tail],axis=0)
                        if sub_orders_df.empty:
                            return {"orders":[],"total":0}
                        else:
                            pass
                        main_orders_dff = pd.merge(sub_orders_df,main_order_df,how="left",left_on="main_order_id",right_on="id")
                    elif is_validorder == "0":
                        count = -1
                        for table in ordesr_tables:
                            count += 1
                            if begin_time == "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                        """
                            elif begin_time != "" and over_time == "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                        """
                            elif begin_time != "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                        """
                            else:
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                                """
                                else:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and payment_amount - refund_money != 0 and sub_order_status in (0,4)

                                                """
                            sub_order_df = pd.read_sql_query(sub_order_sql,reconn_orders)
                            sub_order_df["is_validorder"] = 0
                            if count == 0:
                                sub_order_flash = sub_order_df
                            elif count == 1:
                                sub_order_cross = sub_order_df
                            elif count == 2:
                                sub_order_second = sub_order_df
                            elif count == 3:
                                sub_order_tail = sub_order_df
                            else:
                                pass
                        sub_orders_df = pd.concat([sub_order_flash,sub_order_cross,sub_order_second,sub_order_tail],axis=0)
                        if sub_orders_df.empty:
                            return {"orders":[],"total":0}
                        else:
                            pass
                        main_orders_dff = pd.merge(sub_orders_df,main_order_df,how="left",left_on="main_order_id",right_on="id")
                    elif is_validorder == "1":
                        count = -1
                        for table in ordesr_tables:
                            count += 1
                            if begin_time == "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                        """
                            elif begin_time != "" and over_time == "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                        """
                            elif begin_time != "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                                """
                                else:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                                """
                            else:
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                                """
                                else:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3)

                                                """
                            sub_order_df = pd.read_sql_query(sub_order_sql,reconn_orders)
                            sub_order_df["is_validorder"] = 1
                            if count == 0:
                                sub_order_flash = sub_order_df
                            elif count == 1:
                                sub_order_cross = sub_order_df
                            elif count == 2:
                                sub_order_second = sub_order_df
                            elif count == 3:
                                sub_order_tail = sub_order_df
                            else:
                                pass
                        sub_orders_df = pd.concat([sub_order_flash,sub_order_cross,sub_order_second,sub_order_tail],axis=0)
                        if sub_orders_df.empty:
                            return {"orders":[],"total":0}
                        else:
                            pass
                        main_orders_dff = pd.merge(sub_orders_df,main_order_df,how="left",left_on="main_order_id",right_on="id")
                    else:
                        pass
                else:
                    if is_validorder == "":
                        count = -1
                        for table in ordesr_tables:
                            count += 1
                            if begin_time == "" and over_time != "":
                                if len(main_id) == 1:
                                     sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and created_time <= {last_time} and payment_amount - refund_money != 0 and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and created_time <= {last_time} and payment_amount - refund_money != 0 and period = {period}

                                        """
                            elif begin_time != "" and over_time == "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and created_time >= {first_time} and payment_amount - refund_money != 0 and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and created_time >= {first_time} and payment_amount - refund_money != 0 and period = {period}

                                        """
                            elif begin_time != "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and period = {period}

                                        """
                            else:
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id = {main_id[0]} and payment_amount - refund_money != 0 and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money,sub_order_status from {table} where main_order_id in {main_id} and payment_amount - refund_money != 0 and period = {period}

                                        """
                            sub_order_df = pd.read_sql_query(sub_order_sql,reconn_orders)
                            is_val = []
                            for val in sub_order_df.sub_order_status:
                                if val in (1,2,3):
                                    va = 1
                                else:
                                    va = 0
                                is_val.append(va)
                            sub_order_df["is_validorder"] = is_val
                            sub_order_df.drop("sub_order_status",axis=1,inplace=True)
                            if count == 0:
                                sub_order_flash = sub_order_df
                            elif count == 1:
                                sub_order_cross = sub_order_df
                            elif count == 2:
                                sub_order_second = sub_order_df
                            elif count == 3:
                                sub_order_tail = sub_order_df
                            else:
                                pass
                        sub_orders_df = pd.concat([sub_order_flash,sub_order_cross,sub_order_second,sub_order_tail],axis=0)
                        if sub_orders_df.empty:
                            return {"orders":[],"total":0}
                        else:
                            pass
                        main_orders_dff = pd.merge(sub_orders_df,main_order_df,how="left",left_on="main_order_id",right_on="id")
                    elif is_validorder == "0":
                        count = -1
                        for table in ordesr_tables:
                            count += 1
                            if begin_time == "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                        """
                            elif begin_time != "" and over_time == "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                        """
                            elif begin_time != "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                        """
                            else:
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                                """
                                else:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and payment_amount - refund_money != 0 and sub_order_status in (0,4) and period = {period}

                                                """
                            sub_order_df = pd.read_sql_query(sub_order_sql,reconn_orders)
                            sub_order_df["is_validorder"] = 0
                            if count == 0:
                                sub_order_flash = sub_order_df
                            elif count == 1:
                                sub_order_cross = sub_order_df
                            elif count == 2:
                                sub_order_second = sub_order_df
                            elif count == 3:
                                sub_order_tail = sub_order_df
                            else:
                                pass
                        sub_orders_df = pd.concat([sub_order_flash,sub_order_cross,sub_order_second,sub_order_tail],axis=0)
                        if sub_orders_df.empty:
                            return {"orders":[],"total":0}
                        else:
                            pass
                        main_orders_dff = pd.merge(sub_orders_df,main_order_df,how="left",left_on="main_order_id",right_on="id")
                    elif is_validorder == "1":
                        count = -1
                        for table in ordesr_tables:
                            count += 1
                            if begin_time == "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time <= {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                        """
                            elif begin_time != "" and over_time == "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                        """
                                else:
                                    sub_order_sql = f"""
                                        select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time >= {first_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                        """
                            elif begin_time != "" and over_time != "":
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                                """
                                else:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and created_time between {first_time} and {last_time} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                                """
                            else:
                                if len(main_id) == 1:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id = {main_id[0]} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                                """
                                else:
                                    sub_order_sql = f"""
                                                select main_order_id,from_unixtime(created_time) '下单时间',created_time,period,uid,order_qty,payment_amount - refund_money from {table} where main_order_id in {main_id} and payment_amount - refund_money != 0 and sub_order_status in (1,2,3) and period = {period}

                                                """
                            sub_order_df = pd.read_sql_query(sub_order_sql,reconn_orders)
                            sub_order_df["is_validorder"] = 1
                            if count == 0:
                                sub_order_flash = sub_order_df
                            elif count == 1:
                                sub_order_cross = sub_order_df
                            elif count == 2:
                                sub_order_second = sub_order_df
                            elif count == 3:
                                sub_order_tail = sub_order_df
                            else:
                                pass
                        sub_orders_df = pd.concat([sub_order_flash,sub_order_cross,sub_order_second,sub_order_tail],axis=0)
                        if sub_orders_df.empty:
                            return {"orders":[],"total":0}
                        else:
                            pass
                        main_orders_dff = pd.merge(sub_orders_df,main_order_df,how="left",left_on="main_order_id",right_on="id")
                    else:
                        pass
                main_orders_dff.sort_values(by=["下单时间"],ascending=False,axis = 0,inplace = True)
                """
                获取顾客信息
                """
                
                uids = tuple(set(main_orders_dff.uid.tolist()))
                if len(uids) == 1:
                    users_sql = f"""
                        select uid,telephone_encrypt,from_unixtime(created_time) 'user_created_time',reg_from from vh_user where uid = {uids[0]}
                            
                        """
                elif len(uids) > 1:
                    users_sql = f"""
                            select uid,telephone_encrypt,from_unixtime(created_time) 'user_created_time',reg_from from vh_user where uid in {uids}
                                
                            """
                else:
                    pass
                users_df = pd.read_sql_query(users_sql,reconn_user)
                main_df = pd.merge(main_orders_dff,users_df,how="left")
                province_name = get_regional_name(tuple(main_df.province_id.tolist()),reconn_user)
                city_name = get_regional_name(tuple(main_df.city_id.tolist()),reconn_user)
                district_name = get_regional_name(tuple(main_df.district_id.tolist()),reconn_user)
                if type(province_name) != int and type(city_name) != int and type(district_name) != int:
                    main_dff = pd.merge(main_df,province_name,how="left",left_on="province_id",right_on="id").merge(city_name,how="left",left_on="city_id",right_on="id").merge(district_name,how="left",left_on="district_id",right_on="id")
                elif province_name == 0:
                    main_dff = pd.merge(main_df,city_name,how="left",left_on="city_id",right_on="id").merge(district_name,how="left",left_on="district_id",right_on="id")
                elif province_name == 0 and city_name == 0:
                    main_dff = pd.merge(main_df,district_name,how="left",left_on="district_id",right_on="id")
                elif province_name == 0 and city_name == 0 and district_name == 0:
                    main_dff[["province_name","city_name","district_name"]] = ["暂无省信息","暂无市信息","暂无区信息"]
                else:
                    pass
                main_dff["area"] = main_dff.name_x + main_dff.name_y + main_dff.name + main_dff.address
                commodities_main_df = get_commodities(tuple(main_dff.period.tolist()),reconn_commodities)
                main_data = pd.merge(main_dff,commodities_main_df,how="left",left_on = "period",right_on = "id")
                orders_user_data = get_uid_status(tuple(main_data.uid.tolist()),ordesr_tables,reconn_orders)
                user_type = []
                if type(orders_user_data) == int:
                    main_data["user_type"] = "新用户"
                else:
                    for uid,created_time in zip(main_data.uid,main_data.created_time):
                        uid_order = orders_user_data[orders_user_data.uid == uid]
                        hav_order = uid_order[uid_order.order_created_time < created_time]
                        if hav_order.empty:
                            is_new = "新用户"
                        else:
                            is_new = "老用户"
                        user_type.append(is_new)
                    main_data["user_type"] = user_type
                if is_new_user == "":
                    Over_Main_Df=main_data
                elif is_new_user == "0":
                    Over_Main_Df=main_data[main_data.user_type == "老用户"]
                elif is_new_user == "1":
                    Over_Main_Df=main_data[main_data.user_type == "新用户"]
                else:
                    pass
                total = len(Over_Main_Df)
                if page == "":
                    page = "1"
                if page_nums == "":
                    page_nums = "10"
                else:
                    pass
                if page and page_nums == "all":
                    Over_Main_Df = Over_Main_Df.copy()
                else:
                    Over_Main_Df = Over_Main_Df[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
                if out_type == "data":
                    Over_Main_Df.drop(["main_order_id","id","created_time","province_id","city_id","district_id","id_x","id_y","id_x","id_y","name_x","name_y","name","address"],axis=1,inplace=True)
                    Over_Main_Dff = Over_Main_Df[["下单时间","uid","title","period","area","telephone_encrypt","order_qty","payment_amount - refund_money","user_type","user_created_time","is_validorder"]]
                    Over_Main_Dff.rename(columns={"下单时间":"order_created_time","payment_amount - refund_money":"payment_amount"},inplace=True)
                    Over_Out = Over_Main_Dff
                elif out_type == "json":
                    Over_Main_Df.drop(["main_order_id","id","created_time","province_id","city_id","district_id","id_x","id_y","id_x","id_y","name_x","name_y","name","address"],axis=1,inplace=True)
                    Over_Main_Dff = Over_Main_Df[["下单时间","title","reg_from","period","area","telephone_encrypt","uid","order_qty","payment_amount - refund_money","user_type","user_created_time"]]
                    Over_Main_Dff.rename(columns={"下单时间":"order_created_time","payment_amount - refund_money":"payment_amount"},inplace=True)
                    Over_Main_Dff["order_created_time"] = Over_Main_Dff.order_created_time.astype(str)
                    Over_Main_Dff["user_created_time"] = Over_Main_Dff.user_created_time.astype(str)
                    reg_from_dict = {0:"未知",1:"android",2:"ios",3:"酒云网小程序",4:"h5",5:"PC",6:"抖音小程序",7:"后台添加",8:"酒历小程序",9:"公社小程序",10:"iPad",11:"门店小程序"}
                    Over_Main_Dff["reg_from"] = Over_Main_Dff.reg_from.apply(lambda x:reg_from_dict[x])
                    Over_Out = {"orders":json.loads(Over_Main_Dff.to_json(orient="records")),
                                "total":total
                                }
                return Over_Out
    except Exception as e:
            return {"error_code": -1, "error_msg": f"主页数据获取失败{e}", "status": "fail", "data":[]}


def GetOutUserDatas(time_range,source_type,is_new_user,is_validorder,period):
    """
    统计数据入口
    :param time_range: 时间区间
    :param source_type: 投放渠道
    :param is_new_user: 是否为新顾客(1-是,0-否)
    :param is_validorder: 是否为有效订单(1-是,0-否)
    :return:    
    """
    try:
        Main_Order = GetOutOrder(time_range,source_type,is_new_user,is_validorder,period,"all","all","data")
        if type(Main_Order) == dict:
            return Main_Order
        elif Main_Order.empty:
            return {"error_code": -1, "error_msg": "未发现相应数据,无法生成可视化!", "total": 0, "data":[]}
        else:
            Effective_Order = Main_Order[Main_Order.is_validorder == 1]
            Void_Order = Main_Order[Main_Order.is_validorder == 0]
            if Effective_Order.empty:
                Effective_Order_Count = 0
            else:
                Effective_Order_Count = Effective_Order.order_created_time.count()
            if Void_Order.empty:
                Void_count = 0
            else:
                Void_count = Void_Order.order_created_time.count()       
            Add_count = Effective_Order_Count + Void_count
            Effective_Percentage = Effective_Order_Count/(Effective_Order_Count+Void_count) * 100
            Void_Percentage = Void_count/(Effective_Order_Count+Void_count) * 100
            Add_Percentage = Effective_Percentage + Void_Percentage
            out_user = Main_Order[["user_type","uid"]]
            out_user.drop_duplicates(keep="first",inplace=True)
            out_user_count = out_user.groupby(by="user_type",as_index=False).count()
            out_user_count.rename(columns={"uid":"user_count"},inplace=True)
            out_user_count["user_proportion"] = out_user_count.user_count.apply(lambda x:x/sum(out_user_count.user_count.tolist())*100)
            out_user_orders_count = Main_Order[["user_type","title"]].groupby(by="user_type",as_index=False).count()["title"]
            out_user_count["user_orders_count"] = out_user_orders_count
            out_user_count["user_orders_proportion"] = out_user_count.user_orders_count.apply(lambda x:x/sum(out_user_count.user_orders_count.tolist())*100)
            user_orders_ment = Main_Order[["user_type","payment_amount"]].groupby(by="user_type",as_index=False).sum()["payment_amount"]
            out_user_count["user_orders_ment"] = user_orders_ment
            out_user_count["user_orders_ment_proportion"] = out_user_count.user_orders_ment.apply(lambda x:x/sum(out_user_count.user_orders_ment.tolist())*100)
            out_user_count.loc[2]=["合计",sum(out_user_count.user_count.tolist()),sum(out_user_count.user_proportion.tolist()),sum(out_user_count.user_orders_count.tolist()),sum(out_user_count.user_orders_proportion.tolist()),sum(out_user_count.user_orders_ment.tolist()),sum(out_user_count.user_orders_ment_proportion.tolist())]
            out_user_count["user_proportion"] = out_user_count.user_proportion.apply(lambda x:'%.2f' % x)
            out_user_count["user_orders_proportion"] = out_user_count.user_orders_proportion.apply(lambda x:'%.2f' % x)
            out_user_count["user_orders_ment"] = out_user_count.user_orders_ment.apply(lambda x:'%.2f' % x)
            out_user_count["user_orders_ment_proportion"] = out_user_count.user_orders_ment_proportion.apply(lambda x:'%.2f' % x)
            out_user_count.set_index("user_type",inplace=True)
            Effective_Void_count_json = {
                "有效订单" : int(Effective_Order_Count),
                "无效订单" : int(Void_count),
                "合计" : int(Add_count)
            }
            Effective_Void_Proportion_json = {
                "有效订单占比" :'%.2f' % Effective_Percentage,
                "无效订单占比" :'%.2f' % Void_Percentage,
                "合计" :'%.2f' % Add_Percentage
            }
            total = len(out_user_count.T) + 2
            out_user_count_json = {
                "user_count":out_user_count.T.iloc[0].to_dict(),
                "user_proportion":out_user_count.T.iloc[1].to_dict(),
                "user_orders_count":out_user_count.T.iloc[2].to_dict(),
                "user_orders_proportion":out_user_count.T.iloc[3].to_dict(),
                "user_orders_ment":out_user_count.T.iloc[4].to_dict(),
                "user_orders_ment_proportion":out_user_count.T.iloc[5].to_dict(),
                "Effective_Void_count":Effective_Void_count_json,
                "Effective_Void_proportion":Effective_Void_Proportion_json,
                "total":total
            }
        return out_user_count_json
    except Exception as e:
         return {"error_code": -1, "error_msg": f"统计数据获取失败{e}", "status": "fail", "data":[]}
        
        
        
def GetOutGoodsDatas(time_range,source_type,is_new_user,is_validorder,page,page_nums,period):
    """
    统计数据入口
    :param time_range: 时间区间
    :param source_type: 投放渠道
    :param is_new_user: 是否为新顾客(1-是,0-否)
    :param is_validorder: 是否为有效订单(1-是,0-否)
    :param period: 期数
    :return:    
    """
    Main_Order = GetOutOrder(time_range,source_type,is_new_user,is_validorder,period,"all","all","data")
    if type(Main_Order) == dict:
        return Main_Order
    elif Main_Order.empty:
        return {"error_code": -1, "error_msg": "未发现相应数据,无法生成可视化!", "total": 0, "data":[]}
    else:
        Main_Order = Main_Order.groupby(by="period",as_index=False).agg({"title":"first","order_qty":"size","payment_amount":sum})
        Main_Order = Main_Order.sort_values(by=["order_qty","payment_amount"],ascending = False).reset_index(drop=True)
        Main_Order["Order_Percentage"] = Main_Order.order_qty.apply(lambda x:round(x/(sum(Main_Order.order_qty)) * 100,2))
        Main_Order["Amount_Percentage"] = Main_Order.payment_amount.apply(lambda x:round(x/(sum(Main_Order.payment_amount)) * 100,2))
        if page == "":
            page = "1"
        if page_nums == "":
            page_nums = "10"
        total = len(Main_Order)
        Main_Order_Show = Main_Order[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
        Main_Order_Show = Main_Order_Show[["period","title","order_qty","Order_Percentage","payment_amount","Amount_Percentage"]]
        if len(Main_Order) >= 9:
            Main_Data_Nine = Main_Order[["title","order_qty","payment_amount"]].head(9)
            Main_Data_Other = Main_Order.drop(index = Main_Data_Nine.index.tolist(),axis=0).reset_index(drop=True)[["order_qty","payment_amount"]]
            Main_Data_Other["title"] = "other"
            Main_Data_Other = Main_Data_Other.groupby(by="title",as_index=False).agg({"order_qty":sum,"payment_amount":sum})
            Data_Show = Main_Data_Nine.append(Main_Data_Other).reset_index(drop=True)
        else:
            Data_Show = Main_Order[["title","order_qty","payment_amount"]]
        Data_Show = Data_Show.rename(columns = {"order_qty":"Order_Percentage","payment_amount":"Amount_Percentage"})
        Order_Data_Show = Data_Show[["title","Order_Percentage"]]
        Amount_Data_Show = Data_Show[["title","Amount_Percentage"]]
        res_json = {
            "Orders":json.loads(Main_Order_Show.to_json(orient="records")),
            "Visualization":{
                "Order_Percentage":json.loads(Order_Data_Show.to_json(orient="records")),
                "Amount_Percentage":json.loads(Amount_Data_Show.to_json(orient="records"))
            },
            "Orders_total":total
        }
        return res_json

    

def GetOutOrderDatas(time_range,source_type,is_new_user,is_validorder,page,page_nums,period,Statistics_type):
    if Statistics_type == "0":
        return GetOutUserDatas(time_range,source_type,is_new_user,is_validorder,period)
    elif Statistics_type == "1":
        return GetOutGoodsDatas(time_range,source_type,is_new_user,is_validorder,page,page_nums,period)
    else:
        return {"error_code": -1, "error_msg": f"参数不对", "status": "fail", "data": {}}