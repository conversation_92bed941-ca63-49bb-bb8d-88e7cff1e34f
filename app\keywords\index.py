MONGOURI = "***********************************************************************"

from pymongo import MongoClient
from datetime import datetime, timedelta
import re
import pytz
from urllib.parse import parse_qs, unquote
from check_by_api import process_keywords
from mysql import batch_insert_data, query_data,one_query_data,insert_data
import csv
import time


# 连接到 MongoDB (根据您的配置替换 URI)
client = MongoClient(MONGOURI)

# 选择数据库和集合
db = client.vinehoo_v3
collection = db.gateway_logs

# 获取昨天的日期并设置时区
desired_time_zone = pytz.timezone('Asia/Shanghai')

currentday = datetime.now()
currentday_time = currentday.strftime("%Y-%m-%d %H:%M:%S")
yesterday = currentday - timedelta(days=1)
yesterday_start = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
yesterday_end = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
yesterday_date = yesterday.strftime("%Y-%m-%d")

yday_date = yesterday.date()
# 将时间设置为 0 点
zero_hour = datetime.combine(yday_date, datetime.min.time())
# 转换为 10 位时间戳
yesterday_timestamp = int(zero_hour.timestamp())

print(currentday_time)

# 构建查询条件
query = {
    'route_url': '/openapi/v3/search/aggregation',
    'request_param': re.compile(r'\bkeywords\b'),
    'request_time': {'$gte': yesterday_start, '$lte': yesterday_end}
}

# 执行查询，并且只返回 request_param 字段
projection = {'request_param': 1, '_id': 0}
results = collection.find(query, projection)

# 提取并解码关键词，统计出现次数
keywords_count = {}
for result in results:
    params = parse_qs(result['request_param'])
    if 'search_type' in params and 'goods' in params['search_type']:
        keyword = unquote(params['keywords'][0])
        keywords_count[keyword] = keywords_count.get(keyword, 0) + 1

# 验证关键词是否有搜索结果
res = process_keywords(keywords_count)
if len(res)>0:
    # 查询统计结果是否已写入
    statistics = one_query_data('vh_data_statistics', f"SELECT id FROM vh_search_keywords_statistics where date = '{yesterday_timestamp}'")
    stat_id = 0
    if statistics is not None:
        stat_id = statistics['id']
    stat_info = {"id":stat_id}

    # 查询是否已写入
    exist = query_data('vh_data_statistics',f"SELECT * FROM vh_search_keywords where date = '{yesterday_date}'")
    exist_data = {}
    for v in exist:
        exist_data[f"{v['date']}-{v['keywords']}"] = 1

    data_list = []
    with_results_count = 0
    for v in res:
        if not exist_data.get(f"{yesterday_date}-{v['keywords']}"):
            data_list.append({
                'date':yesterday_date,
                'keywords': v['keywords'],
                'search_count': v['search_count'],
                'status': v['status'],
                'created_time':int(time.time()),
            })

        if int(v['status']) == 0:
            with_results_count += 1

    #保存记录
    if len(data_list)>0:
        batch_insert_data('vh_data_statistics','vh_search_keywords',data_list)

    #保存统计结果
    if int(stat_info['id']) == 0:
        total_count = len(res)
        with_results_rate = f"{(with_results_count / total_count) * 100:.2f}%"
        stat_data = {
            "date":yesterday_timestamp,
            "total_count":total_count,
            "with_results_count":with_results_count,
            "with_results_rate":with_results_rate,
            'created_time':int(time.time()),
        }
        insert_data('vh_data_statistics','vh_search_keywords_statistics',stat_data)





print('OK')
print(currentday.strftime("%Y-%m-%d %H:%M:%S"))
# 获取当前时间并格式化为年月日时分秒
# current_time = datetime.now().strftime("%Y%m%d")

# 保存为 CSV 文件，文件名为当前时间加-keywords.csv
# filename = f"{current_time}-keywords.csv"
# with open(filename, 'w', newline='', encoding='utf-8') as file:
#     writer = csv.writer(file)
#     writer.writerow(['Keyword', 'Count'])  # 写入表头
#     for keyword, count in keywords_count.items():
#         writer.writerow([keyword, count])

# print(f"CSV文件已保存为 {filename}。")