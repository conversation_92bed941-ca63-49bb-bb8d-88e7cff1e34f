# -*- coding: utf-8 -*-
# 项目启动文件

import unittest
from flask import Flask, request
from flask_restful import Api, Resource, reqparse, request
import warnings
import waitress
from datetime import datetime
import time

# 忽略警告
from app.CommonLibraries.LogRecord import record_error
from app.DButils.MongoHelper import conn_mongo
from app.DButils.MysqlHelper import MysqlPool, commodities_pool,orders_pool,users_pool,data_statistic_pool,information_pool,wiki_pool,auction_pool,maidian_pool
from app.ScheduleTask.MarkNewUser import get_newuser_mark
from app.ScheduleTask.MarkNewUser import take_newuser_mark
from app.ScheduleTask.task_start import start_map_analysis
from app.ScheduleTask.task_start import start_map_analysis_test
from app.ScheduleTask.user_active_insert import handler_start
from app.ScheduleTask.second_visit_insert import start_second_visit_insert
from app.admin.OperationSales import get_operation_sales, get_param_time, assemble_data, push_info_robot, push_info_robot_by_queue_service
from app.admin.DailyMerchandiseStatistics import get_daily_merchandise_info
from app.admin.DailySalesProgress import get_daily_sales_progress
from app.admin.OnlineGoodsRealtimeStatistics import admin_function
from app.admin.OperationalComplianceRate import get_operation_complete_info
from app.admin.PlanDataUpdate import add_plan_data
# from app.admin.LiveRealTimeStatistics import get_live_order_info
# from app.admin.LiveRealTimeStatisticsDetail import get_live_order_detail
from app.admin.RegionalStatistics import get_regional_statistics
from app.admin.RegisteredUserStatistics import get_registered_user_statistics
from app.admin.UndeliveredStatistics import get_undelivered_statistics
from app.admin.UndeliveredStatisticsDetail import get_undelivered_statistics_detail
from app.admin.UserActivityStatistics import get_user_activity_statistics
from app.admin.YearSalesProgress import get_year_sales_progress
from app.ScheduleTask.vh_monthly_sales_sum import get_payment_amount
from app.admin.CountDeliversubordersOut import DeliversubordersOut
from app.admin.PullTheNewUser import NewUsersOrders
from app.admin.PullTheNewUser import get_filter_list
from app.admin.NewOrderUsersCount import get_new_order_users
from app.admin.MarkNewUserDatas import GetOutOrder
from app.admin.MarkNewUserDatas import GetOutOrderDatas
from app.admin.SpecialActivitySalesDatas import GetOrderList
from app.admin.SpecialActivitySalesDatas import GetOrderSalesDatas
from app.admin.Auction_Aster import get_hour_resstr
from app.admin.Auction_Aster import get_day_resstr
from app.admin.Auction_Aster import auction_aster
from app.admin.Auction_Aster import get_now_to_aster
from app.admin.OrderFrequency import get_user_order_frequency
from app.admin.MerchantSecondRegion import get_merchantsecondregion
from app.admin.NewUserCreatedFrom import get_newusercreatedfrom
from app.admin.NewUserTransformStatistics import get_new_user_transform_statistics
from app.admin.Sales_situation_dashboard import get_result
from app.admin.Live_Sales_situation_dashboard import get_live_result
from app.admin.Coupon_Board import get_coupon_main
from app.ScheduleTask.take_vest_comment import update_open_state
from app.admin.VestCommentStatistics import get_depatment, get_vest_comment, get_evaluation_main_dff
from app.admin.AuctionPullNewUserStatistics import get_main_massage
from app.CommonLibraries.getConfig import get_config_str
from app.CommonLibraries.AddRequests import add_requests
from app.keywords.api import QuerySearchKeywordsList,QuerySearchKeywordsCurve
from app.admin.SecondVisit import SecondVisitPvUvList
import sys
# from app.ScheduleTask.count_register_daily_day import handler_register_daily
# from app.admin.RegistereUserStatisticsDaily import get_Merchant_main_statistics


warnings.filterwarnings("ignore")

app = Flask(__name__)
api = Api(app)
sys.setrecursionlimit(3000)


@app.errorhandler(404)
def page_not_found(error):
    return 'This route does not exist', 500


# @app.before_request
# def verify_headers():
#     content_type = request.headers['Content-Type']
#     if request.method == 'POST':
#         if content_type != "application/json":
#             return {"error_code": 0, "error_msg": "请求方式不为application/json，请设定请求方式后重新请求", "data": {}}
#         else:
#             pass
#     else:
#         pass


@app.route("/")
def index():
    return "ok-py3-v3-data-analysis"


# class LiveRealTimeStatistics(Resource):
#     def post(self):
#         try:
#             commodities_conn, commodities_conn_cursor = commodities_pool.get_connection()
#             orders_conn, orders_conn_cursor = orders_pool.get_connection()
#             wiki_conn, wiki_conn_cursor = wiki_pool.get_connection()
#             parser = reqparse.RequestParser()
#             parser.add_argument("day", type=str, location="json", help="日期字段校验错误")
#             parser.add_argument("period_type", type=str, location="json", help="类型字段校验错误")
#             parser.add_argument("import_type", type=str, help="进口类型字段校验错误", location="json")
#             parser.add_argument("buyer", type=str, help="采购字段校验错误", location="json")
#             parser.add_argument("sort_key", type=str, help="排序字段校验错误", location="json",
#                                 choices=["payment_amount", "order_qty", "order_users", ""])
#             # print(type(request.json['goods_type']))
#             parser.add_argument("goods_type", type=list, location="json")
#             parser.add_argument("page", type=int, help="初始页字段校验错误", location="json", required=True)
#             parser.add_argument("page_nums", type=int, help="页面条数字段校验错误", location="json", required=True)
#             args = parser.parse_args()
#             response = get_live_order_info(args['day'], args['period_type'], args['import_type'], args['buyer'], args['sort_key'],
#                                       args['goods_type'], args['page'], args['page_nums'], commodities_conn,
#                                       orders_conn, wiki_conn)
#             commodities_pool.dispose(commodities_conn, commodities_conn_cursor)
#             commodities_pool.dispose(orders_conn, orders_conn_cursor)
#             wiki_pool.dispose(wiki_conn, wiki_conn_cursor)
#             return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
#         except Exception as e:
#             print(e)
#             return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


# class LiveRealTimeStatisticsDetail(Resource):
#     def post(self):
#         try:
#             parser = reqparse.RequestParser()
#             parser.add_argument("day", type=str, location="json", help="日期字段校验错误")
#             parser.add_argument("hour", type=int, location="json", help="时刻字段校验错误")
#             parser.add_argument("period_type", type=str ,location="json", help="类型字段校验错误")
#             parser.add_argument("import_type", type=str, help="进口类型字段校验错误", location="json")
#             parser.add_argument("buyer", type=str, help="采购字段校验错误", location="json")
#             parser.add_argument("goods_type", type=list, location="json")
#             parser.add_argument("page", type=int, help="初始页字段校验错误", location="json", required=True)
#             parser.add_argument("page_nums", type=int, help="页面条数字段校验错误", location="json", required=True)
#             args = parser.parse_args()
#             if args['period_type'] == "":
#                 return {"error_code": -1, "error_msg": f"请选择详细统计商品频道", "status": "fail", "data": {}}
#             else:
#                 commodities_conn, commodities_conn_cursor = commodities_pool.get_connection()
#                 orders_conn, orders_conn_cursor = orders_pool.get_connection()
#                 wiki_conn, wiki_conn_cursor = wiki_pool.get_connection()
#                 response = get_live_order_detail(args['day'], args['hour'], args['period_type'], args['import_type'], args['buyer'],
#                                         args['goods_type'], args['page'], args['page_nums'], commodities_conn,
#                                         orders_conn, wiki_conn)
#                 commodities_pool.dispose(commodities_conn, commodities_conn_cursor)
#                 orders_pool.dispose(orders_conn, orders_conn_cursor)
#                 wiki_pool.dispose(wiki_conn, wiki_conn_cursor)
#                 return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
#         except Exception as e:
#             return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class OperationalComplianceRate(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument(
                "month", type=str, location="json", help="月份字段校验错误", required=True)
            args = parser.parse_args()
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            response = get_operation_complete_info(args['month'], conn_statics)
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class DailyMerchandiseStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("start_time", type=str,
                                location="json", help="开始时间校验错误", required=True)
            parser.add_argument("end_time", type=str,
                                location="json", help="结束时间校验错误", required=True)
            parser.add_argument("sort_key", type=str, location="json", help="排序字段校验错误", choices=[
                                "", "0", "1", "2", "3", "4", "5", "6"], required=True)
            parser.add_argument(
                "page", type=int, location="json", help="页数校验错误", required=True)
            parser.add_argument("page_nums", type=int,
                                location="json", help="每页条数校验错误", required=True)
            args = parser.parse_args()
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            response = get_daily_merchandise_info(args['start_time'], args['end_time'], args['sort_key'], args['page'],
                                                  args['page_nums'], conn_statics)
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class DailySalesProgress(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("month", type=str,
                                location="json", help="月份校验错误", required=True)
            parser.add_argument("sell_type", type=int, location="json", help="销售类型校验错误", choices=[1, 2, 3, 4, 5, 6, 7],
                                required=True)
            parser.add_argument("is_liquor", type=int, location="json",
                                help="是否白酒校验错误", choices=[0, 1], required=True)
            args = parser.parse_args()
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            response = get_daily_sales_progress(
                args['month'], args['sell_type'], args['is_liquor'], conn_statics)
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class YearSalesProgress(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument(
                "year", type=str, location="json", help="年份校验错误", required=True)
            parser.add_argument("sell_type", type=int, location="json", help="销售类型校验错误", choices=[0, 1, 2, 3, 4],
                                required=True)
            parser.add_argument(
                "corp", type=str, location="json", help="公司主体校验错误", required=True)
            args = parser.parse_args()
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            response = get_year_sales_progress(
                args['year'], args['sell_type'], args['corp'], conn_statics)
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class RegisteredUserStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("count_key", type=str, location="json", help="统计字段校验错误", required=True,
                                choices=["day", "month"])
            parser.add_argument(
                "date", type=str, location="json", help="时间校验错误", required=True)
            parser.add_argument("channel_type", type=str,
                                location="json", help="频道类型校验错误", required=True)
            parser.add_argument("delivery_store_id", type=str,
                                location="json", help="发货点id校验错误", required=True)
            parser.add_argument("merchant_id", type=str,
                                location="json", help="商家id校验错误", required=True)
            args = parser.parse_args()
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            orders_conn, orders_conn_cursor = orders_pool.get_connection()
            response = get_registered_user_statistics(
                args['count_key'], args['date'], args['channel_type'], args['delivery_store_id'], args['merchant_id'], conn_statics, orders_conn)
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            orders_pool.dispose(orders_conn, orders_conn_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class MerchantSecondRegion(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("count_key", type=str,
                                location="json", help="开始时间字段校验错误")
            parser.add_argument("start_time", type=str,
                                location="json", help="开始时间字段校验错误")
            parser.add_argument("end_time", type=str,
                                location="json", help="结束时间字段校验错误")
            parser.add_argument("is_collect", type=int,
                                location="json", help="是否为明细字段校验错误")
            args = parser.parse_args()
            count_key = args['count_key']
            if count_key == "day":
                response = get_merchantsecondregion(
                    args['start_time'], args['end_time'], args['is_collect'])
            else:
                return {"error_code": -1, "error_msg": f"请求失败,失败信息-请求参数不一致!", "status": "fail", "data": {}}
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class UndeliveredStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("period_type", type=str,
                                location="json", help="期数校验错误")
            parser.add_argument("period", type=str,
                                location="json", help="期数校验错误")
            parser.add_argument("period_name", type=str,
                                location="json", help="产品名称校验错误")
            parser.add_argument("estimate_delivery_time",
                                type=str, location="json", help="预计发货时间校验错误")
            parser.add_argument("stime", type=list,
                                location="json", help="上架时间校验错误")
            parser.add_argument("etime", type=list,
                                location="json", help="下架时间校验错误")
            parser.add_argument("is_overdue", type=int,
                                location="json", help="是否逾期校验错误")
            parser.add_argument("is_stage", type=int,
                                location="json", help="是否暂存校验错误")
            parser.add_argument("supplier_name", type=str,
                                location="json", help="产品名称校验错误")
            parser.add_argument(
                "page", type=int, location="json", help="页数校验错误")
            parser.add_argument("page_nums", type=int,
                                location="json", help="单页条数校验错误")
            parser.add_argument("is_export", type=int,
                                location="json", help="单页条数校验错误")
            parser.add_argument("push_wms_status", type=str,
                                location="json", help="是否推送萌芽查询错误")
            parser.add_argument("import_type", type=str,
                                location="json", help="进口类型校验错误")
            parser.add_argument("is_supplier_delivery", type=str,
                                location="json", help="发货方式查询错误")
            parser.add_argument("is_push_warehouse_24h", type=int,
                                location="json", help="推送仓库时间大于24小时")
            parser.add_argument("buyer_id", type=int,
                                location="json", help="采购人ID")
            args = parser.parse_args()
            args['dingtalk-uid'] = request.headers['dingtalk-uid']
            response = get_undelivered_statistics(args)
            if response['data'] == -1:
                return {"error_code": -1, "error_msg": response['err'], "status": "fail", "data": {}}
            if response['data'] == 1:
                return {"error_code": 0, "error_msg": "文件将通过企业微信的“工作通知”发送", "status": "success", "data": {}}
            if isinstance(response, dict):
                return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
            else:
                return response
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e.args}", "status": "fail", "data": {}}


class UndeliveredStatisticsDetail(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("period", type=int,
                                location="json", help="期数校验错误")
            parser.add_argument("period_type", type=int,
                                location="json", help="期数校验错误")
            parser.add_argument("is_stage", type=int,
                                location="json", help="是否暂存校验错误")
            args = parser.parse_args()
            commodities_conn, commodities_conn_cursor = commodities_pool.get_connection()
            orders_conn, orders_conn_cursor = orders_pool.get_connection()
            response = get_undelivered_statistics_detail(
                args, orders_conn, commodities_conn)
            commodities_pool.dispose(commodities_conn, commodities_conn_cursor)
            orders_pool.dispose(orders_conn, orders_conn_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"获取未发货订单详情失败,失败信息-{e}", "status": "fail", "data": {}}


class RegionalStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("start_time", type=str,
                                location="json", help="开始时间校验错误")
            parser.add_argument("end_time", type=str,
                                location="json", help="结束时间校验错误")
            args = parser.parse_args()
            statics_conn, statics_conn_cursor = data_statistic_pool.get_connection()
            response = get_regional_statistics(
                args['start_time'], args['end_time'], statics_conn)
            data_statistic_pool.dispose(statics_conn, statics_conn_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class OnlineGoodsRealtimeStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("count_key", type=str,
                                location="json", help="统计字段校验错误")
            parser.add_argument("month", type=str,
                                location="json", help="月份校验错误")
            args = parser.parse_args()

            url = get_config_str("url.orders.service", "vinehoo.services")
            req_url = f"{url}/orders_server/v3/data_analysis/online_goods_realtime_statistics?count_key={args['count_key']}&month={args['month']}"
            resp = add_requests(req_type='get', url=req_url, body={})
            return resp
            # response = admin_function(args['count_key'], args['month'])
            # return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class UserActivityStatistics(Resource):
    def post(self):
        try:
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            parser = reqparse.RequestParser()
            parser.add_argument(
                "date", type=str, location="json", help="日期校验错误")
            args = parser.parse_args()
            response = get_user_activity_statistics(
                args['date'], conn_statics_cursor)
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": {"time_frame": response}}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"请求失败,失败信息-{e}", "status": "fail", "data": {}}


class OperationSales(Resource):
    def get(self):
        try:
            # 获取db连接
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            conn_orders, conn_orders_cursor = orders_pool.get_connection()
            conn_commodities, conn_commodities_cursor = commodities_pool.get_connection()
            now_time = get_param_time()
            res_json = get_operation_sales(now_time, conn_statics_cursor, conn_orders_cursor, conn_commodities_cursor,
                                           conn_orders, conn_commodities)
            text, curtime = assemble_data(res_json, now_time)
            response = push_info_robot(text, curtime)
            # 归还db连接
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            orders_pool.dispose(conn_orders, conn_orders_cursor)
            commodities_pool.dispose(conn_commodities, conn_commodities_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"运营销售数据获取失败,失败信息-{e}", "status": "fail", "data": {}}


class DataStaticsTask(Resource):
    def get(self):
        try:
            start_map_analysis()
            return "ok"
        except Exception as e:
            print(e)
            record_error('数据统计', e)

class DataStaticsTaskTest(Resource):
    def get(self):
        try:
            start_map_analysis_test()
            return "ok"
        except Exception as e:
            print(e)
            record_error('测试数据统计', e)

class UserInfoPush(Resource):
    def get(self):
        return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": {}}


class UserActiveInsert(Resource):
    def get(self):
        now_day = request.args.get('now_day')
        conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
        mongo_client = conn_mongo()
        handler_start(now_day, mongo_client, conn_statics, conn_statics_cursor)
        data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
        return 'ok'

class SecondVisitInsert(Resource):
    def get(self):
        conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
        mongo_client = conn_mongo()
        start_second_visit_insert(mongo_client, conn_statics, conn_statics_cursor)
        data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
        return 'ok'


class YearlySalesCount(Resource):
    def get(self):
        date_time = time.strftime(
            '%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        get_payment_amount(date_time)
        return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": {}}


class UpdatePlanData(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("file_name", type=str,
                                location="json", help="文件名校验错误")
            parser.add_argument("is_update", type=int,
                                location="json", help="更新或写入字段校验错误")
            args = parser.parse_args()
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            response = add_plan_data(
                args['file_name'], conn_statics, conn_statics_cursor, args['is_update'])
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": {}}

        except Exception as e:
            return {"error_code": -1, "error_msg": f"{e}", "status": "fail", "data": {}}


class CountDeliversubordersOut(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("suborderno", type=str,
                                location="json", help="订单号校验错误")
            args = parser.parse_args()
            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            subordernodata = args["suborderno"]
            result = DeliversubordersOut(subordernodata, conn_statics)
            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
            return result

        except Exception as e:
            return {"error_code": -1, "error_msg": f"{e}", "status": "fail", "data": {}}


class Ip_Regfrom_Record(Resource):
    def get(self):
        try:
            res_json = get_filter_list()
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": res_json}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"{e}", "status": "fail", "data": {}}


class PullTheNewUser(Resource):
    def post(self):
        try:
            users_conn, users_conn_cursor = users_pool.get_connection()
            parser = reqparse.RequestParser()
            parser.add_argument("begin_time", type=str,
                                location="json", help="开始日期字段校验错误")
            parser.add_argument("over_time", type=str,
                                location="json", help="结束类型字段校验错误")
            parser.add_argument("source_type", type=str,
                                location="json", help="渠道字段校验错误")
            parser.add_argument(
                "mark", type=str, location="json", help="标识字段校验错误")
            parser.add_argument("coupon_id", type=str,
                                location="json", help="券包字段校验错误")
            parser.add_argument("ip_filter", type=str,
                                location="json", help="ip地址筛选字段校验错误")
            parser.add_argument("reg_filter", type=str,
                                location="json", help="注册来源筛选字段校验错误")
            parser.add_argument("table_type", type=str,
                                location="json", help="表选项字段校验错误")
            parser.add_argument("page_args", type=dict,
                                location="json", help="页数字段校验错误")
            args = parser.parse_args()

            if args["source_type"] == "0" and args["mark"].startswith("-"):
                args["mark"] = args["mark"][1:]

            if args["begin_time"] == "" or args["over_time"] == "":
                return {"error_code": 0, "error_msg": "请完整输入日期信息!", "status": "success"}
            result = NewUsersOrders(args["begin_time"], args["over_time"], args["source_type"], args["mark"],
                                    args["coupon_id"], args["ip_filter"], args["reg_filter"], args["table_type"], args["page_args"], users_conn)
            users_pool.dispose(users_conn, users_conn_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": result}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class NewOrderUsersCount(Resource):
    def get(self):
        try:
            time = datetime.now()
            orders_pool = MysqlPool('vh_orders')
            orders_conn, orders_conn_cursor = orders_pool.get_connection()
            result = get_new_order_users(time, orders_conn)
            orders_pool.dispose(orders_conn, orders_conn_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": result}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class MarkNewUserTakeNewUserMark(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("is_add", type=str,
                                location="json", help="编辑模式字段校验错误")
            parser.add_argument("now_platformmark", type=str,
                                location="json", help="当前平台标识字段校验错误")
            parser.add_argument("now_eventmark", type=str,
                                location="json", help="当前事件标识字段校验错误")
            parser.add_argument("now_source_type", type=str,
                                location="json", help="当前渠道标识字段校验错误")
            parser.add_argument("platformmark", type=str,
                                location="json", help="编辑平台标识字段校验错误")
            parser.add_argument("eventmark", type=str,
                                location="json", help="编辑事件标识字段校验错误")
            parser.add_argument("source_type", type=str,
                                location="json", help="编辑渠道标识字段校验错误")
            args = parser.parse_args()
            try:
                if take_newuser_mark(args)["error_code"] == -1:
                    return take_newuser_mark(args)
            except:
                return {"error_code": 0, "error_msg": "请求成功", "status": "success"}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class MarkNewuserGetNewUserMark(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("keyword", type=str,
                                location="json", help="关键词字段校验错误")
            parser.add_argument("now_eventmark", type=str,
                                location="json", help="事件字段校验错误")
            parser.add_argument("is_open", type=str,
                                location="json", help="开关字段校验错误")
            parser.add_argument(
                "page", type=str, location="json", help="页数字段校验错误")
            parser.add_argument("page_nums", type=str,
                                location="json", help="单页条数字段校验错误")
            parser.add_argument("query_type", type=str,
                                location="json", help="查询类型字段校验错误")
            args = parser.parse_args()
            result = get_newuser_mark(
                args["keyword"], args["now_eventmark"], args["is_open"], args["page"], args["page_nums"], args["query_type"])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": result}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class MarkNewuserDatas(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("time_range", type=list,
                                location="json", help="时间范围校验错误")
            parser.add_argument("source_type", type=str,
                                location="json", help="渠道字段校验错误")
            parser.add_argument("is_new_user", type=str,
                                location="json", help="用户类型字段校验错误")
            parser.add_argument("is_validorder", type=str,
                                location="json", help="订单类型字段校验错误")
            parser.add_argument("period", type=str,
                                location="json", help="期数字段校验错误")
            parser.add_argument(
                "page", type=str, location="json", help="页数校验错误")
            parser.add_argument("page_nums", type=str,
                                location="json", help="每页条数校验错误")
            parser.add_argument("Statistics_type", type=str,
                                location="json", help="可视化类型字段校验错误")
            parser.add_argument("out_type", type=str,
                                location="json", help="订单类型字段校验错误")
            args = parser.parse_args()
            if args["out_type"] == "orders":
                result = GetOutOrder(args["time_range"], args["source_type"], args["is_new_user"],
                                     args["is_validorder"], args["period"], args["page"], args["page_nums"], "json")
            elif args["out_type"] == "datas":
                result = GetOutOrderDatas(args["time_range"], args["source_type"], args["is_new_user"],
                                          args["is_validorder"], args["page"], args["page_nums"], args["period"], args["Statistics_type"])
            else:
                return {"error_code": 0, "error_msg": "请正确填写返回类型 out_type -- (datas or orders)", "status": "success"}
            try:
                if result["error_code"] == -1:
                    return result
            except:
                return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": result}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}

class SpecialActivitySalesDatas(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("time_range", type=list,
                                location="json", help="时间范围校验错误")
            parser.add_argument("source_type", type=str,
                                location="json", help="渠道字段校验错误")
            parser.add_argument("is_new_user", type=str,
                                location="json", help="用户类型字段校验错误")
            parser.add_argument("is_validorder", type=str,
                                location="json", help="订单类型字段校验错误")
            parser.add_argument("period", type=str,
                                location="json", help="期数字段校验错误")
            parser.add_argument(
                "page", type=str, location="json", help="页数校验错误")
            parser.add_argument("page_nums", type=str,
                                location="json", help="每页条数校验错误")
            parser.add_argument("Statistics_type", type=str,
                                location="json", help="可视化类型字段校验错误")
            parser.add_argument("out_type", type=str,
                                location="json", help="订单类型字段校验错误")
            args = parser.parse_args()
            if args["out_type"] == "orders":
                result = GetOrderList(args["time_range"], args["source_type"], args["is_new_user"],
                                     args["is_validorder"], args["period"], args["page"], args["page_nums"], "json")
            elif args["out_type"] == "datas":
                result = GetOrderSalesDatas(args["time_range"], args["source_type"], args["is_new_user"],
                                          args["is_validorder"], args["page"], args["page_nums"], args["period"], args["Statistics_type"])
            else:
                return {"error_code": 0, "error_msg": "请正确填写返回类型 out_type -- (datas or orders)", "status": "success"}
            try:
                if result["error_code"] == -1:
                    return result
            except:
                return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": result}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}
        
class Auction_Aster(Resource):
    def get(self):
        try:
            # 获取db连接
            conn_auction, conn_auction_cursor = auction_pool.get_connection()
            now = get_now_to_aster()
            if now.hour == 23 and now.minute > 50:
                text = get_day_resstr(now, conn_auction)
            else:
                text = get_hour_resstr(now, conn_auction)
            response = auction_aster(now, text)
            # 归还db连接
            auction_pool.dispose(conn_auction, conn_auction_cursor)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            return {"error_code": -1, "error_msg": f"拍卖数据获取失败,失败信息-{e}", "status": "fail", "data": {}}


class OrderFrequency(Resource):
    def get(self):
        # try:
        uid = request.args.get('uid')
        start_date = request.args.get('start_date')

        # 验证 uid 是否为数字
        if uid is not None and not uid.isdigit():
            return "Error: uid must be a number."

        # 验证 start_date 是否为指定的日期格式
        if start_date is not None:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return "Error: start_date parameter must be in the format of 'YYYY-MM-DD'."

        respon = get_user_order_frequency(uid, start_date=start_date)
        return respon
        # except Exception as e:
        #     print(e)
        #     return {"error_code": -1, "error_msg": f"获取用户下单频次画像失败", "status": "fail", "data": {}}


class NewUserCreatedFrom(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("start_date", type=str,
                                location="json", help="开始日期校验错误")
            parser.add_argument("end_date", type=str,
                                location="json", help="结束日期校验错误")
            parser.add_argument(
                "page", type=str, location="json", help="页数校验错误")
            parser.add_argument("page_nums", type=str,
                                location="json", help="每页条数校验错误")
            args = parser.parse_args()
            result = get_newusercreatedfrom(
                args["start_date"], args["end_date"], args["page"], args["page_nums"])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": result}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class NewUserTransformStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("start_date", type=str,
                                location="json", help="开始日期校验错误")
            parser.add_argument("end_date", type=str,
                                location="json", help="结束日期校验错误")
            parser.add_argument(
                "page", type=str, location="json", help="页数校验错误")
            parser.add_argument("page_nums", type=str,
                                location="json", help="每页条数校验错误")
            args = parser.parse_args()
            result = get_new_user_transform_statistics(
                args["start_date"], args["end_date"], args["page"], args["page_nums"])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": result}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class Sales_situation_dashboard(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("start_hour_time", type=str,
                                location="json", help="开始时间字段校验错误")
            parser.add_argument("end_hour_time", type=str,
                                location="json", help="结束时间字段校验错误")
            parser.add_argument("period_type", type=str,
                                location="json", help="频道字段校验错误")
            parser.add_argument("import_type", type=str,
                                help="进口类型字段校验错误", location="json")
            parser.add_argument("buyer", type=str,
                                help="采购字段校验错误", location="json")
            parser.add_argument("goods_type", type=list, location="json")
            parser.add_argument("source_event", type=str, location="json")
            parser.add_argument("log", type=str, location="json")
            parser.add_argument(
                "page", type=int, help="初始页字段校验错误", location="json", required=True)
            parser.add_argument(
                "page_nums", type=int, help="页面条数字段校验错误", location="json", required=True)
            args = parser.parse_args()
            product_type = args['goods_type']
            response = get_result(args['start_hour_time'], args['end_hour_time'], args['period_type'], args['import_type'], args['buyer'],
                                  product_type, args['source_event'], args['log'], args['page'], args['page_nums'])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class Live_Sales_situation_dashboard(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("start_hour_time", type=str,
                                location="json", help="开始时间字段校验错误")
            parser.add_argument("end_hour_time", type=str,
                                location="json", help="结束时间字段校验错误")
            parser.add_argument("period_type", type=str,
                                location="json", help="频道字段校验错误")
            parser.add_argument("import_type", type=str,
                                help="进口类型字段校验错误", location="json")
            parser.add_argument("buyer", type=str,
                                help="采购字段校验错误", location="json")
            parser.add_argument("goods_type", type=list, location="json")
            parser.add_argument("log", type=str, location="json")
            parser.add_argument(
                "page", type=int, help="初始页字段校验错误", location="json", required=True)
            parser.add_argument(
                "page_nums", type=int, help="页面条数字段校验错误", location="json", required=True)
            args = parser.parse_args()
            product_type = args['goods_type']
            source_event = ''
            response = get_live_result(args['start_hour_time'], args['end_hour_time'], args['period_type'], args['import_type'], args['buyer'],
                                       product_type, source_event, args['log'], args['page'], args['page_nums'])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class Coupon_Board(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("start_date", type=str,
                                location="json", help="开始日期字段校验错误")
            parser.add_argument("end_date", type=str,
                                location="json", help="结束日期字段校验错误")
            parser.add_argument("coupon_type", type=list,
                                location="json", help="优惠券类型字段校验错误")
            parser.add_argument("sell_type", type=int,
                                location="json", help="是否在售字段校验错误")
            parser.add_argument(
                "sort_rule", type=dict, location="json", help="排序规则字段校验错误", required=True)
            parser.add_argument(
                "out_type", type=int, location="json", help="输出类型字段校验错误", required=True)
            parser.add_argument(
                "page", type=int, help="初始页字段校验错误", location="json", required=True)
            parser.add_argument(
                "limit", type=int, help="页面条数字段校验错误", location="json", required=True)
            args = parser.parse_args()
            page_nums = args['limit']
            response = get_coupon_main(args['start_date'], args['end_date'], args['coupon_type'], args['sell_type'], args['sort_rule'],
                                       args['out_type'], args['page'], page_nums)
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class Depatment_Comment(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("out_type", type=int,
                                location="json", help="返回列表类型字段校验错误")
            parser.add_argument("dept_id", type=str,
                                location="json", help="部门ID字段校验错误")
            parser.add_argument("realname", type=str,
                                location="json", help="名字字段校验错误")
            args = parser.parse_args()
            out_type = args["out_type"]
            if out_type == 1:
                response = get_depatment()
            elif out_type == 2:
                response = get_vest_comment(args["realname"], args["dept_id"])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class Take_Comment(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("admin_id", type=int,
                                location="json", help="管理员ID字段校验错误")
            parser.add_argument("is_open", type=int,
                                location="json", help="开关字段校验错误")
            args = parser.parse_args()
            response = update_open_state(args["admin_id"], args["is_open"])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class VestCommentStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("date_type", type=str,
                                location="json", help="统计时间类型字段校验错误")
            parser.add_argument("time_dimension", type=str,
                                location="json", help="时间标记字段校验错误")
            parser.add_argument("evaluation_type", type=int,
                                location="json", help="评论类型字段校验错误")
            args = parser.parse_args()
            response = get_evaluation_main_dff(
                args["date_type"], args["time_dimension"], args["evaluation_type"])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


class AuctionPullNewUserStatistics(Resource):
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("out_type", type=str,
                                location="json", help="输出类型字段校验错误")
            parser.add_argument("start_date", type=str,
                                location="json", help="开始日期字段校验错误")
            parser.add_argument("end_date", type=str,
                                location="json", help="结束日期字段校验错误")
            parser.add_argument(
                "page", type=int, help="初始页字段校验错误", location="json", required=True)
            parser.add_argument(
                "page_nums", type=int, help="页面条数字段校验错误", location="json", required=True)
            args = parser.parse_args()
            response = get_main_massage(
                args["out_type"], args["start_date"], args["end_date"], args["page"], args["page_nums"])
            return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "success", "data": [], "error_info": str(e)}


# class RegistereUserStatisticsDaily(Resource):
#     def post(self):
#         try:
#             parser = reqparse.RequestParser()
#             parser.add_argument("type", type=str,
#                                 location="json", help="类型校验错误", required=True)
#             parser.add_argument("count_key", type=str, location="json", help="统计字段校验错误", required=True,
#                                 choices=["day", "month"])
#             parser.add_argument(
#                 "date", type=str, location="json", help="时间校验错误", required=True)
#             parser.add_argument("delivery_store_id", type=str,
#                                 location="json", help="发货点id校验错误", required=True)
#             parser.add_argument("merchant_id", type=str,
#                                 location="json", help="商家id校验错误", required=True)
#             args = parser.parse_args()
#             conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
#             orders_conn, orders_conn_cursor = orders_pool.get_connection()
#             users_conn, users_conn_cursor = users_pool.get_connection()
#             commodities_conn, commodities_cursor = commodities_pool.get_connection()
#             response = get_Merchant_main_statistics(args['type'], args['count_key'], args['date'], args['delivery_store_id'],
#                                                     args['merchant_id'], conn_statics,  users_conn, commodities_conn)
#             data_statistic_pool.dispose(conn_statics, conn_statics_cursor)
#             orders_pool.dispose(orders_conn, orders_conn_cursor)
#             users_pool.dispose(users_conn, users_conn_cursor)
#             commodities_pool.dispose(commodities_conn, commodities_cursor)
#             return {"error_code": 0, "error_msg": "请求成功", "status": "success", "data": response}
#         except Exception as e:
#             return {"error_code": -1, "error_msg": f"{e}", "status": "fail", "data": {}}


# api.add_resource(LiveRealTimeStatistics, '/analysis/v3/data_Statistic/LiveRealTimeStatistics')  # 直播实时统计接口
# api.add_resource(LiveRealTimeStatisticsDetail, '/analysis/v3/data_Statistic/LiveRealTimeStatisticsDetail') # 直播实时统计详情接口
api.add_resource(OperationalComplianceRate,
                 '/analysis/v3/data_Statistic/OperationalComplianceRate')  # 运营达标率接口
api.add_resource(DailyMerchandiseStatistics,
                 '/analysis/v3/data_Statistic/DailyMerchandiseStatistics')  # 每日商品统计接口
api.add_resource(DailySalesProgress,
                 '/analysis/v3/data_Statistic/DailySalesProgress')  # 日销售进度接口
api.add_resource(YearSalesProgress,
                 '/analysis/v3/data_Statistic/YearSalesProgress')  # 年销售进度接口
api.add_resource(RegisteredUserStatistics,
                 '/analysis/v3/data_Statistic/RegisteredUserStatistics')  # 注册用户统计接口
api.add_resource(UndeliveredStatistics,
                 '/analysis/v3/data_Statistic/UndeliveredStatistics')  # 未发货统计接口
api.add_resource(UndeliveredStatisticsDetail,
                 '/analysis/v3/data_Statistic/UndeliveredStatisticsDetail')  # 未发货订单详情接口
api.add_resource(RegionalStatistics,
                 '/analysis/v3/data_Statistic/RegionalStatistics')  # 区域统计接口
api.add_resource(MerchantSecondRegion,
                 '/analysis/v3/data_Statistic/MerchantSecondRegion')  # 商家秒发区域统计接口
api.add_resource(OnlineGoodsRealtimeStatistics,
                 '/analysis/v3/data_Statistic/OnlineGoodsRealtimeStatistics')  # 线上商品即时统计接口
api.add_resource(UserActivityStatistics,
                 '/analysis/v3/data_Statistic/UserActivityStatistics')  # 用户时段统计接口
api.add_resource(
    OperationSales, '/analysis/v3/data_Statistic/OperationSales')  # 获取运营销售数据
api.add_resource(
    DataStaticsTask, '/analysis/v3/data_Statistic/DataStaticsTask')  # 统计数据定时任务开启接口
api.add_resource(
    DataStaticsTaskTest, '/analysis/v3/data_Statistic/DataStaticsTaskTest')  # 测试统计数据定时任务开启接口
# 用户活跃数据定时任务开启接口
api.add_resource(UserActiveInsert,
                 '/analysis/v3/data_Statistic/UserActiveInsert')
api.add_resource(
    UpdatePlanData, '/analysis/v3/data_Statistic/UpdatePlanData')  # 更新计划数据接口
api.add_resource(YearlySalesCount,
                 '/analysis/v3/data_Statistic/YearlySalesCount')  # 月实际销售额接口
api.add_resource(CountDeliversubordersOut,
                 '/analysis/v3/data_Statistic/CountDeliversubordersOut')  # 代发订单导出情况接口
# 广告投放情况数据看板接口
api.add_resource(PullTheNewUser, '/analysis/v3/data_Statistic/PullTheNewUser')
# 广告投放情况数据看板ip及注册来源筛选项接口
api.add_resource(Ip_Regfrom_Record,
                 '/analysis/v3/data_Statistic/Ip_Regfrom_Record')
api.add_resource(NewOrderUsersCount,
                 '/analysis/v3/data_Statistic/NewOrderUsersCount')  # 新购用户接口
api.add_resource(MarkNewUserTakeNewUserMark,
                 '/analysis/v3/data_Statistic/MarkNewUserTakeNewUserMark')  # 外部销售数据用户标识编辑接口
api.add_resource(MarkNewuserGetNewUserMark,
                 '/analysis/v3/data_Statistic/MarkNewuserGetNewUserMark')  # 外部销售数据用户标识浏览及开关接口
# 外部销售数据主页及数据统计页面接口
api.add_resource(MarkNewuserDatas,
                 '/analysis/v3/data_Statistic/MarkNewuserDatas')
# 专题活动销售数据
api.add_resource(SpecialActivitySalesDatas,
                 '/analysis/v3/data_Statistic/SpecialActivitySalesDatas')
# 拍卖大师(拍卖数据统计)接口
api.add_resource(Auction_Aster, '/analysis/v3/data_Statistic/Auction_Aster')
api.add_resource(
    OrderFrequency, '/analysis/v3/data_Statistic/order-frequency')  # 用户下单频次画像接口
api.add_resource(NewUserCreatedFrom,
                 '/analysis/v3/data_Statistic/NewUserCreatedFrom')  # 新用户注册来源接口
api.add_resource(NewUserTransformStatistics,
                 '/analysis/v3/data_Statistic/NewUserTransformStatistics')  # 新用户转化统计接口
api.add_resource(Sales_situation_dashboard,
                 '/analysis/v3/data_Statistic/Sales_situation_dashboard')  # 销售统计看板
api.add_resource(Live_Sales_situation_dashboard,
                 '/analysis/v3/data_Statistic/Live_Sales_situation_dashboard')  # 直播销售统计看板
api.add_resource(
    Coupon_Board, '/analysis/v3/data_Statistic/Coupon_Board')  # 优惠券统计看板接口
api.add_resource(Depatment_Comment,
                 '/analysis/v3/data_Statistic/Depatment_Comment')  # 马甲评论看板人员配置列表接口
# 马甲评论看板人员开关配置接口
api.add_resource(Take_Comment, '/analysis/v3/data_Statistic/Take_Comment')
api.add_resource(VestCommentStatistics,
                 '/analysis/v3/data_Statistic/VestCommentStatistics')  # 马甲评论看板接口
api.add_resource(AuctionPullNewUserStatistics,
                 '/analysis/v3/data_Statistic/AuctionPullNewUserStatistics')  # 拍卖拉新看板接口
# api.add_resource(RegistereUserStatisticsDaily,
#                  '/analysis/v3/data_Statistic/RegistereUserStatisticsDaily')  # 注册用户统计显示日详细数据接口
api.add_resource(QuerySearchKeywordsList,
                 '/analysis/v3/data_Statistic/QuerySearchKeywordsList')  # 商品搜索关键词统计列表
api.add_resource(QuerySearchKeywordsCurve,
                 '/analysis/v3/data_Statistic/QuerySearchKeywordsCurve')  # 商品搜索关键词统计曲线图

api.add_resource(SecondVisitInsert,
                 '/analysis/v3/data_Statistic/SecondVisitInsert') #统计秒发栏目、秒发卡片点击数据

api.add_resource(SecondVisitPvUvList,
                 '/analysis/v3/data_Statistic/SecondVisitPvUvList') #获取秒发栏目、秒发卡片点击数据

class OperationSalesTest(unittest.TestCase):
    def test_push_dingtalk(self):
        push_info_robot_by_queue_service("### 测试数据", "2021-09-07")


class TestOperationSales(unittest.TestCase):
    def test_get_operation_sales(self):
        # 获取db连接
        conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
        conn_orders, conn_orders_cursor = orders_pool.get_connection()
        conn_commodities, conn_commodities_cursor = commodities_pool.get_connection()
        now_time = get_param_time()
        res_json = get_operation_sales(now_time, conn_statics_cursor, conn_orders_cursor, conn_commodities_cursor,
                                       conn_orders, conn_commodities)
        text, curtime = assemble_data(res_json, now_time)
        print(curtime, text)


if __name__ == '__main__':
    print("Started.")
    # 启动服务
    waitress.serve(app, host='127.0.0.1', port='8000')
    print("End.")
