import calendar
from datetime import datetime,timedelta,date

def get_date_first_last(time_dimension):
    """
    获取指定月份开始结束日期字符串
    :param time_dimension 指定月份(如2023-06)
    :return:
    """
    year, month = map(int, time_dimension.split('-'))
    first_day = datetime(year, month, 1).strftime("%Y-%m-%d")

    if month == 12:
        next_month = 1
        next_year = year + 1
    else:
        next_month = month + 1
        next_year = year
    next_month_first_day = datetime(next_year, next_month, 1)
    last_day = (next_month_first_day - timedelta(days=1)).strftime("%Y-%m-%d")
    return first_day, last_day


def get_month_dates(year, month):
    """
    获取指定月份所有日期字符串
    :param year(当前年份)
    :param year(当前月份)
    :return:
    """
    cal = calendar.monthcalendar(year, month)
    dates = [day for week in cal for day in week if day != 0]
    date_list = []
    for date in dates:
        date_list.append(f"{year}-{month:02d}-{date:02d}")
    return date_list


def get_month_dates_to_today(year, month):
    """
    获取指定月份截止今天以前的所有日期字符串
    :param year: 当前年份
    :param month: 当前月份
    :return: 日期字符串列表
    """
    today = date.today()
    cal = calendar.monthcalendar(year, month)
    dates = [day for week in cal for day in week if day != 0 and date(year, month, day) <= today]
    date_list = [f"{year}-{month:02d}-{date:02d}" for date in dates]
    return date_list



def get_year_months(to_year:int,to_month:int):
    """
    获取本年截止今天以前的所有年月字符串
    :param to_year: 当前年份
    :param to_month: 当前月份
    :return: 日期字符串列表
    """
    year_months = []
    for month in range(1,to_month + 1):
        if len(str(month)) == 1:
            year_months.append(f"{to_year}-0{month}")
        else:
            year_months.append(f"{to_year}-{month}")
    return year_months
