import requests
import json
import pymysql
import pandas as pd

response = requests.session()


def conn_mysql(host, port, username, password, database):
    """
    链接数据库
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=username, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_live_order_detail(day, hour, period_type, import_type, buyer, goods_type, page, page_nums, commodities_conn, orders_conn,
                     wiki_conn):
    """
    获取订单详情
    :param day:
    :param hour:
    :param period_type:
    :param import_type:
    :param goods_type:
    :param page:
    :param page_nums:
    :return:
    """
    try:
        if int(hour) < 10:
            hour = f'0{hour}'
        sql_product_type = "select id,name from vh_product_type"
        all_product_type = pd.read_sql(sql_product_type, wiki_conn)
        all_ids = all_product_type['id'].tolist()
        goods_type_list = []
        _site1 = 1
        _site2 = 1
        if goods_type[0] == 0:
            for id in all_ids:
                goods_type_list.append(str(all_product_type[all_product_type['id'] == id]['name'].values[0]))
        else:
            for id in goods_type:
                goods_type_list.append(str(all_product_type[all_product_type['id'] == id]['name'].values[0]))
        if buyer ==None or buyer == "":
            buyer=""
        else:
            buyer_name_sql=f"select bb.`buyer_id`,bb.`buyer_name` from (select DISTINCT a.buyer_id from (select `buyer_id` from vh_commodities.vh_periods_cross where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id` from vh_commodities. vh_periods_flash where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id` from vh_commodities.vh_periods_second where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id` from vh_commodities.vh_periods_leftover where buyer_name is not null and `buyer_name`  not in (''))a)b left join (select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_cross where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_flash where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_second where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_leftover where buyer_name is not null and `buyer_name`  not in (''))bb on b.`buyer_id` = bb.`buyer_id`"
            df_buyer_name= pd.read_sql_query(buyer_name_sql, commodities_conn)
            df_buyer_name.set_index('buyer_name',inplace=True)
            df_buyer=df_buyer_name.T
            df_buyer_dict=df_buyer.to_dict('list')
            bid=df_buyer_dict.get(buyer)[0]
        if period_type == "0":
            # 获取统计数据
            sql_count = f"""SELECT period,payment_amount,order_qty,uid FROM `vh_flash_order` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}' AND FROM_UNIXTIME(created_time,'%H') = '{hour}' AND sub_order_status IN(1,2,3)"""
            df_count = pd.read_sql(sql_count, orders_conn)
            if len(df_count) == 0:
                dff_end_flash = pd.DataFrame(
                    columns=['period', 'payment_amount', 'order_qty', 'users_count', 'title', 'price','buyer_name'])
                total_flash = 0
            else:
                flash_periods = df_count['period'].tolist()
                # 获取产品数据
                dff_periods = pd.DataFrame(columns=['period', 'title', 'price','buyer_name'])
                if import_type == "":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} AND title like "【清仓直播】%%"  AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} AND title like "【清仓直播】%%"  AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods).reset_index(drop=True)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} AND title like "【清仓直播】%%"  AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} AND title like "【清仓直播】%%"  AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods).reset_index(drop=True)
                else:
                    if import_type == "0":
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                    elif import_type == "1":
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                    elif import_type == "2":
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id = {tuple(flash_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_flash` where id in {tuple(flash_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
                if dff_periods.empty:
                    dff_end_flash = pd.DataFrame(
                        columns=['period', 'payment_amount', 'order_qty', 'users_count', 'title', 'price','buyer_name'])
                    total_flash = 0
                else:
                    dff = pd.merge(df_count, dff_periods).reset_index(drop=True)
                    dff_payment_amount = dff[['period', 'payment_amount']].groupby('period').sum().reset_index()
                    dff_nums = dff[['period', 'order_qty']].groupby('period').sum().reset_index()
                    dff_order_users = dff[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().groupby(
                        'period').size().reset_index().rename(columns={0: 'users_count'})
                    dff_re = pd.merge(dff_payment_amount, dff_nums, on='period').merge(dff_order_users, on='period')
                    dff_end = pd.merge(dff_re, dff_periods)
                    dff_end_flash = dff_end.sort_values("payment_amount", ascending=False).reset_index(drop=True)
                    total_flash = len(dff_end)

            # 获取统计数据
            sql_count = f"""SELECT period,payment_amount,order_qty,uid FROM `vh_tail_order` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}' AND FROM_UNIXTIME(created_time,'%H') = '{hour}' AND sub_order_status IN(1,2,3)"""
            df_count = pd.read_sql(sql_count, orders_conn)
            if len(df_count) == 0:
                dff_end_tail = pd.DataFrame(
                    columns=['period', 'payment_amount', 'order_qty', 'users_count', 'title', 'price','buyer_name'])
                total_tail = 0
            else:
                tail_periods = df_count['period'].tolist()
                # 获取产品数据
                dff_periods = pd.DataFrame(columns=['period', 'title', 'price','buyer_name'])
                if import_type == "":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if import_type == "0":
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(tail_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(tail_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                    elif import_type == "1":
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(tail_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(tail_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                    elif import_type == "2":
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(tail_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(tail_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                                df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                                df_periods = df_periods1
                                dff_periods = dff_periods.append(df_periods)
                dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
                if dff_periods.empty:
                    dff_end_tail = pd.DataFrame(
                        columns=['period', 'payment_amount', 'order_qty', 'users_count', 'title', 'price','buyer_name'])
                    total_tail = 0
                else:
                    dff = pd.merge(df_count, dff_periods).reset_index(drop=True)
                    dff_payment_amount = dff[['period', 'payment_amount']].groupby('period').sum().reset_index()
                    dff_nums = dff[['period', 'order_qty']].groupby('period').sum().reset_index()
                    dff_order_users = dff[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().groupby(
                        'period').size().reset_index().rename(columns={0: 'users_count'})
                    dff_re = pd.merge(dff_payment_amount, dff_nums, on='period').merge(dff_order_users, on='period')
                    dff_end = pd.merge(dff_re, dff_periods)
                    dff_end_tail = dff_end.sort_values("payment_amount", ascending=False).reset_index(drop=True)
                    total_tail = len(dff_end)
            dff_end = dff_end_flash.append(dff_end_tail).reset_index(drop=True)
            total = total_flash + total_tail

        elif period_type == "1":
            # 获取统计数据
            sql_count = f"""SELECT period,payment_amount,order_qty,uid FROM `vh_cross_order` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}' AND FROM_UNIXTIME(created_time,'%H') = '{hour}' AND sub_order_status IN(1,2,3)"""
            df_count = pd.read_sql(sql_count, orders_conn)
            if len(df_count) == 0:
                return {"count_json": [], "total": 0}
            cross_periods = df_count['period'].tolist()
            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price','buyer_name'])
            if import_type == "":
                if buyer != "":
                    for per_type in goods_type_list:
                        if len(cross_periods) == 1:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                        else:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                        df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                        df_periods = df_periods1
                        dff_periods = dff_periods.append(df_periods)
                else:
                    for per_type in goods_type_list:
                        if len(cross_periods) == 1:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                        else:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                        df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                        df_periods = df_periods1
                        dff_periods = dff_periods.append(df_periods)
            else:
                if import_type == "0":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(cross_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(cross_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                elif import_type == "1":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(cross_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(cross_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                elif import_type == "2":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(cross_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(cross_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id = {tuple(cross_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_cross` where id in {tuple(cross_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
            dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
            if dff_periods.empty:
                return {"count_json": [], "total": 0}
            dff = pd.merge(df_count, dff_periods).reset_index(drop=True)
            dff_payment_amount = dff[['period', 'payment_amount']].groupby('period').sum().reset_index()
            dff_nums = dff[['period', 'order_qty']].groupby('period').sum().reset_index()
            dff_order_users = dff[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().groupby(
                'period').size().reset_index().rename(columns={0: 'users_count'})
            dff_re = pd.merge(dff_payment_amount, dff_nums, on='period').merge(dff_order_users, on='period')
            dff_end = pd.merge(dff_re, dff_periods)
            dff_end = dff_end.sort_values("payment_amount", ascending=False).reset_index(drop=True)
            total = len(dff_end)
        elif period_type == "2":
            # 获取统计数据
            sql_count = f"""SELECT period,payment_amount,order_qty,uid FROM `vh_second_order` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}' AND FROM_UNIXTIME(created_time,'%H') = '{hour}' AND sub_order_status IN(1,2,3)"""
            df_count = pd.read_sql(sql_count, orders_conn)
            if len(df_count) == 0:
                return {"count_json": [], "total": 0}
            second_periods = df_count['period'].tolist()
            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price','buyer_name'])
            if import_type == "":
                if buyer != "":
                    for per_type in goods_type_list:
                        if len(second_periods) == 1:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                        else:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                        df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                        df_periods = df_periods1
                        dff_periods = dff_periods.append(df_periods)
                else:
                    for per_type in goods_type_list:
                        if len(second_periods) == 1:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                        else:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                        df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                        df_periods = df_periods1
                        dff_periods = dff_periods.append(df_periods)
            else:
                if import_type == "0":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(second_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(second_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                elif import_type == "1":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(second_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(second_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                elif import_type == "2":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(second_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(second_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id = {tuple(second_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_second` where id in {tuple(second_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
            dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
            if dff_periods.empty:
                return {"count_json": [], "total": 0}
            dff = pd.merge(df_count, dff_periods).reset_index(drop=True)
            dff_payment_amount = dff[['period', 'payment_amount']].groupby('period').sum().reset_index()
            dff_nums = dff[['period', 'order_qty']].groupby('period').sum().reset_index()
            dff_order_users = dff[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().groupby(
                'period').size().reset_index().rename(columns={0: 'users_count'})
            dff_re = pd.merge(dff_payment_amount, dff_nums, on='period').merge(dff_order_users, on='period')
            dff_end = pd.merge(dff_re, dff_periods)
            dff_end = dff_end.sort_values("payment_amount", ascending=False).reset_index(drop=True)
            total = len(dff_end)
        elif period_type == "3":
            # 获取统计数据
            sql_count = f"""SELECT period,payment_amount,order_qty,uid FROM `vh_tail_order` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}' AND FROM_UNIXTIME(created_time,'%H') = '{hour}' AND sub_order_status IN(1,2,3)"""
            df_count = pd.read_sql(sql_count, orders_conn)
            if len(df_count) == 0:
                return {"count_json": [], "total": 0}
            tail_periods = df_count['period'].tolist()
            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price','buyer_name'])
            if import_type == "":
                if buyer != "":
                    for per_type in goods_type_list:
                        if len(tail_periods) == 1:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                        else:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                        df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                        df_periods = df_periods1
                        dff_periods = dff_periods.append(df_periods)
                else:
                    for per_type in goods_type_list:
                        if len(tail_periods) == 1:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                        else:
                            sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type in (0,1,2) AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                        df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                        df_periods = df_periods1
                        dff_periods = dff_periods.append(df_periods)
            else:
                if import_type == "0":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 0 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                elif import_type == "1":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 1 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                elif import_type == "2":
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(tail_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id = {tuple(tail_periods)[0]} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price,buyer_name FROM `vh_periods_leftover` where id in {tuple(tail_periods)} and import_type = 2 AND title like "【清仓直播】%%" AND product_category like '%{per_type}%'"""
                            df_periods1 = pd.read_sql(sql_periods_flash, commodities_conn)
                            df_periods = df_periods1
                            dff_periods = dff_periods.append(df_periods)
            dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
            if dff_periods.empty:
                return {"count_json": [], "total": 0}
            dff = pd.merge(df_count, dff_periods).reset_index(drop=True)
            dff_payment_amount = dff[['period', 'payment_amount']].groupby('period').sum().reset_index()
            dff_nums = dff[['period', 'order_qty']].groupby('period').sum().reset_index()
            dff_order_users = dff[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().groupby(
                'period').size().reset_index().rename(columns={0: 'users_count'})
            dff_re = pd.merge(dff_payment_amount, dff_nums, on='period').merge(dff_order_users, on='period')
            dff_end = pd.merge(dff_re, dff_periods)
            dff_end = dff_end.sort_values("payment_amount", ascending=False).reset_index(drop=True)
            total = len(dff_end)
        dff_end = dff_end[(page * page_nums - page_nums):page * page_nums]
        dff_end = dff_end.rename(columns={'price': 'market_price'})
        res_json = {
            "count_json": json.loads(dff_end.to_json(orient="records")),
            "total": total
        }
        return res_json
    except Exception as e:
        raise Exception(e)
