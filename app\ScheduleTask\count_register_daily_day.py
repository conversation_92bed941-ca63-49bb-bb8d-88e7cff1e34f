import pymysql
import pandas as pd
from datetime import datetime, timedelta
from config import ChannelTypeConfig
import warnings
from datetime import datetime, tzinfo, timezone
from pymongo import MongoClient
import unittest
import concurrent
from app.CommonLibraries.getConfig import get_config
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str
import json

warnings.filterwarnings("ignore")



# 注册用户日统计

def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")



def get_last_day():
    last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    return last_day


def get_plans(day, rr_host, rr_port, rr_user, rr_password):
    """
    获取计划数据
    :param day:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_data_statistics")
    sql = f"""SELECT `date`,week,remarks FROM vh_sales_analysis_plan WHERE date = '{day}'"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df


def get_re_users(day, rr_host, rr_port, rr_user, rr_password):
    """
    获取注册用户
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_user")
    sql = f"""SELECT uid FROM `vh_user` WHERE FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}'"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df


def get_sales(day, table, tuple_uid, rr_host, rr_port, rr_user, rr_password):
    """
    获取销售数据
    :param day:
    :param table:
    :param tuple_uid:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    cursor = conn.cursor()
    sql = f"""SELECT SUM(payment_amount) 'sales' 
             FROM {table} 
             WHERE sub_order_status IN (1,2,3) 
             AND FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}'
             AND uid in {tuple_uid}"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data[0] is None:
        sales = 0
    else:
        sales = float(data[0])
    conn.close()
    return sales


def get_order_users(tuple_uid, day, rr_host, rr_port, rr_user, rr_password):
    """
    获取下单人数
    :param tuple_uid:
    :param day:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    tables = ["vh_flash_order", "vh_cross_order", "vh_second_order", "vh_tail_order", "vh_rabbit_order"]
    uid_list = []
    for table in tables:
        sql = f"""SELECT uid 
                  FROM {table} 
                  WHERE sub_order_status IN (1,2,3) 
                  AND FROM_UNIXTIME(created_time,'%Y-%m-%d') = '{day}'
                  AND uid IN {tuple_uid}"""
        df = pd.read_sql(sql, conn)
        for uid in df['uid'].tolist():
            uid_list.append(uid)
    order_users = len(list(set(uid_list)))
    conn.close()
    return order_users


'''
    统计每日活跃用户数据（独立用户)
    <AUTHOR>
    @date 2022/09/08
'''
def get_daily_active_user_count() -> int:
    d = datetime.strptime(get_last_day(),'%Y-%m-%d')
    client = MongoClient('**************************************************************************************************')
    result = client['vinehoo_v3']['gateway_logs'].aggregate(
        [{
        '$match': {
            'request_time': {
                '$gte': datetime(d.year, d.month, d.day, 0, 0, 0, tzinfo=timezone.utc), 
                '$lte': datetime(d.year, d.month, d.day, 23, 59, 59, tzinfo=timezone.utc)
            }
        }
    }, {
        '$group': {
            '_id': '$uid'
        }
    }, {
        '$project': {
            'uid': '$_id'
        }
    }, {
        '$count': 'uid'
    }
    ])
    for i in result:
        return i["uid"]


def get_new_order_users(rr_host, rr_port, rr_user, rr_password):

    """
    统计前一天新购用户
    :param time:
    :return:
    """

    Cg=ChannelTypeConfig.channel_type_config
    tables_pool=[Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]
    orders_conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    start_time=int((pd.to_datetime(get_last_day() +" "+"00:00:00")-timedelta(hours=8)).timestamp())
    end_time=int((pd.to_datetime(get_last_day() +" "+"23:59:59")-timedelta(hours=8)).timestamp())
    new_tabel_count=-1
    for table in tables_pool:
        new_tabel_count+=1
        new_order_users_sql=f"select distinct uid from `{table}` where sub_order_status in (1,2,3) and created_time between {start_time} and {end_time}"
        new_order_users_df=pd.read_sql_query(new_order_users_sql,orders_conn)
        if new_tabel_count==0:
            new_order_users_flash=new_order_users_df
        elif new_tabel_count==1:
            new_order_users_cross=new_order_users_df
        elif new_tabel_count==2:
            new_order_users_second=new_order_users_df
        elif new_tabel_count==3:
            new_order_users_tail=new_order_users_df
        else:
            pass
    new_order_users_main_df=pd.concat([new_order_users_flash,new_order_users_cross,new_order_users_second,new_order_users_tail],axis=0)
    new_order_users_main_df.drop_duplicates(keep="first",inplace=True)
    new_order_uid_list=new_order_users_main_df.uid.tolist()
    old_tabel_count=-1
    for table in tables_pool:
        old_tabel_count+=1
        if len(new_order_uid_list) == 0:
            old_order_users_df=pd.DataFrame(columns=["uid"])
        elif len(new_order_uid_list) == 1:
            old_order_users_sql=f"select distinct uid from `{table}` where sub_order_status in (1,2,3) and created_time < {start_time} and uid = {new_order_uid_list[0]}"
            old_order_users_df=pd.read_sql_query(old_order_users_sql,orders_conn)
        elif len(new_order_uid_list) > 1:
            old_order_users_sql=f"select distinct uid from `{table}` where sub_order_status in (1,2,3) and created_time < {start_time} and uid in {tuple(new_order_uid_list)}"
            old_order_users_df=pd.read_sql_query(old_order_users_sql,orders_conn)
        if old_tabel_count==0:
            old_order_users_flash=old_order_users_df
        elif old_tabel_count==1:
            old_order_users_cross=old_order_users_df
        elif old_tabel_count==2:
            old_order_users_second=old_order_users_df
        elif old_tabel_count==3:
            old_order_users_tail=old_order_users_df
        else:
            pass
    old_order_users_main_df=pd.concat([old_order_users_flash,old_order_users_cross,old_order_users_second,old_order_users_tail],axis=0)
    old_order_users_main_df.drop_duplicates(keep="first",inplace=True)
    now_old_uid=old_order_users_main_df.uid.tolist()
    if len(now_old_uid) == 0:
        new_order_user_count=new_order_users_main_df.uid.count()
    elif len(now_old_uid) == 1:
        new_order_user_count=new_order_users_main_df[new_order_users_main_df.uid != now_old_uid[0]].uid.count()
    if len(now_old_uid) > 1:
        new_order_user=new_order_users_main_df[new_order_users_main_df.uid.apply(lambda x:x not in tuple(now_old_uid))]
        new_order_user_count=new_order_user.uid.count()
    else:
        pass
    no_dict={
        "count": 0,
        "error_code": 0,
        "error_msg": ""
    }
    no_json=json.loads(json.dumps(no_dict,ensure_ascii=False))
    if len(old_order_users_main_df.uid.tolist()) == 0:
        return no_json
    new_order_user_dict ={
        "count": int(new_order_user_count),
    }
    new_order_user_json=json.loads(json.dumps(new_order_user_dict,ensure_ascii=False))
    return new_order_user_json


def get_buyuser_counts(day,table):
    """
    统计当天下单用户
    :param day:
    :param table:
    :return:
    """

    orders_conn = nopool_conn("vh_orders").connect()
    buyuser_sql = f""" SELECT `uid` FROM {table} WHERE FROM_UNIXTIME(created_time,'%%Y-%%m-%%d') = '{day}' AND sub_order_status in (1,2,3) AND payment_amount - refund_money != 0"""
    res = pd.read_sql_query(buyuser_sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return res


def deal_with_data(rr_host, rr_port, rr_user, rr_password):
    last_day = get_last_day()
    # 获取计划数据
    df_plan = get_plans(last_day, rr_host, rr_port, rr_user, rr_password)
    Cg=ChannelTypeConfig.channel_type_config
    tables=[Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]
    date = df_plan['date'][0]
    week = df_plan['week'][0]
    remark = df_plan['remarks'][0]
    if remark is None or remark == '':
        remark = "无"
    # 获取注册用户id
    df_re_user = get_re_users(last_day, rr_host, rr_port, rr_user, rr_password)
    uid_tuple = tuple(df_re_user['uid'].tolist())
    # 注册人数
    registrants_number = len(uid_tuple)
    # 获取闪购销售额
    flash_sales = get_sales(last_day, "vh_flash_order", uid_tuple, rr_host, rr_port, rr_user, rr_password)
    # 获取跨境销售额
    cross_sales = get_sales(last_day, "vh_cross_order", uid_tuple, rr_host, rr_port, rr_user, rr_password)
    # 获取秒发销售额
    second_sales = get_sales(last_day, "vh_second_order", uid_tuple, rr_host, rr_port, rr_user, rr_password)
    # 获取总销售额
    all_tables = ["vh_flash_order", "vh_cross_order", "vh_second_order", "vh_tail_order"]
    total_sales_list = []
    for table in all_tables:
        money = get_sales(last_day, table, uid_tuple, rr_host, rr_port, rr_user, rr_password)
        total_sales_list.append(money)
    total_sales = sum(total_sales_list)
    order_users = get_order_users(uid_tuple, last_day, rr_host, rr_port, rr_user, rr_password)
    new_users = get_new_order_users(rr_host, rr_port, rr_user, rr_password)["count"]
    daily_active = get_daily_active_user_count()
    month = f"{last_day.split('-')[0]}-{last_day.split('-')[1]}"
    # 获取下单人数
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        tasks = [executor.submit(get_buyuser_counts,last_day,table) for table in tables]
        Main_Data = pd.DataFrame(columns=["uid"])
        for future in concurrent.futures.as_completed(tasks):
            Main_Data=Main_Data.append(future.result()).reset_index(drop=True)
    buyusers = len(set(Main_Data.uid.tolist()))
    insert_json = {
        "date": date,
        "week": week,
        "registrants_number": registrants_number,
        "flash_sales": flash_sales,
        "cross_sales": cross_sales,
        "second_sales": second_sales,
        "total_sales": total_sales,
        "remark": remark,
        "order_users": order_users,
        "new_users" : new_users,
        "buyuers" :buyusers,
        "daily_active": daily_active,
        "month": month
    }
    return insert_json

def exits_data(date, cursor):
    """
    验证是否已存在
    :param date: 日期
    :param cursor
    :return:
    """
    sql = f"""select * from vh_sales_analysis_daily_user where `date` = '{date}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_sales_analysis_daily_user where `date` = '{date}'"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(insert_json, rm_host, rm_port, rm_user, rm_password):
    conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
    cursor = conn.cursor()
    dict_keys = list(insert_json.keys())
    exits_data(insert_json[dict_keys[0]], cursor)
    sql = f"""insert into vh_sales_analysis_daily_user 
             (`date`, `week`, `registrants_number`, `flash_sales`, `crosss_sales`, `second_sales`, `total_sales`,
             `remark`, `order_users`,`new_users`,buyuers,`daily_active`,`month`) 
             values('{insert_json[dict_keys[0]]}','{insert_json[dict_keys[1]]}',{insert_json[dict_keys[2]]},
             {insert_json[dict_keys[3]]},{insert_json[dict_keys[4]]},{insert_json[dict_keys[5]]},
             {insert_json[dict_keys[6]]},'{insert_json[dict_keys[7]]}',{insert_json[dict_keys[8]]},{insert_json[dict_keys[9]]},
             '{insert_json[dict_keys[10]]}','{insert_json[dict_keys[11]]}','{insert_json[dict_keys[12]]}')"""
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
    finally:
        conn.close()


def handler_register_daily(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        insert_json = deal_with_data(rr_host, rr_port, rr_user, rr_password)
        insert_mysql(insert_json, rm_host, rm_port, rm_user, rm_password)
        return 1
    except Exception as e:
        print(e)
        return -1

class Test(unittest.TestCase):
    def test(self):
        get_daily_active_user_count()
        
if __name__ == '__main__':
    # unittest.main()
    pass