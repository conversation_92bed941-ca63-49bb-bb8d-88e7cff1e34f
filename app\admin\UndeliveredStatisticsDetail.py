from config import ChannelTypeConfig
import pandas as pd
import json
from app.CommonLibraries.GetNowTime import time_str_to_timestamp, get_now_time


def get_undelivered_statistics_detail(args, conn_orders, conn_commodities):
    """
    获取未发货订单详情
    :return:
    """
    try:
        period = args['period']
        period_type = args['period_type']
        is_stage = args['is_stage']
        order_table = ChannelTypeConfig.channel_type_config[period_type][0]
        period_table = ChannelTypeConfig.channel_type_config[period_type][1]
        sql = f"""select sub_order_no,
                  period,
                  order_qty,
                  payment_amount,
                  order_type,
                  remarks,
                  CASE sub_order_status
                  WHEN 1 THEN '已支付'
                  ELSE '其它' 
                  END 'sub_order_status',
                  FROM_UNIXTIME(payment_time) 'payment_time',
                  FROM_UNIXTIME(predict_time) 'predict_time', 
                  DATEDIFF(NOW(),FROM_UNIXTIME(predict_time)) 'over_days' 
                  from {order_table} 
                  where sub_order_status = 1 
                  and FROM_UNIXTIME(predict_time) < NOW() AND period = {period}"""

        # 查询非暂存以逾期订单
        if is_stage != 0:
            if is_stage == 1:
                sql = f""" {sql} AND is_ts = 1"""
            else:
                sql = f""" {sql} AND is_ts = 0"""
        else:
            sql = f""" {sql} """
        df_orders = pd.read_sql(sql, conn_orders)
        df_orders['payment_time'] = df_orders['payment_time'].astype('str')
        df_orders['predict_time'] = df_orders['predict_time'].astype('str')
        df_orders['predict_time'] = df_orders['predict_time'].apply((lambda x: f'{x} 00:00:00' if len(x) == 10 else x))
        if df_orders.empty:
            return []
        sql = f"""select id 'period',title from {period_table} where id = {period}"""
        df_goods = pd.read_sql(sql, conn_commodities)
        if df_goods.empty:
            return []
        dff = pd.merge(df_orders, df_goods).drop('period', axis=1)
        res_json = dff.to_json(orient='records')
        return json.loads(res_json)
    except Exception as e:
        raise Exception(f"获取未发货订单详情失败,失败信息:{e}")
