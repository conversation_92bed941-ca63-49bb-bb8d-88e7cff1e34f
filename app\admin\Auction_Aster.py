import json
import time
import base64
import requests
import warnings
import pandas as pd
from pymongo import MongoClient
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str
from datetime import datetime, timedelta, timezone
from config import DingtalkAccessToken,ChannelTypeConfig
from app.admin.Auction_Aster_plot import get_plot_robot


warnings.filterwarnings("ignore")


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def get_record_uid(start_time,over_time) -> object:
    """
    获取首次参拍用户
    :param now:
    :return:
    """
    try:
        auction_conn = nopool_conn("vh_auction").connect()
        record_sql = f""" select uid,min(created_time) AS 'created_time' from vh_goods_bid_record where is_vest = 0 group by `uid` having `created_time` between {start_time} and {over_time}"""
        record_dff = pd.read_sql_query(record_sql,auction_conn)
        auction_conn.close()
        nopool_conn("vh_auction").dispose()
        return record_dff[["uid"]]
    except Exception  as e:
        print(f"顾客消费历史信息获取失败:{e}")
        return -1
    
    
    

def get_now_to_aster() -> tuple:
    """
    获取当前时间
    :return:
    """
    now = datetime.now()
    return now


def s_o_times(now:tuple) -> tuple:
    """
    获取前一个小时起止时间戳
    :param now:
    :return:
    """
    start_time = int((now - timedelta(hours=1)).timestamp())
    over_time = int(now.timestamp())
    return start_time,over_time

def one_day_times(now:tuple) ->tuple:
    """
    获取当天起止时间戳
    :param now:
    :return:
    """
    start_time = int(datetime.strptime(f"""{now.year}-{now.month}-{now.day} 00:00:00""","%Y-%m-%d %H:%M:%S").timestamp())
    over_time = int(datetime.strptime(f"""{now.year}-{now.month}-{now.day} 23:59:59""","%Y-%m-%d %H:%M:%S").timestamp())
    return start_time,over_time


def get_active_user_count(gte_time,lte_time) -> int:
    """
    获取UV
    :param gte_time:
    :param lte_time:
    :return:
    """
    try:
        client = MongoClient('**************************************************************************************************')
        result = client['vinehoo_v3']['gateway_logs'].aggregate(
            [{
            '$match': {
                'request_time': {
                    '$gte': datetime(gte_time.tm_year, gte_time.tm_mon, gte_time.tm_mday, gte_time.tm_hour, gte_time.tm_min, tzinfo=timezone.utc), 
                    '$lte': datetime(lte_time.tm_year, lte_time.tm_mon, lte_time.tm_mday, lte_time.tm_hour, lte_time.tm_min, tzinfo=timezone.utc)
                },
            'route_url':"/auction-goods/v3/goods/getGoodsDetail"
            }
        }, {
            '$group': {
                '_id': '$uid'
            }
        }, {
            '$project': {
                'uid': '$_id'
            }
        }, {
            '$count': 'uid'
        }
        ])
        for i in result:
            return i["uid"]
    except Exception as e:
        print(f"UV获取失败:{e}")
        return -1

    


def get_active_count(gte_time,lte_time) -> int:
    """
    获取PV
    :param gte_time:
    :param lte_time:
    :return:
    """
    try:
        client = MongoClient('**************************************************************************************************')
        result = client['vinehoo_v3']['gateway_logs'].aggregate(
            [{
            '$match': {
                'request_time': {
                    '$gte': datetime(gte_time.tm_year, gte_time.tm_mon, gte_time.tm_mday, gte_time.tm_hour, gte_time.tm_min, tzinfo=timezone.utc), 
                    '$lte': datetime(lte_time.tm_year, lte_time.tm_mon, lte_time.tm_mday, lte_time.tm_hour, lte_time.tm_min, tzinfo=timezone.utc)
                },
            'route_url':"/auction-goods/v3/goods/getGoodsDetail"
            }
        },{
            '$project': {
                'uid': '$_id'
            }
        }, {
            '$count': 'uid'
        }
        ])
        for i in result:
            return i["uid"]
    except Exception as e:
        print(f"PV获取失败:{e}")
        return -1



def get_res_json(start_time:int,over_time:int,PV:int,UV:int,conn_auction) -> dict:
    """
    获取拍卖数据统计
    :param start_time:
    :param over_time:
    :param PV:
    :param UV:
    :param conn_auction:
    :return:
    """
    try:
        Cg = ChannelTypeConfig.channel_type_config
        conn_user = nopool_conn("vh_user").connect()
        tables = [Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]
        goods_sql = f"""select id from vh_goods where closing_auction_time >= {over_time} and created_time <={over_time} and onsale_status in (1,2,3,4) and id not in (46,54,63,74,123,131)"""
        goods_df = pd.read_sql_query(goods_sql,conn_auction)
        goods_id_list = tuple(set(goods_df.id.tolist()))
        goods_count = goods_df.id.count()
        if goods_df.empty:
            goods_count = 0
            payment_amount = 0
            gross_margin_num = 0
            orders_count = 0
        else:
            vest_sql = f""" select uid from vh_goods_bid_record where created_time between {start_time} and {over_time} and is_vest = 0"""
            vests = tuple(set(pd.read_sql_query(vest_sql,conn_auction).uid.tolist()))
            orders_sql = f""" select order_no,goods_id,payment_amount,product_code,1 as 'orders_count' from vh_orders where payment_time between {start_time} and {over_time} and order_status in (1,2,3)"""# and uid in {vests}"""
            orders_df = pd.read_sql_query(orders_sql,conn_auction)
            if orders_df.empty:
                payment_amount = 0
                orders_count = 0
                gross_margin_num = 0
            goods_ids = tuple(set(orders_df.goods_id.tolist()))
            if len(goods_ids) == 0:
                gross_margin_num = 0
            else:
                if len(goods_ids) == 1:
                    goods_cost_sql = f""" select id,title,cost_price from vh_goods where id = {goods_ids[0]}"""
                else:
                    goods_cost_sql = f""" select id,title,cost_price from vh_goods where id in {goods_ids}"""
                goods_cost_df = pd.read_sql_query(goods_cost_sql,conn_auction)
                if goods_cost_df.empty:
                    gross_margin_num = 0
                else:
                    main_df = pd.merge(orders_df[["order_no","goods_id","product_code","payment_amount"]],goods_cost_df[["id","title","cost_price"]],how="left",left_on="goods_id",right_on="id")
                    main_df["product_code"] = main_df.product_code.apply(lambda x:eval(x))
                    main_df["cost_price"] = main_df.cost_price.fillna(0)
                    main_df["product_code_len"] = main_df.product_code.apply(lambda x:len(x))
                    main_df_one = main_df[main_df.product_code_len == 1]
                    main_df_any = main_df[main_df.product_code_len != 1]
                    main_df_any = main_df_any.explode(["product_code","cost_price"]).reset_index(drop=True)
                    main_df_any.loc[main_df_any.duplicated(subset='order_no', keep='first'), 'payment_amount'] = 0
                    main_df = main_df_one.append(main_df_any).reset_index(drop=True)
                    main_df["nums"] = main_df.product_code.apply(lambda x:int(x[0]["nums"]))
                    payment_amount = orders_df.payment_amount.sum()
                    def  calculate(row):
                        if  '整箱'  in  row['title']:
                            return  row['payment_amount']  -  row['cost_price']
                        else:
                            return  row['payment_amount']  -  row['cost_price']  *  row['nums']
                    gross_margin_num = main_df.apply(calculate,  axis=1).sum()   
                    #gross_margin_num = (main_df.payment_amount - main_df.cost_price * main_df.nums).sum()
                    orders_count = orders_df.orders_count.sum()
        if len(goods_id_list) == 0:
            record_user = 0
            record_counts = 0
            record_goods = 0
        record_sql = f""" select id,uid,goods_id from vh_goods_bid_record where created_time between {start_time} and {over_time} and is_vest = 0"""
        record_df = pd.read_sql_query(record_sql,conn_auction)
        if record_df.empty:
            record_user = 0
            record_counts = 0
            record_goods = 0
        else:
            record_user = record_df.drop_duplicates(subset="uid",keep="first").uid.count()
            record_counts = record_df.id.count()
            record_goods = record_df.drop_duplicates(subset="goods_id",keep="first").goods_id.count()
        auction_user_orders_df= get_record_uid(start_time,over_time)
        created_new_user_sql = f""" select uid from vh_user where created_time between {start_time} and {over_time} and tripartite_source = 'vinehoo-auction_moments' """
        created_new_user_df = pd.read_sql_query(created_new_user_sql,conn_user)
        if created_new_user_df.empty and auction_user_orders_df.empty:
            created_user_counts = 0
            first_record_counts = 0
        else:
            created_user_counts = len(created_new_user_df)
            first_record_counts = len(auction_user_orders_df)
        res_json = {
            "record_user": record_user,
            "record_counts": record_counts,
            "record_goods":record_goods,
            "PV": PV,
            "UV": UV,
            "goods_count": goods_count,
            "orders_count": orders_count,
            "payment_amount": payment_amount,
            "gross_margin_num": gross_margin_num,
            "created_user_counts": created_user_counts,
            "first_record_counts": first_record_counts
        }
        return res_json
    except Exception as e:
        print(f"拍卖数据统计获取失败:{e}")
        return -1



def get_hour_resstr(now,conn_auction) -> str:
    """
    组装传入企微接口字符
    :param now:
    :return:
    """
    try:
        start_time,over_time = s_o_times(now)
        gte_time = time.localtime(start_time)
        lte_time = time.localtime(over_time)
        UV = get_active_user_count(gte_time,lte_time)
        PV = get_active_count(gte_time,lte_time)
        res_json = get_res_json(start_time,over_time,PV,UV,conn_auction)
        hour_resstr  = f"""### 拍卖数据V3 \n\n > {now.year}/{now.month}/{now.day} {(now - timedelta(hours=1)).hour}:00 - {now.hour}:00 \n\n **浏览量:** {res_json['PV']} \n\n **访客数:** {res_json['UV']} \n\n **参拍人数:** {res_json['record_user']} \n\n **拍品数:** {str(res_json['record_goods'])}/{res_json['goods_count']} \n\n **出价数:** {res_json['record_counts']} \n\n **订单数** {res_json['orders_count']} \n\n **销售额** ￥{round(res_json['payment_amount'])} \n\n **毛利值:** ￥{round(res_json['gross_margin_num'])} \n\n **标识注册人数:** {res_json['created_user_counts']} \n\n **首次出价人数:** {res_json['first_record_counts']}"""
        return hour_resstr
    except Exception as e:
        print(f"小时数据字符组装失败:{e}")
        return -1


def get_day_resstr(now,conn_auction) -> str:
    try:
        if now.hour == 23 and now.minute == 59:
            start_time,over_time = one_day_times(now)
            gte_time = time.localtime(start_time)
            lte_time = time.localtime(over_time)
            UV = get_active_user_count(gte_time,lte_time)
            PV = get_active_count(gte_time,lte_time)
            res_json = get_res_json(start_time,over_time,PV,UV,conn_auction)
            hour_resstr  = f"""### 拍卖数据V3 \n\n > {now.year}/{now.month}/{now.day} <今日小结> \n\n **浏览量:** {res_json['PV']} \n\n **访客数:** {res_json['UV']} \n\n **参拍人数:** {res_json['record_user']} \n\n **拍品数:** {res_json['record_goods']}/{res_json['goods_count']} \n\n **出价数:** {res_json['record_counts']} \n\n **订单数** {res_json['orders_count']} \n\n **销售额** ￥{round(res_json['payment_amount'])} \n\n **毛利值:** ￥{round(res_json['gross_margin_num'])} \n\n **标识注册人数:** {res_json['created_user_counts']} \n\n **首次出价人数:** {res_json['first_record_counts']}"""
            return hour_resstr
        else:
            return "请在23点59分调用该方法"
    except Exception as e:
        print(f"今日小结字符组装失败:{e}")
        return -1
    
    
    
'''
    使用推送队列的方式发送消息到钉钉机器人
    @author:vber
    @date:2022-09-07
'''


def push_info_robot_by_queue_service(now,text) -> dict:
    try:
        headers = {
            'Content-Type': 'application/json',
            'vinehoo-client': 'py3-v3-data-analysis'
        }
        url = "http://go-queueservice/services/v3/queue/push"#"http://test-wine.wineyun.com/queueservice/services/v3/queue/push" 
        data = json.dumps({
                "title": f"拍卖数据 - {now.year}/{now.month}/{now.day}{(now - timedelta(hours=1)).hour}:00 - {now.hour}:00",
                "text":  text
            }
        ,ensure_ascii=False)

        print("dingtalk accesstoken:",DingtalkAccessToken.auction_access_token)
        dingtalk_data = base64.b64encode(json.dumps({
                "access_token": DingtalkAccessToken.auction_access_token,#'4399b225-9d5e-4003-90a5-8d8f118f4e96'
                "type":"markdown",
                "at":"",
                "content": base64.b64encode(data.encode('utf-8')).decode('utf-8')
            },ensure_ascii=False).encode("utf-8"))
        
        print("拍卖队列推送返回：",dingtalk_data)
        queue_data = json.dumps({
            "exchange_name":"dingtalk",
            "routing_key":"dingtalk_sender",
            "data":str(dingtalk_data, encoding = "utf-8")
        }, ensure_ascii=False)
        
        print(queue_data)
        res = requests.post(url, headers=headers, data=queue_data)
        # get_plot_robot()
        return res.json()
    except Exception as e:
        print(f"传入企业微信接口失败:{e}")
        return -1

def auction_aster(now,text) -> dict:
    """
    推送消息给企微机器人
    :return: f
    """
    try:
        return push_info_robot_by_queue_service(now,text)
    except Exception as e:
        print(f"推送企微机器人失败:{e}")
        return -1