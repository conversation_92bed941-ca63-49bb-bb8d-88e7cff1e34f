import pymysql
from dbutils.pooled_db import PooledDB
# 获取配置文件
from app.CommonLibraries.getConfig import get_config
from app.DButils.db_config import MysqlConfig


class MysqlPool:
    def __init__(self, database):
        config_info = get_config("db.data.v3_all", "vinehoo.accounts")#"db.v3_all", "vinehoo.accounts"
        # config_info = get_config("db.v3_all", "vinehoo.accounts")
        db_config = {
            'host': config_info['host'],
            'port': config_info['port'],
            'user': config_info['user'],
            'password': config_info['password'],
            # 'host': "rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com",
            # 'port': 3306,
            # 'user': "wy_wh",
            # 'password': "1HGP4o3OpYk9lFfU",
            'database': database,
            'maxconnections': MysqlConfig.DB_MAX_CONNECYIONS,  # 连接池允许的最大连接数
            'mincached': MysqlConfig.DB_MIN_CACHED,  # 初始化时连接池中至少创建的空闲的连接，0表示不创建
            'maxcached': MysqlConfig.DB_MAX_CACHED,  # 连接池中最多闲置的连接，0表示不限制，连接使用完成后的空闲连接保留数。
            'maxshared': MysqlConfig.DB_MAX_SHARED,  # 共享连接数允许的最大数量(缺省值 0 代表所有连接都是专用的)如果达到了最大数量,被请求为共享的连接将会被共享使用
            'blocking': MysqlConfig.DB_BLOCKING,  # 连接池中如果没有可用连接后是否阻塞等待
            'maxusage': MysqlConfig.DB_MAX_USAGE,  # 每个连接最多被重复使用的次数，None表示不限制
            # True 等待，让用户等待，尽可能的成功； False 不等待然后报错，尽快告诉用户错误，例如抢购，不成功就提示。
            'setsession': MysqlConfig.DB_SET_SESSION,  # 一个可选的SQL命令列表用于准备每个会话，如["set datestyle to german", ...]
            'charset': MysqlConfig.DB_CHARSET

        }
        self.__pool = PooledDB(pymysql, **db_config)

    def get_connection(self):
        """
        获取连接
        :return:
        """
        conn = self.__pool.connection()
        conn.autocommit = True
        cursor = conn.cursor()
        return conn, cursor

    @staticmethod
    def dispose(cursor, conn):
        cursor.close()
        conn.close()

class MysqlWrite:
    def __init__(self, database):
        config_info = get_config("db.data.write.v3_all", "vinehoo.accounts")#"db.v3_all", "vinehoo.accounts"
        # config_info = get_config("db.v3_all", "vinehoo.accounts")
        db_config = {
            'host': config_info['host'],
            'port': config_info['port'],
            'user': config_info['user'],
            'password': config_info['password'],
            # 'host': "rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com",
            # 'port': 3306,
            # 'user': "wy_wh",
            # 'password': "1HGP4o3OpYk9lFfU",
            'database': database,
            'maxconnections': MysqlConfig.DB_MAX_CONNECYIONS,  # 连接池允许的最大连接数
            'mincached': MysqlConfig.DB_MIN_CACHED,  # 初始化时连接池中至少创建的空闲的连接，0表示不创建
            'maxcached': MysqlConfig.DB_MAX_CACHED,  # 连接池中最多闲置的连接，0表示不限制，连接使用完成后的空闲连接保留数。
            'maxshared': MysqlConfig.DB_MAX_SHARED,  # 共享连接数允许的最大数量(缺省值 0 代表所有连接都是专用的)如果达到了最大数量,被请求为共享的连接将会被共享使用
            'blocking': MysqlConfig.DB_BLOCKING,  # 连接池中如果没有可用连接后是否阻塞等待
            'maxusage': MysqlConfig.DB_MAX_USAGE,  # 每个连接最多被重复使用的次数，None表示不限制
            # True 等待，让用户等待，尽可能的成功； False 不等待然后报错，尽快告诉用户错误，例如抢购，不成功就提示。
            'setsession': MysqlConfig.DB_SET_SESSION,  # 一个可选的SQL命令列表用于准备每个会话，如["set datestyle to german", ...]
            'charset': MysqlConfig.DB_CHARSET

        }
        self.__pool = PooledDB(pymysql, **db_config)

    def get_connection(self):
        """
        获取连接
        :return:
        """
        conn = self.__pool.connection()
        conn.autocommit = True
        cursor = conn.cursor()
        return conn, cursor

    @staticmethod
    def dispose(cursor, conn):
        cursor.close()
        conn.close()

def conn_str(database,conn_type):
    if conn_type == "channel":
        conn_str = f"""mysql+pymysql://vinehoodev:<EMAIL>/{database}"""
    elif conn_type == "wms":
        conn_str = f"""mysql+pymysql://wy_bqy:HrscHd3*&nbvFFKCF7u!<EMAIL>/{database}"""
    elif conn_type == "test":
        conn_str = f"""mysql+pymysql://vinehoodev:vinehoo123@39.103.135.15/{database}"""
    return conn_str


commodities_pool = MysqlPool('vh_commodities')
orders_pool = MysqlPool('vh_orders')
users_pool = MysqlPool('vh_user')
data_statistic_pool = MysqlWrite('vh_data_statistics')
information_pool = MysqlPool('information_schema')
wiki_pool = MysqlPool('vh_wiki')
auction_pool = MysqlPool('vh_auction')
maidian_pool = MysqlPool('vh_maidian')
marketing_pool = MysqlPool('vh_marketing')
