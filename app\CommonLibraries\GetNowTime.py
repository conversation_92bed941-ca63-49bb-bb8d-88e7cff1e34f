# -*- coding: utf-8 -*-
# 获取当前时间
from pytz import timezone
import datetime
import time

# 设置时区为上海
tz = timezone('Asia/Shanghai')


def get_now_time(time_type):
    """
    获取当前时间
    :param time_type: 时间类型 timestamp-时间戳,date-时间字符串
    :return:
    """
    if time_type == 'timestamp':
        now_time = datetime.datetime.now(tz).timestamp()
    elif time_type == 'date':
        now_time = datetime.datetime.now(tz).strftime("%Y-%m-%d %H:%M:%S")
    return now_time


def time_str_to_timestamp(time_str):
    """
    时间字符串转时间戳
    :param time_str:
    :return:
    """
    time_array = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    timestamp = int(time.mktime(time_array))
    return timestamp


def timestamp_str_to_timestamp(time_str):
    """
    时间字符串转时间戳
    :param time_str:
    :return:
    """
    time_array = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    timestamp = int(time.mktime(time_array))
    return timestamp
