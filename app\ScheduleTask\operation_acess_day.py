import pymysql
import pandas as pd
from datetime import datetime
from dateutil.relativedelta import relativedelta

import warnings

warnings.filterwarnings("ignore")


# 运营达标率


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_last_month():
    # 获取前一个月
    try:
        month_date = datetime.now().date() - relativedelta(months=1)
        return month_date.strftime("%Y-%m")
    except Exception as e:
        print(f"获取前一月失败,失败原因:{e}")


def get_cur_month():
    # 获取当前月
    return datetime.now().strftime("%Y-%m")


def get_cur_date():
    # 获取当前日
    return datetime.now().strftime("%d")


def get_plans(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取计划数据
    :param month:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_data_statistics")
    sql = f"""SELECT operation_duty,flash_redwine_sales_plan + flash_baijiu_sales_plan + cross_border_plan + second_delivery_plan + tail_sales_plan 'sales_plan',main_single_plan 'single_plan',`date` FROM vh_sales_analysis_plan WHERE date LIKE '{month}%'"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df


def get_order_info(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取订单数据
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    req_tables = ["vh_flash_order", "vh_cross_order", "vh_tail_order", "vh_second_order"]
    req_columns = ["date", "sales_money", "sales_order_count"]
    dff = pd.DataFrame(columns=req_columns)
    for table in req_tables:
        sql = f"""SELECT FROM_UNIXTIME( created_time, '%Y-%m-%d') 'date', SUM( payment_amount ) 'sales_money',COUNT(1) 'sales_order_count' FROM {table} WHERE sub_order_status IN( 1, 2, 3 ) AND FROM_UNIXTIME( created_time, '%Y-%m' ) = '{month}' GROUP BY FROM_UNIXTIME( created_time, '%Y-%m-%d')"""
        df = pd.read_sql(sql, conn)
        dff = dff.append(df)
    conn.close()
    return dff


def deal_with_data(rr_host, rr_port, rr_user, rr_password):
    """
    数据处理sales_plan
    :return:
    """
    if datetime.now().strftime('%d') == '01':
        month = get_last_month()
    else:
        month = get_cur_month()
    df_plan = get_plans(month, rr_host, rr_port, rr_user, rr_password)
    df_actual = get_order_info(month, rr_host, rr_port, rr_user, rr_password).reset_index(drop=True)
    df_actual = df_actual.groupby('date').sum().reset_index()
    dff = pd.merge(df_actual, df_plan)
    dff = dff.rename(
        columns={'sales_money': 'actual_sales', 'sales_order_count': 'single_quantity_actual', 'operation_duty': 'name',
                 'sales_plan': 'plan_sales', 'single_plan': 'single_quantity_plan'})
    dff1 = dff[['name', 'actual_sales']].groupby('name').sum().reset_index()
    dff2 = dff[['name', 'single_quantity_actual']].groupby('name').sum().reset_index()
    dff3 = dff[['name', 'plan_sales']].groupby('name').sum().reset_index()
    dff4 = dff[['name', 'single_quantity_plan']].groupby('name').sum().reset_index()
    dff = pd.merge(dff1, dff2).merge(dff3).merge(dff4).reset_index(drop=True)
    dff['single_quantity_proportion'] = round(dff['single_quantity_actual'] / dff['single_quantity_plan'], 2) * 100
    dff['single_quantity_standard'] = round(dff['single_quantity_actual'] / dff['single_quantity_plan'], 2) * 100
    dff['single_quantity_total_standard'] = round(dff['single_quantity_actual'] / dff['single_quantity_plan'].sum(),
                                                  2) * 100
    dff['sales_success_rate'] = round(dff['actual_sales'] / dff['plan_sales'], 2) * 100
    dff['date'] = month
    return dff


def exits_data(month, operation_duty, cursor):
    """
    验证是否已存在
    :param month: 月份
    :param operation_duty: 运营人员
    :param cursor
    :return:
    """
    sql = f"""select * from vh_sales_analysis_target where `date` = '{month}' and `name`= '{operation_duty}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_sales_analysis_target where `date` = '{month}' and `name`= '{operation_duty}'"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(*args):
    conn = rm_conn(args[10], args[11], args[12], args[13], 'vh_data_statistics')
    cursor = conn.cursor()
    sql = f"""insert into vh_sales_analysis_target 
             (`date`,`actual_sales`,`single_quantity_actual`,`name`,`plan_sales`,`single_quantity_plan`,
             `single_quantity_proportion`,`single_quantity_standard`,`single_quantity_total_standard`,
             `sales_success_rate`) values('{args[0]}',{args[1]},{args[2]},'{args[3]}',{args[4]},
             {args[5]},{args[6]},{args[7]},{args[8]},{args[9]})"""
    try:
        exits_data(month=args[0], operation_duty=args[3], cursor=cursor)
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
    finally:
        conn.close()


def handler_operation_access(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        dff = deal_with_data(rr_host, rr_port, rr_user, rr_password)
        dff = dff[['date', 'actual_sales', 'single_quantity_actual', 'name', 'plan_sales', 'single_quantity_plan',
                   'single_quantity_proportion', 'single_quantity_standard', 'single_quantity_total_standard',
                   'sales_success_rate']]
        data_list = dff.values.tolist()
        for data in data_list:
            insert_mysql(data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8], data[9],
                         rm_host, rm_port, rm_user, rm_password)
        return 1
    except Exception as e:
        print(e)
        return -1
