import json
import warnings
import concurrent
import numpy as np
import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime
from app.DButils.MysqlHelper import conn_str
from app.CommonLibraries.GetNowTime import get_now_time
from app.CommonLibraries.GetMonthDate import get_date_first_last,get_month_dates,get_month_dates_to_today,get_year_months

warnings.filterwarnings("ignore")

def nopool_conn(database:str) -> create_engine:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine

def get_all_year_month(time_dimension) -> list:
    year_month_list = [f"{time_dimension}-01",f"{time_dimension}-02",f"{time_dimension}-03"
                       ,f"{time_dimension}-04",f"{time_dimension}-05",f"{time_dimension}-06"
                       ,f"{time_dimension}-07",f"{time_dimension}-08",f"{time_dimension}-09"
                       ,f"{time_dimension}-10",f"{time_dimension}-11",f"{time_dimension}-12"]
    return year_month_list

def get_evaluation_list() -> list:
    evaluation_list = ["vh_posts","vh_wine_evaluation","vh_wine_evaluation_comment","`vh_short_video`.`vh_video_comment`"]
    return evaluation_list

def get_depatment() -> dict:
    depatment_sql = f""" select dept_id,department from vh_department_list """
    depatment_conn = nopool_conn("vh_data_statistics").connect()
    depatment_dff = pd.read_sql_query(depatment_sql,depatment_conn)
    depatment_conn.close()
    nopool_conn("vh_data_statistics").dispose()
    res_json = {"list":json.loads(depatment_dff.to_json(orient="records"))}
    return res_json

def get_vest_comment(realname,dept_id) -> dict:
    if realname == "":
        if dept_id == "":
            vest_comment_sql = f""" select admin_id,realname,department,is_open from vh_vest_comment_config """
        else:
            vest_comment_sql = f""" select admin_id,realname,department,is_open from vh_vest_comment_config where dept_id = {dept_id}"""
    else:
        if dept_id == "":
            vest_comment_sql = f""" select admin_id,realname,department,is_open from vh_vest_comment_config where realname like '%%{realname}%%' """
        else:
            vest_comment_sql = f""" select admin_id,realname,department,is_open from vh_vest_comment_config where dept_id = {dept_id} and realname like '%%{realname}%%' """
    vest_comment_conn = nopool_conn("vh_data_statistics").connect()
    vest_comment_dff = pd.read_sql_query(vest_comment_sql,vest_comment_conn)
    vest_comment_conn.close()
    nopool_conn("vh_data_statistics").dispose()
    res_json = {"list":json.loads(vest_comment_dff.to_json(orient="records"))}
    return res_json

def get_vest_dff() -> pd.DataFrame:
    vest_sql = f""" select a.uid,a.admin_name from vh_vest_user AS a left join vh_data_statistics.vh_vest_comment_config AS aa on a.admin_id = aa.admin_id where a.is_del = 0 and aa.is_open = 1 """
    vest_conn = nopool_conn("vh_user").connect()
    vest_dff = pd.read_sql_query(vest_sql,vest_conn)
    vest_conn.close()
    nopool_conn("vh_user").dispose()
    return vest_dff

def get_remarks_dff(dates) -> pd.DataFrame:
    remarks_sql = f""" select date,concat(operation_duty,'+',operation_duty_assist) as 'operation',remarks from vh_sales_analysis_plan where date in {dates}"""
    remarks_conn = nopool_conn("vh_data_statistics").connect()
    remarks_dff = pd.read_sql_query(remarks_sql,remarks_conn)
    remarks_conn.close()
    nopool_conn("vh_data_statistics").dispose()
    return remarks_dff

def get_periods_evaluation_dff(start_timestamp,end_timestamp,date_type,vest_uids) -> pd.DataFrame:
    if date_type == 1:
        periods_comment_sql = f""" select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid,count(id) as 'evaluation_counts' from vh_periods_comment where created_time between {start_timestamp} and {end_timestamp} and uid in {vest_uids} and audit_status = 1 group by `uid`,`date` """
    elif date_type == 2:
        periods_comment_sql = f""" select from_unixtime(created_time,'%%Y-%%m') as 'month',uid,count(id) as 'evaluation_counts' from vh_periods_comment where created_time between {start_timestamp} and {end_timestamp} and uid in {vest_uids} and audit_status = 1 group by `uid`,`month` """
    periods_comment_conn = nopool_conn("vh_commodities").connect()
    periods_comment_dff = pd.read_sql_query(periods_comment_sql,periods_comment_conn)
    periods_comment_conn.close()
    nopool_conn("vh_commodities").dispose()
    return periods_comment_dff

def get_article_comment_dff(start_timestamp,end_timestamp,date_type,vest_uids) -> pd.DataFrame:
    if date_type == 1:
        periods_comment_sql = f""" select from_unixtime(addtime,'%%Y-%%m-%%d') as 'date',uid,count(comid) as 'evaluation_counts' from vh_article_comment where addtime between {start_timestamp} and {end_timestamp} and uid in {vest_uids} and audit_status = 2 group by `uid`,`date` """
    elif date_type == 2:
        periods_comment_sql = f""" select from_unixtime(addtime,'%%Y-%%m') as 'month',uid,count(comid) as 'evaluation_counts' from vh_article_comment where addtime between {start_timestamp} and {end_timestamp} and uid in {vest_uids} and audit_status = 2 group by `uid`,`month` """
    periods_comment_conn = nopool_conn("vh_news").connect()
    periods_comment_dff = pd.read_sql_query(periods_comment_sql,periods_comment_conn)
    periods_comment_conn.close()
    nopool_conn("vh_news").dispose()
    return periods_comment_dff

def get_other_evaluation_dff(start_timestamp,end_timestamp,evaluation_table,date_type,vest_uids) -> pd.DataFrame:
    if date_type == 1:
        evaluation_sql = f""" select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid,count(id) as 'evaluation_counts' from {evaluation_table} where created_time between {start_timestamp} and {end_timestamp} and uid in {vest_uids} and status = 1 group by `uid`,`date` """
    elif date_type == 2:
        evaluation_sql = f""" select from_unixtime(created_time,'%%Y-%%m') as 'month',uid,count(id) as 'evaluation_counts' from {evaluation_table} where created_time between {start_timestamp} and {end_timestamp} and uid in {vest_uids} and status = 1 group by `uid`,`month`"""
    evaluation_conn = nopool_conn("vh_community").connect()
    evaluation_dff = pd.read_sql_query(evaluation_sql,evaluation_conn)
    evaluation_conn.close()
    nopool_conn("vh_community").dispose()
    return evaluation_dff

def get_evaluation_main_dff(date_type,time_dimension,evaluation_type) -> dict:
    vest_dff = get_vest_dff()
    if vest_dff.empty:
        return {"data":[]}
    if date_type == "month":
        if datetime(int(get_now_time("date").split(" ")[0].split("-")[0]),int(get_now_time("date").split(" ")[0].split("-")[1]),1) == datetime(int(time_dimension.split(" ")[0].split("-")[0]),int(time_dimension.split(" ")[0].split("-")[1]),1):
            if int(get_now_time("date").split(" ")[0].split("-")[2]) < int(get_date_first_last(time_dimension)[1].split("-")[2]):
                month_dates = get_month_dates_to_today(int(time_dimension.split("-")[0]),int(time_dimension.split("-")[1]))
            elif int(get_now_time("date").split(" ")[0].split("-")[2]) == int(get_date_first_last(time_dimension)[1].split("-")[2]):
                month_dates = get_month_dates(int(time_dimension.split("-")[0]),int(time_dimension.split("-")[1]))
            else:
                return {"data":[]}
        elif datetime(int(get_now_time("date").split(" ")[0].split("-")[0]),int(get_now_time("date").split(" ")[0].split("-")[1]),1) > datetime(int(time_dimension.split(" ")[0].split("-")[0]),int(time_dimension.split(" ")[0].split("-")[1]),1):
            month_dates = get_month_dates(int(time_dimension.split("-")[0]),int(time_dimension.split("-")[1]))
        else:
            return {"data":[]}
        date_dff = pd.DataFrame({"date":month_dates})
        start_date,end_date = get_date_first_last(time_dimension)
        start_timestamp,end_timestamp = int(datetime.strptime(f"{start_date} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"{end_date} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
        if evaluation_type == 0:
            periods_evaluation_dff = get_periods_evaluation_dff(start_timestamp,end_timestamp,1,tuple(set(vest_dff.uid.tolist())))
            article_comment_dff = get_article_comment_dff(start_timestamp,end_timestamp,1,tuple(set(vest_dff.uid.tolist())))
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(get_evaluation_list()) * 2) as executor:
                evaluation_tasks = [executor.submit(get_other_evaluation_dff,start_timestamp,end_timestamp,evaluation_table,1,tuple(set(vest_dff.uid.tolist()))) for evaluation_table in get_evaluation_list()]
                evaluation_dff = pd.DataFrame(columns=["date","uid","evaluation_counts"])
                for evaluation_future in concurrent.futures.as_completed(evaluation_tasks):
                    evaluation_dff=evaluation_dff.append(evaluation_future.result()).reset_index(drop=True)
            evaluation_main_dff = (
                article_comment_dff
                .append([periods_evaluation_dff, evaluation_dff])
                .reset_index(drop=True)
            )
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            remarks_dff = get_remarks_dff(tuple(set(date_dff.date.tolist())))
            date_dff = pd.merge(date_dff,remarks_dff,how="left",on="date")
            main_dff = main_dff[["date","admin_name","evaluation_counts"]].groupby(by=["date","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='date', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(date_dff,main_dff,how="left",on="date")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            main_dff['date'] = main_dff.date.apply(lambda x:x.split("-")[1] + "-" + x.split("-")[2])
            sum_values_list = list(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total", "",""]
            mean_values_list[:0] = ["Mean", "",""]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json
        elif evaluation_type == 1:
            periods_evaluation_dff = get_periods_evaluation_dff(start_timestamp,end_timestamp,1,tuple(set(vest_dff.uid.tolist())))
            evaluation_main_dff = periods_evaluation_dff.copy()
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            remarks_dff = get_remarks_dff(tuple(set(date_dff.date.tolist())))
            date_dff = pd.merge(date_dff,remarks_dff,how="left",on="date")
            main_dff = main_dff[["date","admin_name","evaluation_counts"]].groupby(by=["date","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='date', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(date_dff,main_dff,how="left",on="date")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            main_dff['date'] = main_dff.date.apply(lambda x:x.split("-")[1] + "-" + x.split("-")[2])
            sum_values_list = list(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total", "",""]
            mean_values_list[:0] = ["Mean", "",""]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json
        elif evaluation_type == 2:
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(get_evaluation_list()) * 2) as executor:
                evaluation_tasks = [executor.submit(get_other_evaluation_dff,start_timestamp,end_timestamp,evaluation_table,1,tuple(set(vest_dff.uid.tolist()))) for evaluation_table in get_evaluation_list()]
                evaluation_dff = pd.DataFrame(columns=["date","uid","evaluation_counts"])
                for evaluation_future in concurrent.futures.as_completed(evaluation_tasks):
                    evaluation_dff=evaluation_dff.append(evaluation_future.result()).reset_index(drop=True)
            evaluation_main_dff = evaluation_dff.copy()
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            remarks_dff = get_remarks_dff(tuple(set(date_dff.date.tolist())))
            date_dff = pd.merge(date_dff,remarks_dff,how="left",on="date")
            main_dff = main_dff[["date","admin_name","evaluation_counts"]].groupby(by=["date","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='date', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(date_dff,main_dff,how="left",on="date")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            main_dff['date'] = main_dff.date.apply(lambda x:x.split("-")[1] + "-" + x.split("-")[2])
            sum_values_list = list(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total", "",""]
            mean_values_list[:0] = ["Mean", "",""]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json
        elif evaluation_type == 3:
            article_comment_dff = get_article_comment_dff(start_timestamp,end_timestamp,1,tuple(set(vest_dff.uid.tolist())))
            evaluation_main_dff = article_comment_dff.copy()
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            remarks_dff = get_remarks_dff(tuple(set(date_dff.date.tolist())))
            date_dff = pd.merge(date_dff,remarks_dff,how="left",on="date")
            main_dff = main_dff[["date","admin_name","evaluation_counts"]].groupby(by=["date","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='date', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(date_dff,main_dff,how="left",on="date")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            main_dff['date'] = main_dff.date.apply(lambda x:x.split("-")[1] + "-" + x.split("-")[2])
            sum_values_list = list(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["date","operation","remarks"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total", "",""]
            mean_values_list[:0] = ["Mean", "",""]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json
    elif date_type == "year":
        to_year = int(get_now_time("date").split(" ")[0].split("-")[0])
        to_month = int(get_now_time("date").split(" ")[0].split("-")[1])
        if to_year == int(time_dimension):
            year_months = get_year_months(to_year,to_month)
        else:
            year_months = get_all_year_month(time_dimension)
        month_dff = pd.DataFrame({"month":year_months})
        start_timestamp,end_timestamp = int(datetime.strptime(f"{time_dimension}-01-01 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"{time_dimension}-12-31 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
        if evaluation_type == 0:
            periods_evaluation_dff = get_periods_evaluation_dff(start_timestamp,end_timestamp,2,tuple(set(vest_dff.uid.tolist())))
            article_comment_dff = get_article_comment_dff(start_timestamp,end_timestamp,2,tuple(set(vest_dff.uid.tolist())))
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(get_evaluation_list()) * 2) as executor:
                evaluation_tasks = [executor.submit(get_other_evaluation_dff,start_timestamp,end_timestamp,evaluation_table,2,tuple(set(vest_dff.uid.tolist()))) for evaluation_table in get_evaluation_list()]
                evaluation_dff = pd.DataFrame(columns=["month","uid","evaluation_counts"])
                for evaluation_future in concurrent.futures.as_completed(evaluation_tasks):
                    evaluation_dff=evaluation_dff.append(evaluation_future.result()).reset_index(drop=True)
            evaluation_main_dff = (
                article_comment_dff
                .append([periods_evaluation_dff, evaluation_dff])
                .reset_index(drop=True)
            )
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            main_dff = main_dff[["month","admin_name","evaluation_counts"]].groupby(by=["month","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='month', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(month_dff,main_dff,how="left",on="month")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            sum_values_list = list(main_dff[list(main_dff.drop(["month"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["month"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total"]
            mean_values_list[:0] = ["Mean"]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json
        elif evaluation_type == 1:
            periods_evaluation_dff = get_periods_evaluation_dff(start_timestamp,end_timestamp,2,tuple(set(vest_dff.uid.tolist())))
            evaluation_main_dff = periods_evaluation_dff.copy()
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            main_dff = main_dff[["month","admin_name","evaluation_counts"]].groupby(by=["month","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='month', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(month_dff,main_dff,how="left",on="month")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            sum_values_list = list(main_dff[list(main_dff.drop(["month"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["month"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total"]
            mean_values_list[:0] = ["Mean"]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json
        elif evaluation_type == 2:
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(get_evaluation_list()) * 2) as executor:
                evaluation_tasks = [executor.submit(get_other_evaluation_dff,start_timestamp,end_timestamp,evaluation_table,2,tuple(set(vest_dff.uid.tolist()))) for evaluation_table in get_evaluation_list()]
                evaluation_dff = pd.DataFrame(columns=["date","uid","evaluation_counts"])
                for evaluation_future in concurrent.futures.as_completed(evaluation_tasks):
                    evaluation_dff=evaluation_dff.append(evaluation_future.result()).reset_index(drop=True)
            evaluation_main_dff = evaluation_dff.copy()
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            main_dff = main_dff[["month","admin_name","evaluation_counts"]].groupby(by=["month","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='month', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(month_dff,main_dff,how="left",on="month")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            sum_values_list = list(main_dff[list(main_dff.drop(["month"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["month"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total"]
            mean_values_list[:0] = ["Mean"]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json
        elif evaluation_type == 3:
            periods_evaluation_dff = get_article_comment_dff(start_timestamp,end_timestamp,2,tuple(set(vest_dff.uid.tolist())))
            evaluation_main_dff = periods_evaluation_dff.copy()
            main_dff = pd.merge(vest_dff,evaluation_main_dff,how="right",on="uid")
            main_dff = main_dff[["month","admin_name","evaluation_counts"]].groupby(by=["month","admin_name"],as_index=False).agg({"evaluation_counts":sum})
            main_dff = main_dff.pivot(index='month', columns='admin_name', values='evaluation_counts').reset_index()
            main_dff = pd.merge(month_dff,main_dff,how="left",on="month")
            main_dff.columns.name = None
            main_dff = main_dff.fillna(0)
            sum_values_list = list(main_dff[list(main_dff.drop(["month"],axis=1).columns)].sum().values)
            mean_values_list = list(np.round(main_dff[list(main_dff.drop(["month"],axis=1).columns)].mean().values,2))
            sum_values_list[:0] = ["Total"]
            mean_values_list[:0] = ["Mean"]
            main_dff.loc[len(main_dff)] = sum_values_list
            main_dff.loc[len(main_dff)] = mean_values_list
            res_json = {"data": json.loads(main_dff.to_json(orient="records"))}
            return res_json