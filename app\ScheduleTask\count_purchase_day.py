import pymysql
import pandas as pd
from datetime import datetime
from dateutil.relativedelta import relativedelta
import json
import numpy as np
import warnings

warnings.filterwarnings("ignore")


# 采购统计

def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_last_month():
    # 获取前一个月
    try:
        month_date = datetime.now().date() - relativedelta(months=1)
        return month_date.strftime("%Y-%m")
    except Exception as e:
        print(f"获取前一月失败,失败原因:{e}")


def get_cur_month():
    # 获取当前月
    return datetime.now().strftime("%Y-%m")


def get_cur_date():
    # 获取当前日
    return datetime.now().strftime("%d")


def get_plans(month, rr_host, rr_port, rr_user, rr_password):
    """
    获取计划数据
    :param month:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_data_statistics")
    sql = f"""SELECT `date`,buyer_id,buyer,sale_target,import_type,order_type FROM `vh_purchase_sales_target` WHERE date = '{month}'"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df


def deal_list_wine_info(wine_info):
    data_list = []
    for i in json.loads(wine_info):
        data_list.append(i['product_id'])
    return data_list


def deal_list_nums(nums):
    data_list = []
    for i in json.loads(nums):
        data_list.append(i['nums'])
    return data_list


def get_periods_info(month, buyer_id, rr_host, rr_port, rr_user, rr_password):
    """
    获取采购人采购产品
    :param month:
    :param buyer_id:
    :return:
    """
    req_tables = ["vh_periods_flash", "vh_periods_cross", "vh_periods_second", "vh_periods_leftover"]
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_commodities")
    dff = pd.DataFrame(columns=['period', 'import_type'])
    for table in req_tables:
        sql = f"""SELECT id 'period',buyer_id,import_type FROM `{table}` WHERE FROM_UNIXTIME(onsale_time,'%Y-%m') = '{month}' AND buyer_id in {buyer_id}"""
        df = pd.read_sql(sql, conn)
        if table == 'vh_periods_flash':
            df['period_type'] = 0
        if table == 'vh_periods_cross':
            df['period_type'] = 2
        if table == 'vh_periods_second':
            df['period_type'] = 1
        if table == 'vh_periods_leftover':
            df['period_type'] = 3
        dff = dff.append(df)
    dff = dff.reset_index(drop=True)
    conn.close()
    return dff


def get_sales_info(month, periods, rr_host, rr_port, rr_user, rr_password):
    """
    获取销售信息
    :param month:
    :param periods:
    :return:
    """
    req_tables = ["vh_flash_order", "vh_cross_order", "vh_second_order", "vh_tail_order"]
    dff = pd.DataFrame(columns=['sub_order_no', 'payment_amount'])
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    for table in req_tables:
        if len(periods) == 1:
            sql = f"""SELECT sub_order_no,period,payment_amount
                              FROM `{table}` 
                              WHERE sub_order_status in (1,2,3) 
                              AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' 
                              AND period = {periods}"""
        else:
            sql = f"""SELECT sub_order_no,period,payment_amount 
                      FROM `{table}` 
                      WHERE sub_order_status in (1,2,3) 
                      AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}' 
                      AND period IN {periods}"""
        df = pd.read_sql(sql, conn)
        if table == 'vh_flash_order':
            df['order_type'] = 0
        if table == 'vh_cross_order':
            df['order_type'] = 2
        if table == 'vh_second_order':
            df['order_type'] = 1
        if table == 'vh_tail_order':
            df['order_type'] = 3
        dff = dff.append(df)
    dff = dff.reset_index(drop=True)
    return dff


def exits_data(purchaser, statistical_month, product_type, order_type, cursor):
    """
    验证是否已存在
    :return:
    """
    sql = f"""select * from vh_procurement_statistics 
              where `purchaser` = '{purchaser}' 
              and `statistical_month`= '{statistical_month}'
              and `product_type`= {product_type}
              and `order_type`= {order_type}"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_procurement_statistics 
                     where `purchaser` = '{purchaser}' 
                     and `statistical_month`= '{statistical_month}'
                     and `product_type`= {product_type}
                     and `order_type`= {order_type}"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(insert_json, rm_host, rm_port, rm_user, rm_password):
    conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
    cursor = conn.cursor()
    dict_keys = list(insert_json.keys())
    exits_data(insert_json[dict_keys[0]], insert_json[dict_keys[10]], insert_json[dict_keys[9]],
               insert_json[dict_keys[11]], cursor)
    sql = f"""insert into vh_procurement_statistics 
             (`purchaser`, `sales_targets`, `sales`, `compliance_rate`, `gross_margin`,`gross_margin_num`,`shortfall`, `order_nums`,
             `bottle_nums`, `product_type`, `statistical_month`, `order_type`) 
             values('{insert_json[dict_keys[0]]}',{insert_json[dict_keys[1]]},{insert_json[dict_keys[2]]},
             {insert_json[dict_keys[3]]},{insert_json[dict_keys[4]]},{insert_json[dict_keys[5]]},
             {insert_json[dict_keys[6]]},{insert_json[dict_keys[7]]},{insert_json[dict_keys[8]]},
             {insert_json[dict_keys[9]]},'{insert_json[dict_keys[10]]}',{insert_json[dict_keys[11]]})"""
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
    finally:
        conn.close()


def get_cost(order_type, buyer_id, import_type, month, rr_host, rr_port, rr_user, rr_password):
    """
    获取成本信息
    :return:
    """
    conn_commodities = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_commodities")
    conn_orders = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    if order_type == 0 or order_type == 3:
        sql_commodities_flash = f"""SELECT id FROM vh_periods_flash WHERE buyer_id = {buyer_id} AND import_type = {import_type}"""
        df_flash = pd.read_sql(sql_commodities_flash, conn_commodities)
        sql_commodities_tail = f"""SELECT id FROM vh_periods_leftover WHERE buyer_id = {buyer_id} AND import_type = {import_type}"""
        df_tail = pd.read_sql(sql_commodities_tail, conn_commodities)
        if df_flash.empty and df_tail.empty:
            return 0, 0, 0
        elif df_flash.empty is True and df_tail.empty is False:
            periods_tail = tuple(df_tail['id'].tolist())
            if len(periods_tail) == 1:
                sql_orders_tail = f"""SELECT sub_order_no,package_id,order_qty
                                                                       FROM vh_tail_order 
                                                                       WHERE period = {periods_tail[0]} 
                                                                       AND sub_order_status IN (1,2,3) 
                                                                       AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_tail = f"""SELECT sub_order_no,package_id,order_qty
                                                           FROM vh_tail_order 
                                                           WHERE period IN {periods_tail} 
                                                           AND sub_order_status IN (1,2,3) 
                                                           AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            df_order_tail = pd.read_sql(sql_orders_tail, conn_orders)
            dff_order = df_order_tail
            total_orders = len(dff_order)
            tail_package_ids = tuple(df_order_tail['package_id'].tolist())
            if len(tail_package_ids) == 1:
                sql_package_tail = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_leftover_set WHERE id = {tail_package_ids[0]}"""
            else:
                sql_package_tail = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_leftover_set WHERE id in {tail_package_ids}"""
            df_set = pd.read_sql(sql_package_tail, conn_commodities)
            # 获取期数绑定产品id
            df_set['product_id'] = df_set['associated_products'].apply(lambda x: deal_list_wine_info(x))
            df_product_id = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.product_id.str.len()),
                                          'product_id': np.concatenate(df_set.product_id.values)}).reset_index(
                drop=True)
            # 获取期数绑定产品数量
            df_set['nums'] = df_set['associated_products'].apply(lambda x: deal_list_nums(x))
            df_nums = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.nums.str.len()),
                                    'nums': np.concatenate(df_set.nums.values)}).reset_index(drop=True)
            df_nums = df_nums[['nums']]
            dff_package_info = pd.concat([df_product_id, df_nums], axis=1)
            # 获取产品成本
            sql = """SELECT product_id,costprice FROM vh_periods_product_inventory"""
            df_cost = pd.read_sql(sql, conn_commodities)
            df_cost = df_cost.drop_duplicates(subset='product_id', keep='first').reset_index(drop=True)
            dff_cost = pd.merge(dff_package_info, df_cost, how='left')
            dff_cost['costprice'] = dff_cost['costprice'] * dff_cost['nums']
            dff_cost = dff_cost.drop_duplicates(subset='package_id', keep='first')
            dff_cost['channel_type'] = 0
            dff_end = pd.merge(dff_order, dff_cost, how='left')
            dff_end['cost'] = dff_end['costprice'] * dff_end['order_qty']
            dff_end['bottle_nums'] = dff_end['nums'] * dff_end['order_qty']
            total_cost = dff_end['cost'].sum()
            total_nums = dff_end['bottle_nums'].sum()
            return total_cost, total_nums, total_orders
        elif df_flash.empty is False and df_tail.empty is True:
            periods_flash = tuple(df_flash['id'].tolist())
            if len(periods_flash) == 1:
                sql_orders_flash = f"""SELECT sub_order_no,package_id,order_qty
                                                               FROM vh_flash_order 
                                                               WHERE period = {periods_flash[0]} 
                                                               AND sub_order_status IN (1,2,3) 
                                                               AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_flash = f"""SELECT sub_order_no,package_id,order_qty
                                                   FROM vh_flash_order 
                                                   WHERE period IN {periods_flash} 
                                                   AND sub_order_status IN (1,2,3) 
                                                   AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            df_order_flash = pd.read_sql(sql_orders_flash, conn_orders)
            dff_order = df_order_flash
            total_orders = len(dff_order)
            flash_package_ids = tuple(df_order_flash['package_id'].tolist())
            if len(flash_package_ids) == 1:
                sql_package_flash = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id = {flash_package_ids[0]}"""
            else:
                sql_package_flash = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id in {flash_package_ids}"""
            df_set = pd.read_sql(sql_package_flash, conn_commodities)
            # 获取期数绑定产品id
            df_set['product_id'] = df_set['associated_products'].apply(lambda x: deal_list_wine_info(x))
            df_product_id = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.product_id.str.len()),
                                          'product_id': np.concatenate(df_set.product_id.values)}).reset_index(
                drop=True)
            # 获取期数绑定产品数量
            df_set['nums'] = df_set['associated_products'].apply(lambda x: deal_list_nums(x))
            df_nums = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.nums.str.len()),
                                    'nums': np.concatenate(df_set.nums.values)}).reset_index(drop=True)
            df_nums = df_nums[['nums']]
            dff_package_info = pd.concat([df_product_id, df_nums], axis=1)
            # 获取产品成本
            sql = """SELECT product_id,costprice FROM vh_periods_product_inventory"""
            df_cost = pd.read_sql(sql, conn_commodities)
            df_cost = df_cost.drop_duplicates(subset='product_id', keep='first').reset_index(drop=True)
            dff_cost = pd.merge(dff_package_info, df_cost, how='left')
            dff_cost['costprice'] = dff_cost['costprice'] * dff_cost['nums']
            dff_cost = dff_cost.drop_duplicates(subset='package_id', keep='first')
            dff_cost['channel_type'] = 0
            dff_end = pd.merge(dff_order, dff_cost, how='left')
            dff_end['cost'] = dff_end['costprice'] * dff_end['order_qty']
            dff_end['bottle_nums'] = dff_end['nums'] * dff_end['order_qty']
            total_cost = dff_end['cost'].sum()
            total_nums = dff_end['bottle_nums'].sum()
            return total_cost, total_nums, total_orders
        else:
            periods_flash = tuple(df_flash['id'].tolist())
            if len(periods_flash) == 1:
                sql_orders_flash = f"""SELECT sub_order_no,package_id,order_qty
                                                       FROM vh_flash_order 
                                                       WHERE period = {periods_flash[0]} 
                                                       AND sub_order_status IN (1,2,3) 
                                                       AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_flash = f"""SELECT sub_order_no,package_id,order_qty
                                                       FROM vh_flash_order 
                                                       WHERE period IN {periods_flash} 
                                                       AND sub_order_status IN (1,2,3) 
                                                       AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            periods_tail = tuple(df_tail['id'].tolist())
            if len(periods_tail) == 1:
                sql_orders_tail = f"""SELECT sub_order_no,package_id,order_qty
                                                               FROM vh_tail_order 
                                                               WHERE period = {periods_tail[0]} 
                                                               AND sub_order_status IN (1,2,3) 
                                                               AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_tail = f"""SELECT sub_order_no,package_id,order_qty
                                                   FROM vh_tail_order 
                                                   WHERE period IN {periods_tail} 
                                                   AND sub_order_status IN (1,2,3) 
                                                   AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            df_order_flash = pd.read_sql(sql_orders_flash, conn_orders)
            df_order_tail = pd.read_sql(sql_orders_tail, conn_orders)
            if df_order_tail.empty:
                dff_order = df_order_flash
                tail_package_ids = (0,)
            else:
                dff_order = df_order_flash.append(df_order_tail).reset_index(drop=True)
                tail_package_ids = tuple(df_order_tail['package_id'].tolist())
            total_orders = len(dff_order)
            flash_package_ids = tuple(df_order_flash['package_id'].tolist())
            if len(flash_package_ids) == 1:
                sql_package_flash = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id = {flash_package_ids[0]}"""
            else:
                sql_package_flash = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_flash_set WHERE id in {flash_package_ids}"""
            if len(tail_package_ids) == 1:
                sql_package_tail = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_leftover_set WHERE id = {tail_package_ids[0]}"""
            else:
                sql_package_tail = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_leftover_set WHERE id in {tail_package_ids}"""
            df_set = pd.read_sql(sql_package_flash, conn_commodities).append(
                pd.read_sql(sql_package_tail, conn_commodities)).reset_index(drop=True)
            # 获取期数绑定产品id
            df_set['product_id'] = df_set['associated_products'].apply(lambda x: deal_list_wine_info(x))
            df_product_id = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.product_id.str.len()),
                                          'product_id': np.concatenate(df_set.product_id.values)}).reset_index(
                drop=True)
            # 获取期数绑定产品数量
            df_set['nums'] = df_set['associated_products'].apply(lambda x: deal_list_nums(x))
            df_nums = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.nums.str.len()),
                                    'nums': np.concatenate(df_set.nums.values)}).reset_index(drop=True)
            df_nums = df_nums[['nums']]
            dff_package_info = pd.concat([df_product_id, df_nums], axis=1)
            # 获取产品成本
            sql = """SELECT product_id,costprice FROM vh_periods_product_inventory"""
            df_cost = pd.read_sql(sql, conn_commodities)
            df_cost = df_cost.drop_duplicates(subset='product_id', keep='first').reset_index(drop=True)
            dff_cost = pd.merge(dff_package_info, df_cost, how='left')
            dff_cost['costprice'] = dff_cost['costprice'] * dff_cost['nums']
            dff_cost = dff_cost.drop_duplicates(subset='package_id', keep='first')
            dff_cost['channel_type'] = 0
            dff_end = pd.merge(dff_order, dff_cost, how='left')
            dff_end['cost'] = dff_end['costprice'] * dff_end['order_qty']
            dff_end['bottle_nums'] = dff_end['nums'] * dff_end['order_qty']
            total_cost = dff_end['cost'].sum()
            total_nums = dff_end['bottle_nums'].sum()
            return total_cost, total_nums, total_orders
    elif order_type == 2:
        sql_commodities_cross = f"""SELECT id FROM vh_periods_cross WHERE buyer_id = {buyer_id}"""
        df_cross = pd.read_sql(sql_commodities_cross, conn_commodities)
        if df_cross.empty:
            return 0, 0, 0
        periods_cross = tuple(df_cross['id'].tolist())
        if len(periods_cross) == 1:
            sql_orders_cross = f"""SELECT sub_order_no,package_id,order_qty
                                                       FROM vh_cross_order 
                                                       WHERE period = {periods_cross[0]} 
                                                       AND sub_order_status IN (1,2,3) 
                                                       AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        else:
            sql_orders_cross = f"""SELECT sub_order_no,package_id,order_qty
                                                       FROM vh_cross_order 
                                                       WHERE period IN {periods_cross} 
                                                       AND sub_order_status IN (1,2,3) 
                                                       AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        df_order_cross = pd.read_sql(sql_orders_cross, conn_orders)
        dff_order = df_order_cross
        total_orders = len(dff_order)
        cross_package_ids = tuple(df_order_cross['package_id'].tolist())
        if len(cross_package_ids) == 1:
            sql_package_cross = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_cross_set WHERE id = {cross_package_ids[0]}"""
        else:
            sql_package_cross = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_cross_set WHERE id in {cross_package_ids}"""
        df_set = pd.read_sql(sql_package_cross, conn_commodities)
        # 获取期数绑定产品id
        df_set['product_id'] = df_set['associated_products'].apply(lambda x: deal_list_wine_info(x))
        df_product_id = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.product_id.str.len()),
                                      'product_id': np.concatenate(df_set.product_id.values)}).reset_index(drop=True)
        # 获取期数绑定产品数量
        df_set['nums'] = df_set['associated_products'].apply(lambda x: deal_list_nums(x))
        df_nums = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.nums.str.len()),
                                'nums': np.concatenate(df_set.nums.values)}).reset_index(drop=True)
        df_nums = df_nums[['nums']]
        dff_package_info = pd.concat([df_product_id, df_nums], axis=1)
        # 获取产品成本
        sql = """SELECT product_id,costprice FROM vh_periods_product_inventory"""
        df_cost = pd.read_sql(sql, conn_commodities)
        df_cost = df_cost.drop_duplicates(subset='product_id', keep='first').reset_index(drop=True)
        dff_cost = pd.merge(dff_package_info, df_cost, how='left')
        dff_cost['costprice'] = dff_cost['costprice'] * dff_cost['nums']
        dff_cost = dff_cost.drop_duplicates(subset='package_id', keep='first')
        dff_cost['channel_type'] = 0
        dff_end = pd.merge(dff_order, dff_cost, how='left')
        dff_end['cost'] = dff_end['costprice'] * dff_end['order_qty']
        dff_end['bottle_nums'] = dff_end['nums'] * dff_end['order_qty']
        total_cost = dff_end['cost'].sum()
        total_nums = dff_end['bottle_nums'].sum()
        return total_cost, total_nums, total_orders
    elif order_type == 1:
        sql_commodities_second = f"""SELECT id FROM vh_periods_second WHERE buyer_id = {buyer_id}"""
        df_second = pd.read_sql(sql_commodities_second, conn_commodities)
        if df_second.empty:
            return 0, 0, 0
        periods_second = tuple(df_second['id'].tolist())
        if len(periods_second) == 1:
            sql_orders_second = f"""SELECT sub_order_no,package_id,order_qty
                                                       FROM vh_second_order 
                                                       WHERE period = {periods_second[0]} 
                                                       AND sub_order_status IN (1,2,3) 
                                                       AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        else:
            sql_orders_second = f"""SELECT sub_order_no,package_id,order_qty
                                               FROM vh_second_order 
                                               WHERE period IN {periods_second} 
                                               AND sub_order_status IN (1,2,3) 
                                               AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        df_order_second = pd.read_sql(sql_orders_second, conn_orders)
        dff_order = df_order_second
        total_orders = len(dff_order)
        second_package_ids = tuple(df_order_second['package_id'].tolist())
        if len(second_package_ids) == 1:
            sql_package_second = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_second_set WHERE id = {second_package_ids[0]}"""
        else:
            sql_package_second = f"""SELECT id 'package_id' ,associated_products FROM vh_periods_second_set WHERE id in {second_package_ids}"""
        df_set = pd.read_sql(sql_package_second, conn_commodities)
        # 获取期数绑定产品id
        df_set['product_id'] = df_set['associated_products'].apply(lambda x: deal_list_wine_info(x))
        df_product_id = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.product_id.str.len()),
                                      'product_id': np.concatenate(df_set.product_id.values)}).reset_index(drop=True)
        # 获取期数绑定产品数量
        df_set['nums'] = df_set['associated_products'].apply(lambda x: deal_list_nums(x))
        df_nums = pd.DataFrame({'package_id': df_set.package_id.repeat(df_set.nums.str.len()),
                                'nums': np.concatenate(df_set.nums.values)}).reset_index(drop=True)
        df_nums = df_nums[['nums']]
        dff_package_info = pd.concat([df_product_id, df_nums], axis=1)
        # 获取产品成本
        sql = """SELECT product_id,costprice FROM vh_periods_product_inventory"""
        df_cost = pd.read_sql(sql, conn_commodities)
        df_cost = df_cost.drop_duplicates(subset='product_id', keep='first').reset_index(drop=True)
        dff_cost = pd.merge(dff_package_info, df_cost, how='left')
        dff_cost['costprice'] = dff_cost['costprice'] * dff_cost['nums']
        dff_cost = dff_cost.drop_duplicates(subset='package_id', keep='first')
        dff_cost['channel_type'] = 0
        dff_end = pd.merge(dff_order, dff_cost, how='left')
        dff_end['cost'] = dff_end['costprice'] * dff_end['order_qty']
        dff_end['bottle_nums'] = dff_end['nums'] * dff_end['order_qty']
        total_cost = dff_end['cost'].sum()
        total_nums = dff_end['bottle_nums'].sum()
        return total_cost, total_nums, total_orders
    else:
        return 0, 0, 0


def get_sales(order_type, buyer_id, import_type, month, rr_host, rr_port, rr_user, rr_password):
    """
    获取销售额
    :return:
    """
    conn_commodities = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_commodities")
    conn_orders = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    if order_type == 0 or order_type == 3:
        sql_commodities_flash = f"""SELECT id FROM vh_periods_flash WHERE buyer_id = {buyer_id} AND import_type = {import_type}"""
        df_flash = pd.read_sql(sql_commodities_flash, conn_commodities)
        sql_commodities_tail = f"""SELECT id FROM vh_periods_leftover WHERE buyer_id = {buyer_id} AND import_type = {import_type}"""
        df_tail = pd.read_sql(sql_commodities_tail, conn_commodities)
        if df_flash.empty and df_tail.empty:
            return 0
        elif df_flash.empty is True and df_tail.empty is False:
            periods_tail = tuple(df_tail['id'].tolist())
            if len(periods_tail) == 1:
                sql_orders_tail = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                                   FROM vh_tail_order 
                                                                   WHERE period = {periods_tail[0]} 
                                                                   AND sub_order_status IN (1,2,3) 
                                                                   AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_tail = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                       FROM vh_tail_order 
                                                       WHERE period IN {periods_tail} 
                                                       AND sub_order_status IN (1,2,3) 
                                                       AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            df_order_tail = pd.read_sql(sql_orders_tail, conn_orders)
            tail_sales = df_order_tail['payment_amount'][0]
            return tail_sales
        elif df_flash.empty is False and df_tail.empty is True:
            periods_flash = tuple(df_flash['id'].tolist())
            if len(periods_flash) == 1:
                sql_orders_flash = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                           FROM vh_flash_order 
                                                           WHERE period = {periods_flash[0]} 
                                                           AND sub_order_status IN (1,2,3) 
                                                           AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_flash = f"""SELECT SUM(payment_amount) 'payment_amount'
                                               FROM vh_flash_order 
                                               WHERE period IN {periods_flash} 
                                               AND sub_order_status IN (1,2,3) 
                                               AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            df_order_flash = pd.read_sql(sql_orders_flash, conn_orders)
            flash_sales = df_order_flash['payment_amount'][0]
            return flash_sales
        else:
            periods_flash = tuple(df_flash['id'].tolist())
            if len(periods_flash) == 1:
                sql_orders_flash = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                   FROM vh_flash_order 
                                                   WHERE period = {periods_flash[0]} 
                                                   AND sub_order_status IN (1,2,3) 
                                                   AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_flash = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                   FROM vh_flash_order 
                                                   WHERE period IN {periods_flash} 
                                                   AND sub_order_status IN (1,2,3) 
                                                   AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            periods_tail = tuple(df_tail['id'].tolist())
            if len(periods_tail) == 1:
                sql_orders_tail = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                           FROM vh_tail_order 
                                                           WHERE period = {periods_tail[0]} 
                                                           AND sub_order_status IN (1,2,3) 
                                                           AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            else:
                sql_orders_tail = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                           FROM vh_tail_order 
                                                           WHERE period IN {periods_tail} 
                                                           AND sub_order_status IN (1,2,3) 
                                                           AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
            df_order_flash = pd.read_sql(sql_orders_flash, conn_orders)
            if df_order_flash.empty or df_order_flash['payment_amount'][0] is None:
                flash_sales = 0
            else:
                flash_sales = df_order_flash['payment_amount'][0]
            df_order_tail = pd.read_sql(sql_orders_tail, conn_orders)
            if df_order_tail.empty or df_order_tail['payment_amount'][0] is None:
                tail_sales = 0
            else:
                tail_sales = df_order_tail['payment_amount'][0]
            sales = flash_sales + tail_sales
            return sales
    elif order_type == 2:
        sql_commodities_cross = f"""SELECT id FROM vh_periods_cross WHERE buyer_id = {buyer_id}"""
        df_cross = pd.read_sql(sql_commodities_cross, conn_commodities)
        if df_cross.empty:
            return 0
        periods_cross = tuple(df_cross['id'].tolist())
        if len(periods_cross) == 1:
            sql_orders_cross = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                   FROM vh_cross_order 
                                                   WHERE period = {periods_cross[0]} 
                                                   AND sub_order_status IN (1,2,3) 
                                                   AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        else:
            sql_orders_cross = f"""SELECT SUM(payment_amount) 'payment_amount'
                                           FROM vh_cross_order 
                                           WHERE period IN {periods_cross} 
                                           AND sub_order_status IN (1,2,3) 
                                           AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        df_order_cross = pd.read_sql(sql_orders_cross, conn_orders)
        sales = df_order_cross['payment_amount'][0]
        return sales
    elif order_type == 1:
        sql_commodities_second = f"""SELECT id FROM vh_periods_second WHERE buyer_id = {buyer_id}"""
        df_cross = pd.read_sql(sql_commodities_second, conn_commodities)
        if df_cross.empty:
            return 0
        periods_second = tuple(df_cross['id'].tolist())
        if len(periods_second) == 1:
            sql_orders_second = f"""SELECT SUM(payment_amount) 'payment_amount'
                                                   FROM vh_second_order 
                                                   WHERE period = {periods_second[0]} 
                                                   AND sub_order_status IN (1,2,3) 
                                                   AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        else:
            sql_orders_second = f"""SELECT SUM(payment_amount) 'payment_amount'
                                           FROM vh_second_order 
                                           WHERE period IN {periods_second} 
                                           AND sub_order_status IN (1,2,3) 
                                           AND FROM_UNIXTIME(created_time,'%Y-%m') = '{month}'"""
        df_order_second = pd.read_sql(sql_orders_second, conn_orders)
        sales = df_order_second['payment_amount'][0]
        return sales
    else:
        return 0


def deal_with_data(rr_host, rr_port, rr_user, rr_password):
    insert_json_list = []
    day = get_cur_date()
    if day == '01':
        month = get_last_month()
    else:
        month = get_cur_month()
    # 获取计划数据
    df_plan = get_plans(month, rr_host, rr_port, rr_user, rr_password)
    # 组合计划数据
    insert_data_list = df_plan.values.tolist()
    for per_data in insert_data_list:
        statistical_month = per_data[0]
        buyer_id = per_data[1]
        purchaser = per_data[2]
        sales_targets = per_data[3]
        product_type = per_data[4]
        order_type = per_data[5]
        sales = get_sales(order_type, buyer_id, product_type, month, rr_host, rr_port, rr_user, rr_password)
        costs, bottle_nums, order_nums, = get_cost(order_type, buyer_id, product_type, month, rr_host, rr_port, rr_user, rr_password)
        # 达标率
        if sales_targets == 0:
            compliance_rate = 0
        else:
            compliance_rate = round(sales / sales_targets, 4) * 100
        # 毛利率
        if sales == 0:
            gross_margin = 0
        else:
            gross_margin = round((sales - costs) / sales, 4) * 100
        # 差额
        shortfall = round(sales_targets - sales)
        # 毛利
        if sales == 0:
            gross_margin_num = 0
        else:
            gross_margin_num = round(sales - costs)
        insert_json = {
            "purchaser": purchaser,
            "sales_targets": sales_targets,
            "sales": '%.2f' % sales,
            "compliance_rate": '%.4f' % compliance_rate,
            "gross_margin": '%.4f' % gross_margin,
            "gross_margin_num":gross_margin_num,
            "shortfall": shortfall,
            "order_nums": order_nums,
            "bottle_nums": bottle_nums,
            "product_type": product_type,
            "statistical_month": statistical_month,
            "order_type": order_type
        }
        insert_json_list.append(insert_json)
    return insert_json_list


def handler_purchase(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    datas = deal_with_data(rr_host, rr_port, rr_user, rr_password)
    for data in datas:
        insert_mysql(data, rm_host, rm_port, rm_user, rm_password)
    return 'ok'


