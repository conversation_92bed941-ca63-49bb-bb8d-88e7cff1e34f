import json
import calendar
import warnings
import concurrent
import pandas as pd
from config import ChannelTypeConfig
from datetime import datetime
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str


warnings.filterwarnings("ignore")


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"), pool_size=30)
    return conn_engine


def get_current_day():
    current_day = datetime.now().strftime("%Y-%m-%d")
    return current_day


def is_leap_year(year):
    if (int(year) % 4 == 0 and int(year) % 100 != 0) or int(year) % 400 == 0:
        return True
    else:
        return False



def date_difference(start_hour_time, end_hour_time):
    date1 = datetime.strptime(start_hour_time, "%Y-%m-%d %H")
    date2 = datetime.strptime(end_hour_time, "%Y-%m-%d %H")
    delta = date2 - date1
    _, days_in_month = calendar.monthrange(date1.year, date1.month)
    if is_leap_year(date1.year) == False:
        days_in_year = 365
    elif is_leap_year(date1.year) == True:
        days_in_year = 366
    if delta.days > days_in_year or delta.days == days_in_year and delta.seconds > 0:
        return 'year'
    elif delta.days > days_in_month  or delta.days == days_in_month and delta.seconds > 0:
        return 'month'
    elif delta.days > 1 or delta.days == 1 and delta.seconds > 0:
        return 'day'
    else:
        return 'hour'
    

def get_buyer_massage():
    sql = f""" select buyer_id,buyer_name from `vh_buyer_record`"""
    statistics_conn = nopool_conn("vh_data_statistics").connect()
    dff = pd.read_sql_query(sql,statistics_conn)
    statistics_conn.close()
    nopool_conn("vh_data_statistics").dispose()
    dff.set_index('buyer_name',inplace=True)
    buyer_dict=dff.T.to_dict('list')
    return buyer_dict

def get_order_massage(table,period,package,start,end):
    orders_conn = nopool_conn("vh_orders").connect()
    sql = f"""select a.main_order_id,from_unixtime(a.payment_time,"%%Y-%%m-%%d") as 'date',from_unixtime(a.payment_time,"%%H") as 'hour',a.period,aa.title,aa.price,'{table}' as 'channel_type',aa.buyer_name,aa.buyer_id,a.sub_order_no,a.payment_amount - a.refund_money as 'payment_amount',a.uid,b.associated_products,aa.import_type from {table} AS a left join vh_commodities.{period} AS aa on a.period = aa.id left join vh_commodities.{package} AS b on a.package_id = b.id left join vh_user.vh_user AS bb on a.uid = bb.uid where a.payment_time between {start} and {end} and a.sub_order_status in (1,2,3) and a.payment_amount - a.refund_money != 0 and aa.title like '%%直播%%'"""
    dff = pd.read_sql_query(sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return dff

def get_eventmark(main_order_ids):
    orders_conn = nopool_conn("vh_orders").connect()
    if len(main_order_ids) == 1:
        sql = f""" select `main_order_id`,`source_event` from vh_order_source_log where main_order_id = {main_order_ids[0]}"""
    elif len(main_order_ids) == 0:
        return pd.DataFrame(columns = ["main_order_id","source_event"])
    else:
        sql = f""" select `main_order_id`,`source_event` from vh_order_source_log where main_order_id in {main_order_ids}"""
    dff = pd.read_sql_query(sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return dff
    
                
def get_general_dff(start_hour_time,end_hour_time,log,period_type,import_type,buyer,goods_element,source_event):
    if log == "":
        if period_type == "":
            if source_event == "":
                if buyer == "":
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '[{goods_element},%%' or product_type like '%%,{goods_element}]' or product_type like '%%,{goods_element},%%' or product_type = '[{goods_element}]' and `import_type` = {import_type})) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '[{goods_element},%%' or product_type like '%%,{goods_element}]' or product_type like '%%,{goods_element},%%' or product_type = '[{goods_element}]' and `import_type` = {import_type})) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '[{goods_element},%%' or product_type like '%%,{goods_element}]' or product_type like '%%,{goods_element},%%' or product_type = '[{goods_element}]' and `import_type` = {import_type})) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer in get_buyer_massage():
                        buyer_id = get_buyer_massage()[buyer][0]
                    else:
                        if goods_element == 0:
                            if log == "":
                                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        else:
                            return pd.DataFrame([])
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
            else:
                if buyer == "":
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer in get_buyer_massage():
                        buyer_id = get_buyer_massage()[buyer][0]
                    else:
                        if goods_element == 0:
                            if log == "":
                                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        else:
                            return pd.DataFrame([])
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where  (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
        else:
            if period_type == 0:
                if source_event == "":
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3)  having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where  (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where  (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where  (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3)  having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()


            else:
                if source_event == "":
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect  where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect and where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where  (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where  (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where  (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select date,hour,period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select date,day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select date,month(date) as 'month',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select date,year(date) as 'year',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
    else:
        if period_type == "":
            if source_event == "":
                if buyer == "":
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where date between '{start_hour_time.split(" ")[0]}' and '{end_hour_time.split(" ")[0]} and concat(date," ",hour) = '{log}' and  `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and  `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and  `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  `import_type` = {import_type} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer in get_buyer_massage():
                        buyer_id = get_buyer_massage()[buyer][0]
                    else:
                        if goods_element == 0:
                            if log == "":
                                    return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        else:
                            return pd.DataFrame([])
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select day(date) as 'day',period,title,price,order_counts,payment_amount,user_counts,buyer_name from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or `date` between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}'  and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}'  and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%'"""
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
            else:
                if buyer == "":
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}'  having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}'  having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}'  having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}'))  having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}'  and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}')) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}'  and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}')) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and  `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and  `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  `import_type` = {import_type} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer in get_buyer_massage():
                        buyer_id = get_buyer_massage()[buyer][0]
                    else:
                        if goods_element == 0:
                            if log == "":
                                    return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        else:
                            return pd.DataFrame([])
                    if import_type == "":
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                    else:
                        if goods_element == 0:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  `import_type` = {import_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                        else:
                            if date_difference(start_hour_time, end_hour_time) == "hour":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "day":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date= '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date= '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "month":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
                            elif date_difference(start_hour_time, end_hour_time) == "year":
                                general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and  (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and  `import_type` = {import_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                statistics_conn = nopool_conn("vh_data_statistics").connect()
                                general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                statistics_conn.close()
                                nopool_conn("vh_data_statistics").dispose()
        else:
            if period_type == 0:
                if source_event == "":
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `channel_type` in (0,3)  having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3)) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` in (0,3) and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
            else:
                if source_event == "":
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date))= '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}'and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}'and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                else:
                    if buyer == "":
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}'  and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}'  and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and hour(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and hour(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            return pd.DataFrame([])
                        if import_type == "":
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}'and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}'and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}'and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                        else:
                            if goods_element == 0:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and `date` = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id} having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                            else:
                                if date_difference(start_hour_time, end_hour_time) == "hour":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(date," ",hour) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "day":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and date = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}'  and date = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "month":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and concat(year(date),"-",month(date)) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
                                elif date_difference(start_hour_time, end_hour_time) == "year":
                                    general_sql  = f""" select period,title,price,order_counts,payment_amount,buyer_name,user_counts from  vh_sales_statistics_dialect where (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (product_type like '{goods_element},%%' or product_type like '%%,{goods_element}' or product_type like '%%,{goods_element},%%' or product_type = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) or (source_event = '{source_event}' and concat(date," ",`hour`) between '{start_hour_time}' and '{end_hour_time}' and year(date) = '{log}' and (fid like '{goods_element},%%' or fid like '%%,{goods_element}' or fid like '%%,{goods_element},%%' or fid = '{goods_element}') and `import_type` = {import_type} and `channel_type` = {period_type} and `buyer_id` = {buyer_id}) having title like '%%直播%%' """
                                    statistics_conn = nopool_conn("vh_data_statistics").connect()
                                    general_dff = pd.read_sql_query(general_sql,statistics_conn)
                                    statistics_conn.close()
                                    nopool_conn("vh_data_statistics").dispose()
    return general_dff


def get_current_day_massage(end_hour_time,goods_element):
    start,end = int(datetime.strptime(f"""{get_current_day()} 00:00:00""","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"""{end_hour_time}:59:59""","%Y-%m-%d %H:%M:%S").timestamp())
    Cg = ChannelTypeConfig.channel_type_config
    tables,periods,packages = [Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]],[Cg[1][1],Cg[2][1],Cg[3][1],Cg[4][1]],[Cg[1][2],Cg[2][2],Cg[3][2],Cg[4][2]]
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        tasks = [executor.submit(get_order_massage,table,period,package,start,end) for table,period,package in zip(tables,periods,packages)]
        Sale_Data = pd.DataFrame(columns=["main_order_id","date","hour","period","title","price","channel_type","buyer_name","buyer_id","sub_order_no","payment_amount","uid","associated_products","import_type"])
        for future in concurrent.futures.as_completed(tasks):
            Sale_Data=Sale_Data.append(future.result()).reset_index(drop=True)
    if Sale_Data.empty:
        return pd.DataFrame(columns=["main_order_id","date","hour","period","title","price","channel_type","buyer_name","buyer_id","sub_order_no","payment_amount","uid","associated_products","import_type"])
    if len(tuple(set(Sale_Data.main_order_id.tolist()))) == 1:
        main_order_no_sql = f""" select id as 'main_order_id',main_order_no from vh_order_main where id = {tuple(set(Sale_Data.main_order_id.tolist()))[0]} """
    else:
        main_order_no_sql = f""" select id as 'main_order_id',main_order_no from vh_order_main where id in {tuple(set(Sale_Data.main_order_id.tolist()))} """
    order_conn = nopool_conn("vh_orders").connect()
    main_order_no_dff = pd.read_sql_query(main_order_no_sql,order_conn)
    Sale_Data = pd.merge(Sale_Data,main_order_no_dff,how="left",on="main_order_id")
    if len(tuple(set(Sale_Data.main_order_no.tolist()))) == 1:
        mh_sql = f"""  select main_order_no,period,product_info from vh_order_mystery_box_log where main_order_no = '{tuple(set(Sale_Data.main_order_no.tolist()))[0]}' """
    else:
        mh_sql = f"""  select main_order_no,period,product_info from vh_order_mystery_box_log where main_order_no in {tuple(set(Sale_Data.main_order_no.tolist()))} """
    mh_dff = pd.read_sql_query(mh_sql,order_conn)
    Sale_Data = pd.merge(Sale_Data,mh_dff,how="left",on=["main_order_no","period"])
    order_conn.close()
    nopool_conn("vh_orders").dispose()
    for index, row in Sale_Data.iterrows():
        if pd.notna(row['product_info']):
            Sale_Data.at[index, 'associated_products'] = row['product_info']
    have_massage = Sale_Data[Sale_Data.associated_products.apply(lambda x:x != '[]' and x != None and type(x) != float)]
    no_massage = Sale_Data[Sale_Data.associated_products.apply(lambda x:x == '[]' or x == None or type(x) == float)]
    transfer_df = have_massage.copy()
    transfer_df["associated_products"]=transfer_df.associated_products.apply(lambda x:eval(x))
    is_any = []
    for is_a in transfer_df.associated_products:
        if len(is_a) == 1:
            a = 0
        elif len(is_a) > 1:
            a = 1
        is_any.append(a)
    transfer_df["is_any"] = is_any
    main_data_any = transfer_df[transfer_df["is_any"] == 1]
    main_data_one = transfer_df[transfer_df["is_any"] == 0]
    if main_data_any.empty:
        dff_main_data = main_data_one
        dff_main_data["product_id"] = dff_main_data.associated_products.apply(lambda x:x[0]["product_id"])
        dff_main_data["nums"] = dff_main_data.associated_products.apply(lambda x:x[0]["nums"])        
    else:
        main_data_one["product_id"] = main_data_one.associated_products.apply(lambda x:x[0]["product_id"])
        main_data_one["nums"] = main_data_one.associated_products.apply(lambda x:x[0]["nums"])    
        main_data_any = main_data_any.explode('associated_products').reset_index(drop=True)
        main_data_any["mark"] = main_data_any.associated_products.apply(lambda x:json.dumps(x))
        main_data_any = main_data_any.drop_duplicates(subset=["sub_order_no","mark"],keep="first")
        main_data_any["product_id"] = main_data_any.associated_products.apply(lambda x:x["product_id"])
        main_data_any["nums"] = main_data_any.associated_products.apply(lambda x:x["nums"])
        dff_main_data = main_data_one.append(main_data_any).reset_index(drop=True)
    product_id = tuple(dff_main_data.product_id.tolist())
    if len(product_id ) == 1:
        in_sql = f"select id as 'product_id',product_type from vh_products where id = {product_id[0]}"
        wiki_conn = nopool_conn("vh_wiki").connect()
        in_df = pd.read_sql_query(in_sql, wiki_conn)
        wiki_conn.close()
        nopool_conn("vh_wiki").dispose()
    elif len(product_id ) == 0:
        in_df = pd.DataFrame(columns=["product_id","product_type"])
    else:
        in_sql = f"select a.id as 'product_id',a.product_type,aa.fid from vh_products as a left join vh_product_type as aa on a.product_type = aa.id where a.id in {product_id}"
        wiki_conn = nopool_conn("vh_wiki").connect()
        in_df = pd.read_sql_query(in_sql, wiki_conn)
        wiki_conn.close()
        nopool_conn("vh_wiki").dispose()
    Sale_df = pd.merge(dff_main_data, in_df, how="left", on="product_id")
    if goods_element == 0:
        Sale_df.loc[Sale_df.duplicated(subset='sub_order_no', keep='first'), 'payment_amount'] = 0
        Sale_df.loc[Sale_df.duplicated(subset='sub_order_no', keep='first'), 'sub_order_no'] = None
    else:
        Sale_df =  Sale_df[(Sale_df.product_type == goods_element) | (Sale_df.fid == goods_element)]
        Sale_df.loc[Sale_df.duplicated(subset='sub_order_no', keep='first'), 'payment_amount'] = 0
        Sale_df.loc[Sale_df.duplicated(subset='sub_order_no', keep='first'), 'sub_order_no'] = None
    Sale_df.drop(["is_any","product_id"], axis=1, inplace=True)
    no_massage["nums"] = ''
    no_massage["product_type"] = ''
    no_massage["fid"] = ''
    Sale_df = Sale_df.append(no_massage).reset_index(drop=True)
    main_order_ids = tuple(set(Sale_df.main_order_id.tolist()))
    eventmark_df = get_eventmark(main_order_ids)
    Sale_df = pd.merge(Sale_df,eventmark_df,how="left",on="main_order_id")
    Sale_df["source_event"] = Sale_df.source_event.fillna("internal")
    Sale_dff = Sale_df.groupby(by=["date","hour","channel_type","source_event","period"],as_index=False).agg({"title":"first","price":"first","buyer_name":"first","buyer_id":"first","sub_order_no":"count","payment_amount":sum,"product_type":set,"import_type":"first"})
    Sale_dff["product_type"] = Sale_dff.product_type.apply(lambda x:list(x))
    Sale_df_count = Sale_df[["date","hour","channel_type","source_event","period","uid"]].groupby(by=["date","hour","channel_type","source_event","period"],as_index=False)["uid"].nunique().reset_index(drop=True)
    Sale_dff = pd.merge(Sale_dff,Sale_df_count,how="left",on=["date","hour","channel_type","source_event","period"])
    Sale_dff = Sale_dff.rename(columns={"sub_order_no":"order_counts","uid":"user_counts"})
    Sale_dff = Sale_dff[["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"]]
    channel_dict = {"vh_flash_order":0,"vh_cross_order":1,"vh_second_order":2,"vh_tail_order":3}
    Sale_dff["channel_type"] = Sale_dff.channel_type.apply(lambda x:channel_dict[x])
    return Sale_dff



def get_live_result(start_hour_time,end_hour_time,period_type,import_type,buyer,product_type,source_event,log,page,page_nums):
    if period_type != "":
        period_type = int(period_type)
    else:
        pass
    if import_type != "":
        import_type = int(import_type)
    if date_difference(start_hour_time, end_hour_time) == "month":
        if log != "":
            if log.split("-")[1][0] == "0":
                log = f"""{log.split("-")[0]}-{log.split("-")[1][1]}"""
    if end_hour_time.split(" ")[0] == get_current_day():
        if product_type == [0]:
            general_dff = get_general_dff(start_hour_time,end_hour_time,log,period_type,import_type,buyer,product_type[0],source_event)
        else:
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                tasks = [executor.submit(get_general_dff,start_hour_time,end_hour_time,log,period_type,import_type,buyer,goods_element,source_event) for goods_element in product_type]
                if date_difference(start_hour_time, end_hour_time) == "year":
                    general_dff = pd.DataFrame(columns=["date","year","period","title","price","order_counts","payment_amount","user_counts"])
                elif date_difference(start_hour_time, end_hour_time) == "month":
                    general_dff = pd.DataFrame(columns=["date","month","period","title","price","order_counts","payment_amount","user_counts"])
                elif date_difference(start_hour_time, end_hour_time) == "day":
                    general_dff = pd.DataFrame(columns=["date","day","period","title","price","order_counts","payment_amount","user_counts"])
                elif date_difference(start_hour_time, end_hour_time) == "hour":
                    general_dff = pd.DataFrame(columns=["date","hour","period","title","price","order_counts","payment_amount","user_counts"])
                for future in concurrent.futures.as_completed(tasks):
                    general_dff=general_dff.append(future.result()).reset_index(drop=True)
        if period_type == "":
            if source_event == "":
                if buyer == "":
                    if import_type == "":
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                    else:
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                            current_dff = current_dff[current_dff.import_type == import_type]
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                            current_dff = current_dff[current_dff.import_type == import_type]
                else:
                    if buyer in get_buyer_massage():
                        buyer_id = get_buyer_massage()[buyer][0]
                    else:
                        if log == "":
                                    return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                        else:
                            return {"massage_json":{"massage":[],"total":0}}
                    if import_type == "":
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                            current_dff = current_dff[current_dff.buyer_id == buyer_id]
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[current_dff.buyer_id == buyer_id]
                    else:
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                            current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id)]
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                            current_dff = current_dff[current_dff.import_type == import_type & (current_dff.buyer_id == buyer_id)]
            else:
                if buyer == "":
                    if import_type == "":
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                            current_dff = current_dff[current_dff.source_event == source_event]
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                            current_dff = current_dff[current_dff.source_event == source_event]
                    else:
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                            current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.source_event == source_event)]
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                            current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.source_event == source_event)]
                else:
                    if buyer in get_buyer_massage():
                        buyer_id = get_buyer_massage()[buyer][0]
                    else:
                        if log == "":
                                    return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                        else:
                            return {"massage_json":{"massage":[],"total":0}}
                    if import_type == "":
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                            current_dff = current_dff[(current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event)]
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                            current_dff = current_dff[(current_dff.buyer_id) == buyer_id & (current_dff.source_event == source_event)]
                    else:
                        if product_type == [0]:
                            current_dff = get_current_day_massage(end_hour_time,product_type[0])
                            current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event)]
                        else:
                            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                for future in concurrent.futures.as_completed(tasks):
                                    current_dff=current_dff.append(future.result()).reset_index(drop=True)
                            current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event)]
        else:
            if period_type == 0:
                if source_event == "":
                    if buyer == "":
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[current_dff.channel_type.apply(lambda x:x in (0,3))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[current_dff.channel_type.apply(lambda x:x in (0,3))]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            if log == "":
                                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                    current_dff = current_dff[(current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                else:
                    if buyer == "":
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.channel_type.apply(lambda x:x in (0,3))) & (current_dff.source_event == source_event)]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = [[(current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            if log == "":
                                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.buyer_id) == buyer_id & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x in (0,3)))]
            else:
                if source_event == "":
                    if buyer == "":
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[current_dff.channel_type == period_type]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[current_dff.channel_type == period_type]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            if log == "":
                                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                    current_dff = current_dff[(current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                else:
                    if buyer == "":
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.channel_type == period_type) & (current_dff.source_event == source_event)]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = [[(current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x == period_type))]]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                    else:
                        if buyer in get_buyer_massage():
                            buyer_id = get_buyer_massage()[buyer][0]
                        else:
                            if log == "":
                                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
                            else:
                                return {"massage_json":{"massage":[],"total":0}}
                        if import_type == "":
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.buyer_id) == buyer_id & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                        else:
                            if product_type == [0]:
                                current_dff = get_current_day_massage(end_hour_time,product_type[0])
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x == period_type))]
                            else:
                                with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                                    tasks = [executor.submit(get_current_day_massage,end_hour_time,goods_element) for goods_element in product_type]
                                    current_dff = pd.DataFrame(columns=["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type"])
                                    for future in concurrent.futures.as_completed(tasks):
                                        current_dff=current_dff.append(future.result()).reset_index(drop=True)
                                current_dff = current_dff[(current_dff.import_type == import_type) & (current_dff.buyer_id == buyer_id) & (current_dff.source_event == source_event) & (current_dff.channel_type.apply(lambda x:x == period_type))]
        if current_dff.empty and general_dff.empty:
            if log == "":
                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
            else:
                return {"massage_json":{"massage":[],"total":0}}
        if date_difference(start_hour_time, end_hour_time) == "year":
            if current_dff.empty:
                pass
            elif general_dff.empty:
                current_dff["year"] = current_dff.date.apply(lambda x:datetime.strptime(x,"%Y-%m-%d").year)
                general_dff = current_dff.copy()
            else:
                current_dff["year"] = current_dff.date.apply(lambda x:datetime.strptime(x,"%Y-%m-%d").year)
                general_dff = general_dff.append(current_dff[["date","year","period","title","price","order_counts","payment_amount","user_counts","buyer_name"]]).reset_index(drop=True)
        elif date_difference(start_hour_time, end_hour_time) == "month":
            if current_dff.empty:
                pass
            elif general_dff.empty:
                current_dff["year"] = current_dff.date.apply(lambda x:datetime.strptime(x,"%Y-%m-%d").year)
                general_dff = current_dff[["date","hour","period","title","price","order_counts","payment_amount","user_counts","buyer_name"]]
            else:
                current_dff["month"] = current_dff.date.apply(lambda x:datetime.strptime(x,"%Y-%m-%d").month)
                general_dff = general_dff.append(current_dff[["date","month","period","title","price","order_counts","payment_amount","user_counts","buyer_name"]]).reset_index(drop=True)
        elif date_difference(start_hour_time, end_hour_time) == "day":
            if current_dff.empty:
                pass
            elif general_dff.empty:
                current_dff["year"] = current_dff.date.apply(lambda x:datetime.strptime(x,"%Y-%m-%d").year)
                general_dff = current_dff[["date","hour","period","title","price","order_counts","payment_amount","user_counts","buyer_name"]]
            else:
                current_dff["day"] = current_dff.date.apply(lambda x:datetime.strptime(x,"%Y-%m-%d").day)
                general_dff = general_dff.append(current_dff[["date","day","period","title","price","order_counts","payment_amount","user_counts","buyer_name"]]).reset_index(drop=True)
        elif date_difference(start_hour_time, end_hour_time) == "hour":
            if current_dff.empty:
                pass
            elif general_dff.empty:
                general_dff = current_dff[["date","hour","period","title","price","order_counts","payment_amount","user_counts","buyer_name"]]
            else:
                general_dff = general_dff.append(current_dff[["date","hour","period","title","price","order_counts","payment_amount","user_counts","buyer_name"]]).reset_index(drop=True)
        if  general_dff.empty:
            if log == "":
                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
            else:
                return {"massage_json":{"massage":[],"total":0}}
        else:
            if log == "":
                if date_difference(start_hour_time, end_hour_time) == "year":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["year"] = general_dff.year.astype(str)
                    general_dff["year"] = general_dff.date.apply(lambda x:x.split("-")[0])
                    general_dff = general_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "month":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["month"] = general_dff.month.astype(str)
                    general_dff["month"] = general_dff.date.apply(lambda x:x.split("-")[0] + "-" + x.split("-")[1])
                    general_dff = general_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "day":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["day"] = general_dff.day.astype(str)
                    general_dff["day"] = general_dff.date.apply(lambda x:x)
                    general_dff = general_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "hour":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["hour"] = general_dff.hour.astype(str)
                    general_dff["hour"] = general_dff.date + " " + general_dff.hour
                    general_dff = general_dff.drop("date",axis = 1)
                general_view = general_dff.copy()
                general_massage_orders = general_dff.groupby(by="period",as_index=False).agg({"title":"first","price":"first","order_counts":sum,"payment_amount":sum,"user_counts":sum})
                general_massage_orders = general_massage_orders.sort_values(by="payment_amount",ascending=False)
                general_massage = general_massage_orders[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
                general_view_res = general_view.groupby(by = general_view.columns[0],as_index = False).agg({"order_counts":sum,"payment_amount":sum})
                general_view_res["payment_amount"] = general_view_res.payment_amount.apply(lambda x:round(x))
                return {"general_json":json.loads(general_view_res.to_json(orient="records")),"unit":date_difference(start_hour_time, end_hour_time),"massage_json":{"massage":json.loads(general_massage.to_json(orient="records")),"total":len(general_massage_orders)}}
            else:
                general_dff = get_general_dff(start_hour_time,end_hour_time,log,period_type,import_type,buyer,product_type[0],source_event)
                general_data = general_dff.groupby(by="period",as_index=False).agg({"title":"first","price":"first","order_counts":sum,"payment_amount":sum,"buyer_name":"first","user_counts":sum})
                if date_difference(start_hour_time, end_hour_time) == "year":
                    current_dff["date"] = current_dff.date.astype(str)
                    current_dff["year"] = current_dff.year.astype(str)
                    current_dff["year"] = current_dff.date.apply(lambda x:x.split("-")[0])
                    current_dff = current_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "month":
                    current_dff["date"] = current_dff.date.astype(str)
                    current_dff["month"] = current_dff.month.astype(str)
                    current_dff["month"] = current_dff.date.apply(lambda x:x.split("-")[0] + "-" + x.split("-")[1])
                    current_dff = current_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "day":
                    current_dff["date"] = current_dff.date.astype(str)
                    current_dff["day"] = current_dff.day.astype(str)
                    current_dff["day"] = current_dff.date.apply(lambda x:x)
                    current_dff = current_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "hour":
                    current_dff["date"] = current_dff.date.astype(str)
                    current_dff["hour"] = current_dff.hour.astype(str)
                    current_dff["hour"] = current_dff.date + " " + current_dff.hour
                    current_dff = current_dff.drop("date",axis = 1)
                current_dff = current_dff[current_dff.iloc[:, 0] == log]
                current_data = current_dff.groupby(by="period",as_index=False).agg({"title":"first","price":"first","order_counts":sum,"payment_amount":sum,"buyer_name":"first","user_counts":sum})
                general_massage_orders = general_data.append(current_data).reset_index(drop=True)
                general_massage_orders = general_massage_orders.sort_values(by="payment_amount",ascending=False)
                general_massage = general_massage_orders[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
                return {"massage_json":json.loads(general_massage.to_json(orient="records")),"total":len(general_massage_orders)}
    else:
        if product_type == [0]:
            general_dff = get_general_dff(start_hour_time,end_hour_time,log,period_type,import_type,buyer,product_type[0],source_event)
        else:
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(product_type) * 2) as executor:
                tasks = [executor.submit(get_general_dff,start_hour_time,end_hour_time,log,period_type,import_type,buyer,goods_element,source_event) for goods_element in product_type]
                if date_difference(start_hour_time, end_hour_time) == "year":
                    general_dff = pd.DataFrame(columns=["date","year","period","title","price","order_counts","payment_amount","user_counts","buyer_name"])
                elif date_difference(start_hour_time, end_hour_time) == "month":
                    general_dff = pd.DataFrame(columns=["date","month","period","title","price","order_counts","payment_amount","user_counts","buyer_name"])
                elif date_difference(start_hour_time, end_hour_time) == "day":
                    general_dff = pd.DataFrame(columns=["date","day","period","title","price","order_counts","payment_amount","user_counts","buyer_name"])
                elif date_difference(start_hour_time, end_hour_time) == "hour":
                    general_dff = pd.DataFrame(columns=["date","hour","period","title","price","order_counts","payment_amount","user_counts","buyer_name"])
                for future in concurrent.futures.as_completed(tasks):
                    general_dff=general_dff.append(future.result()).reset_index(drop=True)
        
        if general_dff.empty:
            if log == "":
                return {"general_json":[],"massage_json":{"massage":[],"total":0}}
            else:
                return {"massage_json":{"massage":[],"total":0}}
        else:
            if log == "":
                if date_difference(start_hour_time, end_hour_time) == "year":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["year"] = general_dff.year.astype(str)
                    general_dff["year"] = general_dff.date.apply(lambda x:x.split("-")[0])
                    general_dff = general_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "month":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["month"] = general_dff.month.astype(str)
                    general_dff["month"] = general_dff.date.apply(lambda x:x.split("-")[0] + "-" + x.split("-")[1])
                    general_dff = general_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "day":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["day"] = general_dff.day.astype(str)
                    general_dff["day"] = general_dff.date.apply(lambda x:x)
                    general_dff = general_dff.drop("date",axis = 1)
                elif date_difference(start_hour_time, end_hour_time) == "hour":
                    general_dff["date"] = general_dff.date.astype(str)
                    general_dff["hour"] = general_dff.hour.astype(str)
                    general_dff["hour"] = general_dff.date + " " + general_dff.hour
                    general_dff = general_dff.drop("date",axis = 1)
                general_view = general_dff.copy()
                general_massage_orders = general_dff.groupby(by="period",as_index=False).agg({"title":"first","price":"first","order_counts":sum,"payment_amount":sum,"user_counts":sum})
                general_massage_orders = general_massage_orders.sort_values(by="payment_amount",ascending=False)
                general_massage = general_massage_orders[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
                general_view_res = general_view.groupby(by = general_view.columns[0],as_index = False).agg({"order_counts":sum,"payment_amount":sum})
                general_view_res["payment_amount"] = general_view_res.payment_amount.apply(lambda x:round(x))
                return {"general_json":json.loads(general_view_res.to_json(orient="records")),"unit":date_difference(start_hour_time, end_hour_time),"massage_json":{"massage":json.loads(general_massage.to_json(orient="records")),"total":len(general_massage_orders)}}
            else:
                general_dff = get_general_dff(start_hour_time,end_hour_time,log,period_type,import_type,buyer,product_type[0],source_event)
                general_massage_orders = general_dff.groupby(by="period",as_index=False).agg({"title":"first","price":"first","order_counts":sum,"payment_amount":sum,"buyer_name":"first","user_counts":sum})
                general_massage_orders = general_massage_orders.sort_values(by="payment_amount",ascending=False)
                general_massage = general_massage_orders[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
                return {"massage_json":{"massage":json.loads(general_massage.to_json(orient="records")),"total":len(general_massage_orders)}}