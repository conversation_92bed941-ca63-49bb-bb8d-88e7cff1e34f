import json
import time
import base64
import requests
import warnings
import pandas as pd
from pymongo import MongoClient
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str
from datetime import datetime, timezone
from config import DingtalkAccessToken
from matplotlib.font_manager import FontProperties
import matplotlib.pyplot as plt
from io import BytesIO
import requests
import hashlib


font = FontProperties(fname="/var/www/html/simhei.ttf")
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings("ignore")


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine



def send_wechat_image_message(image_data, md5, key) -> json:
    url = f'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}'
    payload = {
        'msgtype': 'image',
        'image': {
            'base64': image_data,
            'md5': md5
        }
    }
    json_payload = json.dumps(payload)
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, headers=headers, data=json_payload)
    return response.status_code,response.content


def get_created_uid(start_time,over_time,conn_auction) -> object:
    """
    获取首次参拍用户
    :param now:
    :return:
    """
    try:
        user_conn = nopool_conn("vh_user").connect()
        user_sql = f""" select from_unixtime(created_time,'%%H') AS 'hour',uid AS 'created_user_counts' from vh_user where created_time between {start_time} and {over_time} and tripartite_source = 'vinehoo-auction_moments' """
        record_sql = f""" select from_unixtime(min(created_time),'%%H') AS 'hour',created_time,uid AS 'first_record_counts' from vh_goods_bid_record where is_vest = 0 group by `uid` having `created_time` between {start_time} and {over_time}"""
        record_dff = pd.read_sql_query(record_sql,conn_auction)
        user_dff = pd.read_sql_query(user_sql,user_conn)
        user_conn.close()
        nopool_conn("vh_user").dispose()
        out_dff = pd.merge(record_dff[["hour","first_record_counts"]],user_dff,on="hour")
        return out_dff
    except Exception  as e:
        print(f"获取首次参拍用户获取失败:{e}")
        return -1
    
    

def get_now_to_aster() -> tuple:
    """
    获取当前时间
    :return:
    """
    now = datetime.now()
    return now


def one_day_times(now:tuple) ->tuple:
    """
    获取当天起止时间戳
    :param now:
    :return:
    """
    start_time = int(datetime.strptime(f"""{now.year}-{now.month}-{now.day} 00:00:00""","%Y-%m-%d %H:%M:%S").timestamp())
    over_time = int(datetime.strptime(f"""{now.year}-{now.month}-{now.day} {now.hour}:{now.minute}:{now.second}""","%Y-%m-%d %H:%M:%S").timestamp())
    return start_time,over_time


def get_hourly_pv(gte_time, lte_time) -> pd.DataFrame:
    """
    获取指定时间范围内每小时的PV数量
    :param gte_time: 开始时间
    :param lte_time: 结束时间
    :return: 包含每小时PV数量的DataFrame对象，如果出现错误返回空DataFrame对象
    """
    try:
        client = MongoClient('************************************************/?authSource=admin&ssl=false&directConnection=true')
        result = client['vinehoo_v3']['gateway_logs'].aggregate([
            {
                '$match': {
                    'request_time': {
                        '$gte': datetime(gte_time.tm_year, gte_time.tm_mon, gte_time.tm_mday, gte_time.tm_hour, tzinfo=timezone.utc),
                        '$lte': datetime(lte_time.tm_year, lte_time.tm_mon, lte_time.tm_mday, lte_time.tm_hour, tzinfo=timezone.utc)
                    },
                    'route_url': "/auction-goods/v3/goods/getGoodsDetail"
                }
            },
            {
                '$group': {
                    '_id': {'$hour': '$request_time'},
                    'count': {'$sum': 1}
                }
            },
            {
                '$project': {
                    '_id': 0,
                    'hour': '$_id',
                    'count': 1
                }
            }
        ])
        df = pd.DataFrame(list(result))
        if df.empty:
            dff = pd.DataFrame(columns=["hour","PV"])
        else:
            dff = df.rename(columns={"Hour":"hour","count":"PV"}).sort_values(by="hour")[["hour","PV"]].reset_index(drop=True)
        return dff
    except Exception as e:
        print(f"PV获取失败:{e}")
        return pd.DataFrame()

    


def get_hourly_uv(gte_time, lte_time) -> pd.DataFrame:
    """
    获取UV
    :param gte_time: 起始时间
    :param lte_time: 结束时间
    :return: 包含每个小时对应的UV的DataFrame
    """
    try:
        client = MongoClient('************************************************/'
                             '?authSource=admin&ssl=false&directConnection=true')
        result = client['vinehoo_v3']['gateway_logs'].aggregate(
            [{
                '$match': {
                    'request_time': {
                        '$gte': datetime(gte_time.tm_year, gte_time.tm_mon, gte_time.tm_mday, gte_time.tm_hour, gte_time.tm_min, tzinfo=timezone.utc), 
                        '$lte': datetime(lte_time.tm_year, lte_time.tm_mon, lte_time.tm_mday, lte_time.tm_hour, lte_time.tm_min, tzinfo=timezone.utc)
                    },
                    'route_url':"/auction-goods/v3/goods/getGoodsDetail"
                }
            }, {
                '$group': {
                    '_id': {
                        'hour': {'$hour': '$request_time'},
                        'uid': '$uid'
                    }
                }
            }, {
                '$group': {
                    '_id': '$_id.hour',
                    'count': {'$sum': 1}
                }
            }
        ])
        df = pd.DataFrame(result)
        df = df.rename(columns={'_id': 'hour', 'count': 'UV'})
        if df.empty:
            dff = pd.DataFrame(columns=["hour","UV"])
        else:
            dff = df.sort_values(by="hour")[["hour","UV"]].reset_index(drop=True)
        return dff
    except Exception as e:
        print(f"UV获取失败:{e}")
        return pd.DataFrame()



def get_res_plot(start_time:int,over_time:int,lte_time:tuple,PV:pd.DataFrame,UV:pd.DataFrame) -> dict:
    """
    获取拍卖数据统计
    :param start_time:
    :param over_time:
    :param PV:
    :param UV:
    :param conn_auction:
    :return:
    """
    try:
        hour_df = pd.DataFrame(columns=["hour"])
        hour_list = []
        for hour in range(0,lte_time.tm_hour):
            if len(str(hour)) == 1:
                hour_list.append(f"0{hour}")
            else:
                hour_list.append(str(hour))
        hour_df["hour"] = hour_list
        PV["hour"] = PV.hour.apply(lambda x:f"0{x}" if len(str(x)) == 1 else str(x))
        UV["hour"] = UV.hour.apply(lambda x:f"0{x}" if len(str(x)) == 1 else str(x))
        conn_auction = nopool_conn("vh_auction").connect()
        record_sql = f"""select FROM_UNIXTIME(created_time,'%%H') as 'hour',count(id) as 'record_count',count(DISTINCT uid) as 'record_user_count',count(DISTINCT goods_id) as 'record_goods_count' from vh_goods_bid_record where created_time BETWEEN {start_time} and {over_time} and is_vest = 0 group by `hour`"""
        record_df = pd.read_sql_query(record_sql,conn_auction)
        record_df["hour"] = record_df.hour.astype(str)
        record_dff = pd.merge(hour_df,record_df,how="left",on="hour")
        record_dff = record_dff.fillna(0)
        order_sql = f"""select FROM_UNIXTIME(a.payment_time,'%%H') as 'hour',count(a.order_no) as 'order_count',sum(a.payment_amount) as 'amount_sum',sum(a.payment_amount) - sum(aa.cost_price) as 'gross_margin_num' from vh_orders AS a left join vh_goods AS aa on a.goods_id = aa.id where a.payment_time BETWEEN {start_time} and {over_time} group by `hour` """
        order_df = pd.read_sql_query(order_sql,conn_auction)
        order_df["hour"] = order_df.hour.astype(str)
        order_datas = pd.merge(record_dff,order_df,how="left",on="hour")
        order_datas = order_datas.fillna(0)
        pull_now_dff = get_created_uid(start_time,over_time,conn_auction)
        conn_auction.close()
        nopool_conn("vh_auction").dispose
        if pull_now_dff.empty:
            order_datas["created_user_counts"] = 0
            order_datas["first_record_counts"] = 0
        else:
            pull_now_dff = pull_now_dff.groupby(by="hour",as_index=False).agg({"created_user_counts":"count","first_record_counts":"count"})
            order_datas = pd.merge(order_datas,pull_now_dff,how="left",on="hour")
        order_datas = pd.merge(order_datas,PV,how="left",on="hour").merge(UV,how="left",on="hour")
        order_datas = order_datas.fillna(0)
        plt.figure(figsize=(12, 6))
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['PV'], linestyle='--', marker='o', markersize=5, color='tab:blue', label='PV')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['UV'], linestyle='--', marker='o', markersize=5, color='tab:orange', label='UV')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['record_user_count'], linestyle='--', marker='o', markersize=5, color='tab:green', label='参拍人数')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['record_goods_count'], linestyle='--', marker='o', markersize=5, color='tab:red', label='出价拍品数')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['record_count'], linestyle='--', marker='o', markersize=5, color='tab:purple', label='出价数')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['order_count'], linestyle='--', marker='o', markersize=5, color='tab:brown', label='订单数')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['amount_sum'], linestyle='--', marker='o', markersize=5, color='tab:pink', label='销售额')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['gross_margin_num'], linestyle='--', marker='o', markersize=5, color='tab:olive', label='毛利值')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['created_user_counts'], linestyle='--', marker='o', markersize=5, color='tab:cyan', label='标识注册人数')
        plt.plot(order_datas['hour'].astype(int).sort_values().astype(str), order_datas['first_record_counts'], linestyle='--', marker='o', markersize=5, color='tab:cyan', label='首次出价人数')
        plt.legend(prop = font)
        plt.xlabel('小时',fontproperties=font)
        plt.title('拍卖小时走势',fontproperties=font)
        buffer = BytesIO()
        plt.savefig(buffer, format='jpg')
        buffer.seek(0)
        img_content = buffer.getvalue()
        img_md5 = hashlib.md5(img_content).hexdigest()
        img_str = base64.b64encode(buffer.read()).decode('utf-8')
        send_wechat_image_message(image_data=img_str, md5=img_md5, key=DingtalkAccessToken.auction_access_token)#'4399b225-9d5e-4003-90a5-8d8f118f4e96'
    except Exception as e:
        print(f"拍卖数据统计获取失败:{e}")
        return -1

def get_plot_main(now) -> str:
    try:
        start_time,over_time = one_day_times(now)
        gte_time = time.localtime(start_time)
        lte_time = time.localtime(over_time)
        UV = get_hourly_uv(gte_time,lte_time)
        PV = get_hourly_pv(gte_time,lte_time)
        res_json = get_res_plot(start_time,over_time,lte_time,PV,UV)
        return res_json
    except Exception as e:
        print(f"折线图主体执行失败:{e}")
        return -1

def get_plot_robot():
    return get_plot_main(get_now_to_aster())