# 获取配置
from app.CommonLibraries.AddRequests import add_requests
import config
import json


def get_config(data_id, group):
    """
    获取配置
    :param data_id: nacos data_id
    :param group: nacos group
    :return:
    """
    url_route = "config/v3/nacos/readstring"
    url = f"{config.ConfigCenter.config_url['config_url']}/{url_route}"
    req_url = f"{url}?dataid={data_id}&group={group}"
    config_info = add_requests(req_type='get', url=req_url, body={})
    if config_info['error_code'] != 0:
        raise Exception("获取配置信息失败")
    config_json = json.loads(config_info['data'])
    return config_json


def get_config_str(data_id, group):
    """
    获取配置
    :param data_id: nacos data_id
    :param group: nacos group
    :return:
    """
    url_route = "config/v3/nacos/readstring"
    url = f"{config.ConfigCenter.config_url['config_url']}/{url_route}"
    req_url = f"{url}?dataid={data_id}&group={group}"
    config_info = add_requests(req_type='get', url=req_url, body={})
    if config_info['error_code'] != 0:
        raise Exception("获取配置信息失败")
    return config_info['data']