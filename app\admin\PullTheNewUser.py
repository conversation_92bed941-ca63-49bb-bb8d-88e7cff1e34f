import pandas as pd
import warnings
import json
import time
import concurrent
from app.DButils.MysqlHelper import conn_str
from sqlalchemy import create_engine


warnings.filterwarnings("ignore")

def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


#####根据id获取地区名称
def get_regional_name(regional_id_tup,users_conn):
    try:
        if len(regional_id_tup) == 0:
            regional_name_df = pd.DataFrame(columns=["id","name"])
        elif len(regional_id_tup) > 1:
            regional_name_sql=f"select id,`name` from vh_regional where id in {regional_id_tup}"
            regional_name_df= pd.read_sql_query(regional_name_sql, users_conn)
        elif len(regional_id_tup) == 1:
            regional_name_sql=f"select id,`name` from vh_regional where id = {regional_id_tup[0]}"
            regional_name_df= pd.read_sql_query(regional_name_sql, users_conn)
        return regional_name_df
    except Exception as e:
        return {"error_code": "地区信息获取失败", "error_msg": f"{e}", "status": "fail", "data": {}}
    


#####获取商品信息
def get_commodities(period_tup,commoditie):
    try:
        if len(period_tup) > 1:
            commodities_sql=f"select id,title,product_category from {commoditie} where id in {period_tup}"
        elif len(period_tup) == 1:
            commodities_sql=f"select id,title,product_category from {commoditie} where id = {period_tup[0]}"
        conn_commodities = nopool_conn("vh_commodities").connect()
        commodities_df=pd.read_sql_query(commodities_sql,conn_commodities)
        conn_commodities.close()
        nopool_conn("vh_commodities").dispose()
        return commodities_df
    except Exception as e:
            return {"error_code": "商品信息获取失败", "error_msg": f"{e}", "status": "fail", "data": {}}
        
        

#####获取最后登陆地址:
def get_address(user_id_list):
    conn_statistics = nopool_conn("vh_data_statistics").connect()
    if len(user_id_list) == 1:
        ip_sql = f""" select uid,ip_address from vh_user_login_behavior where uid = {user_id_list[0]} """
    else:
        ip_sql = f""" select uid,ip_address from vh_user_login_behavior where uid in {user_id_list} """
    ip_df = pd.read_sql_query(ip_sql,conn_statistics)
    conn_statistics.close()
    nopool_conn("vh_data_statistics").dispose()
    return ip_df



#####登陆地址提取城市:
def process_ip_address(value):
    if type(value) == str:
        value = value.split("|")
        if len(value) == 1:
            res = value[0]
        elif len(value) == 2:
            res = f"{value[0]}{value[1]}"
        elif len(value) == 3 or len(value) == 4:
            res = f"{value[1]}{value[2]}"
        else:
            res = None
    else:
        res = value
    return res


#####获取注册来源列表
def get_reg_list():
    sql = f""" select `reg_from` from `vh_user_regfrom`"""
    conn_statistics = nopool_conn("vh_data_statistics").connect()
    dff = pd.read_sql_query(sql,conn_statistics).T
    conn_statistics.close()
    nopool_conn("vh_data_statistics").dispose()
    res_json = json.loads(dff.to_json(orient="records"))
    return res_json



#####获取ip列表
def get_ip_list():
    sql = f""" select `ip_address` from `vh_ipaddress_list`"""
    conn_statistics = nopool_conn("vh_data_statistics").connect()
    res_dff = pd.DataFrame([{"ip_address":"未知"}])
    dff = pd.read_sql_query(sql,conn_statistics)
    res_dff = res_dff.append(dff).reset_index(drop=True)
    conn_statistics.close()
    nopool_conn("vh_data_statistics").dispose()
    res_json = json.loads(res_dff.T.to_json(orient="records"))
    return res_json

#####获取来源和ip筛选项
def get_filter_list():
    ip_filter = get_ip_list()
    reg_filter = get_reg_list()
    res_json = {"ip_filter":ip_filter,"reg_filter":reg_filter}
    return {"error_code": 0, "error_msg": "请求成功", "status": "success" , "data":res_json}


####USER信息

def GetNewUsers(begin_time,over_time,source_type,mark,coupon_id,users_conn):
    try:
        b_time_str = f"{begin_time} 00:00:00"
        o_time_str = f"{over_time} 23:59:59"
        b_time = pd.to_datetime(b_time_str)
        o_time = pd.to_datetime(o_time_str)
        first_time = int(time.mktime(b_time.timetuple()))
        last_time = int(time.mktime(o_time.timetuple()))
        if source_type == "0":
            if mark == "":
                return {"data":[],"total":0}
            else:
                user_sql=f"""
                                    select
                                        uid,nickname,telephone_encrypt as 'telephone',FROM_UNIXTIME(created_time) 'created_time',case reg_from when 0 then '未知' when 1 then 'android' when 2 then 'ios' when 3 then '酒云网小程序' when 4 then 'h5' when 5 then 'PC' when 6 then '抖音小程序' when 7 then '后台添加' when 8 then '酒历小程序' when 9 then '公社小程序' when 10 then 'iPad' when 11 then '门店小程序' end as 'reg_from'
                                    from
                                        vh_user
                                    where
                                        tripartite_source = "{mark}"
                                        and
                                        created_time BETWEEN {first_time} and {last_time}
                                    order by `created_time`
                        """
        elif source_type == "1":
            if coupon_id == "":
                return {"data":[],"total":0}
            else:
                user_sql=f"""
                                select
                                    aa.uid,aa.nickname,aa.telephone_encrypt as 'telephone',FROM_UNIXTIME(aa.created_time) 'created_time',case aa.reg_from when 0 then '未知' when 1 then 'android' when 2 then 'ios' when 3 then '酒云网小程序' when 4 then 'h5' when 5 then 'PC' when 6 then '抖音小程序' when 7 then '后台添加' when 8 then '酒历小程序' when 9 then '公社小程序' when 10 then 'iPad' when 11 then '门店小程序' end as 'reg_from'
                                from
                                    vh_marketing.vh_coupon_issue AS a right join vh_user AS aa on a.uid = aa.uid
                                where
                                    a.coupon_id
                                    in
                                    (select
                                    coupon_id
                                    from
                                    vh_marketing.vh_coupon_package_details
                                    where
                                    coupon_package_id = {coupon_id})
                                and
                                    aa.created_time BETWEEN {first_time} and {last_time}
                                group by aa.`uid`
                                order by `created_time`
                                """
        user_df = pd.read_sql_query(user_sql,users_conn)
        if user_df.empty:
            return {"error_code":-1, "error_msg": "未发现相应用户信息!", "status": "fail", "data":[]}
        else:
            return user_df
    except Exception as e:
            return {'error_code': -1, 'error_msg': f'用户数据获取失败{e}!', 'status': 'fail', 'data': []}
        
        
def get_user_orders(user_id_list,table):
    if len(user_id_list) == 1:
        orders_sum_sql=f"select a.created_time,a.uid,aa.province_id,aa.city_id,a.payment_amount ,if(a.order_qty>1,1,order_qty) 'order_num' from {table} AS a left join vh_order_main AS aa on a.main_order_id = aa.id where a.uid = {user_id_list[0]} and a.sub_order_status in (1,2,3)"
    else:
        orders_sum_sql=f"select a.created_time,a.uid,aa.province_id,aa.city_id,a.payment_amount ,if(a.order_qty>1,1,order_qty) 'order_num' from {table} AS a left join vh_order_main AS aa on a.main_order_id = aa.id where a.uid in {user_id_list} and a.sub_order_status in (1,2,3)"
    if table in ("vh_flash_order","vh_tail_order", "vh_second_order"):
        if len(user_id_list) == 1:
            coupon_orders_sql=f"""
                select
                    a.uid,a.nickname,a.telephone_encrypt as 'telephone',aa.sub_order_no,aa.period,aa.order_qty,aa.payment_amount-aa.refund_money 'payment_amount',case when aa.sub_order_status=0 then '待支付'  when aa.sub_order_status=1 then '已支付'  when aa.sub_order_status=2 then '已发货' when aa.sub_order_status=3 then '已完成' when aa.sub_order_status=4 then '已取消' end as 'sub_order_status',FROM_UNIXTIME(aa.created_time) 'created_time',FROM_UNIXTIME(a.created_time) 'user_created_time',c.coupon_id 'coupon_id',bb.address 'address',bb.province_id 'province_id',bb.city_id 'city_id',bb.district_id 'district_id'
                from
                    vh_user AS a right join vh_orders.{table} AS aa on a.uid = aa.uid
                    left join vh_orders.vh_order_main AS bb on aa.main_order_id = bb.id
                    left join vh_marketing.vh_coupon_issue AS c on aa.coupon_id = c.id
                where
                    a.uid = {user_id_list[0]}
                    and 
                    aa.sub_order_status in (1,2,3)
                    and
                    aa.payment_amount-aa.refund_money > 0
                order by created_time,user_created_time
            """
        else:
            coupon_orders_sql=f"""
                select
                    a.uid,a.nickname,a.telephone_encrypt as 'telephone',aa.sub_order_no,aa.period,aa.order_qty,aa.payment_amount-aa.refund_money 'payment_amount',case when aa.sub_order_status=0 then '待支付'  when aa.sub_order_status=1 then '已支付'  when aa.sub_order_status=2 then '已发货' when aa.sub_order_status=3 then '已完成' when aa.sub_order_status=4 then '已取消' end as 'sub_order_status',FROM_UNIXTIME(aa.created_time) 'created_time',FROM_UNIXTIME(a.created_time) 'user_created_time',c.coupon_id 'coupon_id',bb.address 'address',bb.province_id 'province_id',bb.city_id 'city_id',bb.district_id 'district_id'
                from
                    vh_user AS a right join vh_orders.{table} AS aa on a.uid = aa.uid
                    left join vh_orders.vh_order_main AS bb on aa.main_order_id = bb.id
                    left join vh_marketing.vh_coupon_issue AS c on aa.coupon_id = c.id
                where
                    a.uid in {user_id_list}
                    and 
                    aa.sub_order_status in (1,2,3)
                    and
                    aa.payment_amount-aa.refund_money > 0
                order by created_time,user_created_time
                    """
    else:
        if len(user_id_list) == 1:
            coupon_orders_sql=f"""
                select
                    a.uid,a.nickname,a.telephone_encrypt as 'telephone',aa.sub_order_no,aa.period,aa.order_qty,aa.payment_amount-aa.refund_money 'payment_amount',case when aa.sub_order_status=0 then '待支付'  when aa.sub_order_status=1 then '已支付'  when aa.sub_order_status=2 then '已发货' when aa.sub_order_status=3 then '已完成' when aa.sub_order_status=4 then '已取消' end as 'sub_order_status',FROM_UNIXTIME(aa.created_time) 'created_time',FROM_UNIXTIME(a.created_time) 'user_created_time',null 'coupon_id',bb.address 'address',bb.province_id 'province_id',bb.city_id 'city_id',bb.district_id 'district_id'
                from
                    vh_user AS a right join vh_orders.{table} AS aa on a.uid = aa.uid
                    left join vh_orders.vh_order_main AS bb on aa.main_order_id = bb.id
                where
                    a.uid = {user_id_list[0]}
                    and 
                    aa.sub_order_status in (1,2,3)
                    and
                    aa.payment_amount-aa.refund_money > 0
                order by created_time,user_created_time
                """
        else:
            coupon_orders_sql=f"""
                select
                    a.uid,a.nickname,a.telephone_encrypt as 'telephone',aa.sub_order_no,aa.period,aa.order_qty,aa.payment_amount-aa.refund_money 'payment_amount',case when aa.sub_order_status=0 then '待支付'  when aa.sub_order_status=1 then '已支付'  when aa.sub_order_status=2 then '已发货' when aa.sub_order_status=3 then '已完成' when aa.sub_order_status=4 then '已取消' end as 'sub_order_status',FROM_UNIXTIME(aa.created_time) 'created_time',FROM_UNIXTIME(a.created_time) 'user_created_time',null 'coupon_id',bb.address 'address',bb.province_id 'province_id',bb.city_id 'city_id',bb.district_id 'district_id'
                from
                    vh_user AS a right join vh_orders.{table} AS aa on a.uid = aa.uid
                    left join vh_orders.vh_order_main AS bb on aa.main_order_id = bb.id
                where
                    a.uid in {user_id_list}
                    and 
                    aa.sub_order_status in (1,2,3)
                    and
                    aa.payment_amount-aa.refund_money > 0
                order by created_time,user_created_time
                """
    orders_conn = nopool_conn("vh_orders").connect()
    order_sum_dff = pd.read_sql_query(orders_sum_sql, orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    users_conn = nopool_conn("vh_user").connect()
    coupon_order_dff = pd.read_sql_query(coupon_orders_sql,users_conn)
    users_conn.close()
    nopool_conn("vh_user").dispose()
    return coupon_order_dff,order_sum_dff


####获取具体订单情况
def NewUsersOrders(begin_time,over_time,source_type,mark,coupon_id,ip_filter,reg_filter,table_type,page_args,users_conn):
    try:
        if source_type == "":
            return {"data":[],"total":0}
        user_data=GetNewUsers(begin_time,over_time,source_type,mark,coupon_id,users_conn) ###用户
        if type(user_data) != dict:
            user_data["created_time"] = user_data.created_time.astype(str)
            user_id_list = tuple(user_data.uid.tolist())
            ip_df = get_address(user_id_list)
            user_data = pd.merge(user_data,ip_df,how="left",on="uid")
            user_data["ip_address"] = user_data.ip_address.apply(lambda x:process_ip_address(x))
            user_data["ip_address"] = user_data.ip_address.fillna('未知')
            if len(ip_filter) == 0 and len(reg_filter) == 0:
                user_df = user_data.copy()
            elif len(ip_filter) != 0 and len(reg_filter) == 0:
                user_df = user_data[user_data.ip_address == ip_filter]
            elif len(ip_filter) == 0 and len(reg_filter) != 0:
                user_df = user_data[user_data.reg_from == reg_filter]
            elif len(ip_filter) != 0 and len(reg_filter) != 0:
                user_df = user_data[(user_data.reg_from == reg_filter) & (user_data.ip_address == ip_filter)]
            else:
                pass
            if user_df.empty:
                return {"data":[],"total":0}
            coupon_tabel_list=["vh_flash_order","vh_tail_order", "vh_second_order"] ###订单
            with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                get_user_orders_take = [executor.submit(get_user_orders,user_id_list,table) for table in coupon_tabel_list]
                coupon_order_dff = pd.DataFrame(columns = ['uid','nickname','province_id','city_id','telephone','sub_order_no','period','order_qty','payment_amount','sub_order_status','created_time','user_created_time','coupon_id','address'])
                order_sum_dff = pd.DataFrame(columns = ["uid",'province_id','city_id',"payment_amount","order_num"])
                for order_future in concurrent.futures.as_completed(get_user_orders_take):
                    coupon_order_dff = coupon_order_dff.append(order_future.result()[0]).reset_index(drop=True)
                    order_sum_dff = order_sum_dff.append(order_future.result()[1]).reset_index(drop=True)
            cross_order_dff,cross_sum_dff = get_user_orders(user_id_list,"vh_cross_order")
            main_orders = coupon_order_dff.append(cross_order_dff).reset_index(drop=True)
            main_order_sum = order_sum_dff.append(cross_sum_dff).reset_index(drop=True)
            if len(tuple(set(main_orders.uid.tolist()))) == 0:
                main_orders_address = main_orders.copy()
                main_orders_address["ip_address"] = None
            else:
                main_orders_address = get_address(tuple(set(main_orders.uid.tolist())))
            main_orders = pd.merge(main_orders,main_orders_address,how="left",on="uid")
            if len(tuple(set(main_order_sum.uid.tolist()))) == 0:
                main_orders_sum_address = main_order_sum.copy()
                main_orders_sum_address["ip_address"] = None
            else:
                main_orders_sum_address = get_address(tuple(set(main_order_sum.uid.tolist())))
            main_order_sum = pd.merge(main_order_sum,main_orders_sum_address,how="left",on="uid")
            main_orders["ip_address"] = main_orders.ip_address.apply(lambda x:process_ip_address(x))
            main_orders["ip_address"] = main_orders.ip_address.fillna('未知')
            main_order_sum["ip_address"] = main_order_sum.ip_address.apply(lambda x:process_ip_address(x))
            main_order_sum["ip_address"] = main_order_sum.ip_address.fillna('未知')
            if main_orders.empty or main_order_sum.empty:
                main_user_sum_df_show=pd.DataFrame(columns=['uid','nickname','telephone','created_time','reg_from','ip_address','order_num','payment_amount','province/city'])
                main_order_show=pd.DataFrame(columns=['uid','nickname','telephone','user_created_time','reg_from','ip_address','sub_order_no','period','product_category','title','order_qty','payment_amount','sub_order_status','created_time','coupon_id','address','province/city'])
                no_orders_user_df=user_df.copy()
                main_order_total = len(main_order_show)
                main_user_sum_total = len(main_user_sum_df_show)
                if table_type == "":
                    user_page,user_page_nums = page_args["0"]["page"],page_args["0"]["page_nums"]
                    no_orders_page,no_orders_page_nums = page_args["3"]["page"],page_args["3"]["page_nums"]
                    user_df_show,user_total = user_df[(int(user_page) * int(user_page_nums) - int(user_page_nums)):int(user_page) * int(user_page_nums)].reset_index(drop=True),len(user_df)
                    no_orders_user_df_show,no_orders_user_total = no_orders_user_df[(int(no_orders_page) * int(no_orders_page_nums) - int(no_orders_page_nums)):int(no_orders_page) * int(no_orders_page_nums)].reset_index(drop=True),len(no_orders_user_df)
                    neworder_json={
                        "users_json":{"data":json.loads(user_df_show.to_json(orient="records")),
                                      "total":user_total
                                     },
                        "orders_users_orders_sum_json": {"data":json.loads(main_user_sum_df_show.to_json(orient="records"))
                                                         ,"total":main_user_sum_total
                                                        },
                        "orders_users_orders_json": {"data":json.loads(main_order_show.to_json(orient="records")),
                                                     "total":main_order_total
                                                    },
                        "no_orders_users_json": {"data":json.loads(no_orders_user_df_show.to_json(orient="records")),
                                                 "total":no_orders_user_total
                                                }
                        }
                elif table_type == "0":
                    user_page,user_page_nums = page_args["0"]["page"],page_args["0"]["page_nums"]
                    user_df_show,user_total = user_df[(int(user_page) * int(user_page_nums) - int(user_page_nums)):int(user_page) * int(user_page_nums)].reset_index(drop=True),len(user_df)
                    neworder_json={"data":json.loads(user_df_show.to_json(orient="records")),
                                   "total":user_total
                                  }#####注册新用户
                elif table_type == "1":
                    neworder_json={"data":json.loads(main_user_sum_df_show.to_json(orient="records")),
                                   "total":main_user_sum_total
                                  }#####新用户新消费情况
                elif table_type == "2":            
                    neworder_json={"data":json.loads(main_order_show.to_json(orient="records")),
                                   "total":main_order_total
                                  }#####新用户消费具体明细
                elif table_type == "3":
                    no_orders_page,no_orders_page_nums = page_args["3"]["page"],page_args["3"]["page_nums"]
                    no_orders_user_df_show,no_orders_user_total = no_orders_user_df[(int(no_orders_page) * int(no_orders_page_nums) - int(no_orders_page_nums)):int(no_orders_page) * int(no_orders_page_nums)].reset_index(drop=True),len(no_orders_user_df)
                    neworder_json={"data":json.loads(no_orders_user_df_show.to_json(orient="records")),
                                   "total":no_orders_user_total
                                  }######注册未消费用户
                else:
                    pass
                return neworder_json
            else:
                main_orders["created_time"] = main_orders.created_time.astype(str)
                main_orders["user_created_time"] = main_orders.user_created_time.astype(str)
                province_id_list=tuple(main_orders.province_id.tolist())
                city_id_list = tuple(main_orders.city_id.tolist())
                district_id_list = tuple(main_orders.district_id.tolist())
                province_name = get_regional_name(province_id_list,users_conn)
                city_name = get_regional_name(city_id_list,users_conn)
                district_name = get_regional_name(district_id_list,users_conn)
                main_orders_province=pd.merge(main_orders,province_name,how="left",left_on="province_id",right_on="id")
                main_orders_city = pd.merge(main_orders_province,city_name,how="left",left_on="city_id",right_on="id")
                main_orders_district = pd.merge(main_orders_city,district_name,how="left",left_on="district_id",right_on="id")
                main_orders_district.drop(["province_id","city_id","district_id","id_x","id_y","id"],axis=1,inplace=True)
                main_orders_district.columns=["uid","nickname","telephone","sub_order_no","period","order_qty","payment_amount","sub_order_status","created_time","user_created_time","coupon_id","address","ip_address","province","city","district"]
                period_tup=tuple(set(main_orders_district.period.tolist()))
                commodities_pool=["vh_periods_cross","vh_periods_flash","vh_periods_second","vh_periods_leftover"]
                with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                    commodities_tasks = [executor.submit(get_commodities,period_tup,commoditie) for commoditie in commodities_pool]
                    commodities_main_df = pd.DataFrame(columns=["id","title","product_category"])
                    for future in concurrent.futures.as_completed(commodities_tasks):
                        commodities_main_df=commodities_main_df.append(future.result()).reset_index(drop=True) 
                main_order=pd.merge(main_orders_district,commodities_main_df,how="left",left_on="period",right_on="id")
                main_order.drop("id",axis=1,inplace=True)
                main_order = pd.merge(main_order,user_df[["uid","reg_from"]],how="inner",on="uid")
                main_order = main_order[['uid', 'nickname','telephone','user_created_time','reg_from','ip_address','sub_order_no', 'period', 'product_category', 'title', 'order_qty','payment_amount', 'sub_order_status', 'created_time', 'coupon_id','province', 'city']]
                main_order = main_order.sort_values(by=["user_created_time","created_time"],ascending=False)
                main_order_sum = main_order_sum.sort_values(by="created_time",ascending=False)
                main_sum_dff = main_order_sum.groupby(by="uid",as_index=False).agg({"province_id":"first","city_id":"first","payment_amount":sum,"order_num":sum,"ip_address":"first"})
                main_user_sum_df=pd.merge(user_df,main_sum_dff,how="inner",left_on="uid",right_on="uid").drop("ip_address_y",axis=1).rename(columns={"ip_address_x":"ip_address"})
                main_user_sum_df = main_user_sum_df[["uid","nickname","telephone","created_time","reg_from","ip_address","order_num","payment_amount","province_id","city_id"]]
                province_id_list=tuple(main_user_sum_df.province_id.tolist())
                city_id_list = tuple(main_user_sum_df.city_id.tolist())
                province_name = get_regional_name(province_id_list,users_conn)
                city_name = get_regional_name(city_id_list,users_conn)
                main_user_sum_df = pd.merge(main_user_sum_df,province_name,how="left",left_on="province_id",right_on="id").merge(city_name,how="left",left_on="city_id",right_on="id")
                main_user_sum_df["province/city"] = main_user_sum_df.name_x + "/" + main_user_sum_df.name_y
                main_user_sum_df = main_user_sum_df.drop(["province_id","city_id","id_x","name_x","id_y","name_y"],axis = 1)
                main_user_sum_df = main_user_sum_df.sort_values(by="created_time",ascending=False)
                orders_uid_list=main_user_sum_df.uid.tolist()
                orders_uid=tuple(orders_uid_list)
                no_orders_user_df=user_df[user_df.uid.apply(lambda x:x not in orders_uid)]
                main_order_total = len(main_order)
                if table_type == "":
                    user_page,user_page_nums = page_args["0"]["page"],page_args["0"]["page_nums"]
                    main_user_sum_page,main_user_sum_page_nums = page_args["1"]["page"],page_args["1"]["page_nums"]
                    main_order_page,main_order_page_nums = page_args["2"]["page"],page_args["2"]["page_nums"]
                    no_orders_page,no_orders_page_nums = page_args["3"]["page"],page_args["3"]["page_nums"]
                    user_df_show,user_total = user_df[(int(user_page) * int(user_page_nums) - int(user_page_nums)):int(user_page) * int(user_page_nums)].reset_index(drop=True),len(user_df)
                    main_user_sum_df_show,main_user_sum_total = main_user_sum_df[(int(main_user_sum_page) * int(main_user_sum_page_nums) - int(main_user_sum_page_nums)):int(main_user_sum_page) * int(main_user_sum_page_nums)].reset_index(drop=True),len(main_user_sum_df)
                    main_order_show,main_order_total = main_order[(int(main_order_page) * int(main_order_page_nums) - int(main_order_page_nums)):int(main_order_page) * int(main_order_page_nums)].reset_index(drop=True),len(main_order)
                    no_orders_user_df_show,no_orders_user_total = no_orders_user_df[(int(no_orders_page) * int(no_orders_page_nums) - int(no_orders_page_nums)):int(no_orders_page) * int(no_orders_page_nums)].reset_index(drop=True),len(no_orders_user_df)
                    neworder_json={
                        "users_json":{"data":json.loads(user_df_show.to_json(orient="records")),
                                      "total":user_total
                                     },
                        "orders_users_orders_sum_json": {"data":json.loads(main_user_sum_df_show.to_json(orient="records"))
                                                         ,"total":main_user_sum_total
                                                        },
                        "orders_users_orders_json": {"data":json.loads(main_order_show.to_json(orient="records")),
                                                     "total":main_order_total
                                                    },
                        "no_orders_users_json": {"data":json.loads(no_orders_user_df_show.to_json(orient="records")),
                                                 "total":no_orders_user_total
                                                }
                        }
                elif table_type == "0":
                    user_page,user_page_nums = page_args["0"]["page"],page_args["0"]["page_nums"]
                    user_df_show,user_total = user_df[(int(user_page) * int(user_page_nums) - int(user_page_nums)):int(user_page) * int(user_page_nums)].reset_index(drop=True),len(user_df)
                    neworder_json={"data":json.loads(user_df_show.to_json(orient="records")),
                                   "total":user_total
                                  }#####注册新用户
                elif table_type == "1":
                    main_user_sum_page,main_user_sum_page_nums = page_args["1"]["page"],page_args["1"]["page_nums"]
                    main_user_sum_df_show,main_user_sum_total = main_user_sum_df[(int(main_user_sum_page) * int(main_user_sum_page_nums) - int(main_user_sum_page_nums)):int(main_user_sum_page) * int(main_user_sum_page_nums)].reset_index(drop=True),len(main_user_sum_df)
                    neworder_json={"data":json.loads(main_user_sum_df_show.to_json(orient="records")),
                                   "total":main_user_sum_total
                                  }#####新用户新消费情况
                elif table_type == "2":
                    main_order_page,main_order_page_nums = page_args["2"]["page"],page_args["2"]["page_nums"]
                    main_order_show,main_order_total = main_order[(int(main_order_page) * int(main_order_page_nums) - int(main_order_page_nums)):int(main_order_page) * int(main_order_page_nums)].reset_index(drop=True),len(main_order)
                    neworder_json={"data":json.loads(main_order_show.to_json(orient="records")),
                                   "total":main_order_total
                                  }#####新用户消费具体明细
                elif table_type == "3":
                    no_orders_page,no_orders_page_nums = page_args["3"]["page"],page_args["3"]["page_nums"]
                    no_orders_user_df_show,no_orders_user_total = no_orders_user_df[(int(no_orders_page) * int(no_orders_page_nums) - int(no_orders_page_nums)):int(no_orders_page) * int(no_orders_page_nums)].reset_index(drop=True),len(no_orders_user_df)
                    neworder_json={"data":json.loads(no_orders_user_df_show.to_json(orient="records")),
                                   "total":no_orders_user_total
                                  }######注册未消费用户
                else:
                    pass
            return neworder_json
        else:
            return user_data
    except Exception as e:
            return {'error_code': -1, 'error_msg': f'广告投放数据获取失败{e}!', 'status': 'fail', 'data': []}