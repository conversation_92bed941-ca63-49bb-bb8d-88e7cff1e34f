from datetime import datetime
from app.CommonLibraries.LogRecord import record_error
from app.CommonLibraries.getConfig import get_config
from app.ScheduleTask.OnsaleCount import handler_onsale_count
from app.ScheduleTask.count_area_day import handler_count_area
from app.ScheduleTask.count_daily_process_day import handler_daily_process
from app.ScheduleTask.count_purchase_day import handler_purchase
from app.ScheduleTask.count_register_daily_day import handler_register_daily
from app.ScheduleTask.count_register_monthly_day import handler_register_monthly
from app.ScheduleTask.count_year_sales_day import handler_year_sales
from app.ScheduleTask.operation_acess_day import handler_operation_access
from app.ScheduleTask.count_new_user_transform_statistics import get_new_user_transform_statistics
from app.ScheduleTask.vh_user_login_behavior_count import get_ip_address_begin
from app.ScheduleTask.ip_regfrom_record import ip_reg_handler_main
from app.ScheduleTask.buyer_dialect_record import handler_buyer_dialect_main
from app.ScheduleTask.count_handler_merchant_second_daily import handler_merchant_second_daily_process
from app.ScheduleTask.take_vest_comment import handler_vest_comment_config_process

rm_config,rr_config = get_config("db.data.write.v3_all", "vinehoo.accounts"),get_config("db.data.v3_all", "vinehoo.accounts")
rm_host,rr_host = rm_config['host'],rr_config['host']
rm_port,rr_port = rm_config['port'],rr_config['port']
rm_user,rr_user = rm_config['user'],rr_config['user']
rm_password,rr_password = rm_config['password'],rr_config['password']


# {
#     "host":"*************",
#     "port":3306,
#     "user":"vinehoodev",
#     "password":"vinehoo123"
# }
# {
#     "host":"rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com",
#     "port":3306,
#     "user":"wy_bqy",
#     "password":"#Fv!DnU!BDhBfwFKH9Ev55ww"
# }
# mysql_host = "rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com"
# mysql_port = 3306
# mysql_user = "wy_wh"
# mysql_password = "1HGP4o3OpYk9lFfU"

def start_map_analysis_test():
    # 日销售进度统计
    try:
        print("年销售统计-----------------start")
        g = handler_year_sales(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if g == -1:
            record_error("年销售统计", 'error')
        print("年销售统计-----------------end")
        # print("日销售进度统计-----------------start")
        # c = handler_daily_process(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        # if c == -1:
        #     record_error("日销售进度统计", 'error')
        # print("日销售进度统计-----------------end")
    except Exception as e:
        print(e)
        pass
    print(f"日销售进度统计-ok")

def start_map_analysis():
    # 运营达标率统计
    try:
        print("运营达标率统计-----------------start")
        a = handler_operation_access(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if a == -1:
            record_error("运营达标率统计", 'error')
        print("运营达标率统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 区域销售统计
    try:
        print("区域销售统计-----------------start")
        b = handler_count_area(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if b == -1:
            record_error("区域销售统计", 'error')
        print("区域销售统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 日销售进度统计
    try:
        print("日销售进度统计-----------------start")
        c = handler_daily_process(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if c == -1:
            record_error("日销售进度统计", 'error')
        print("日销售进度统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 采购统计
    try:
        print("采购统计-----------------start")
        d = handler_purchase(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if d == -1:
            record_error("采购统计", 'error')
        print("采购统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 注册用户日统计
    try:
        print("注册用户日统计-----------------start")
        e = handler_register_daily(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if e == -1:
            record_error("注册用户日统计", 'error')
        print("注册用户日统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 注册用户月统计
    try:
        if datetime.now().day == 1:
            print("注册用户月统计-----------------start")
            f = handler_register_monthly(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
            if f == -1:
                record_error("注册用户月统计", 'error')
            print("注册用户月统计-----------------end")
        else:
            pass
    except Exception as e:
        print(e)
        pass
    # 年销售统计
    try:
        print("年销售统计-----------------start")
        g = handler_year_sales(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if g == -1:
            record_error("年销售统计", 'error')
        print("年销售统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 商品在售统计
    try:
        print("商品在售统计-----------------start")
        g = handler_onsale_count(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if g == -1:
            record_error("商品在售统计", 'error')
        print("商品在售统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 新用户转化统计
    try:
        print("新用户转化统计-----------------start")
        g = get_new_user_transform_statistics()
        if g == -1:
            record_error("新用户转化统计", 'error')
        print("新用户转化统计-----------------end")
    except Exception as e:
        print(e)
        pass
    # 用户最后登陆ip统计
    try:
        print("用户最后登陆ip记录-----------------start")
        g = get_ip_address_begin()
        if g == -1:
            record_error("用户最后登陆ip记录", 'error')
        print("用户最后登陆ip记录-----------------end")
    except Exception as e:
        print(e)
        pass
    # 广告投放来源ip及用户来源列表更新
    try:
        print("广告投放来源ip及用户来源列表更新-----------------start")
        g = ip_reg_handler_main(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if g == -1:
            record_error("广告投放来源ip及用户来源列表更新", 'error')
        print("广告投放来源ip及用户来源列表更新-----------------end")
    except Exception as e:
        print(e)
        pass
    try:
        print("销售即时统计采购及销售数据来源表更新-----------------start")
        g = handler_buyer_dialect_main(rm_host, rm_port, rm_user, rm_password)
        if g == -1:
            record_error("销售即时统计采购及销售数据来源表更新", 'error')
        print("销售即时统计采购及销售数据来源表更新-----------------end")
    except Exception as e:
        print(e)
        pass
    try:
        print("商家秒发用户数据更新-----------------start")
        g = handler_merchant_second_daily_process(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        if g == -1:
            record_error("商家秒发用户数据更新-", 'error')
        print("商家秒发用户数据更新------------------end")
    except Exception as e:
        print(e)
        pass
    try:
        print("马甲评论数据更新-----------------start")
        g = handler_vest_comment_config_process()
        if g == -1:
            record_error("马甲评论数据更新-", 'error')
        print("马甲评论数据更新------------------end")
    except Exception as e:
        print(e)
        pass

###############输出更新数据日期######################
    d = datetime.now()
    print(f"更新数据日期:{d.year}年{d.month}月{d.day}日")

