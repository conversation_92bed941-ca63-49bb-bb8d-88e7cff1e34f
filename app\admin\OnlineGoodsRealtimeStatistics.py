import json
import calendar
import warnings
import requests
import concurrent
import pandas as pd
from tenacity import retry
from config import ChannelTypeConfig
from sqlalchemy import create_engine
from datetime import datetime
from app.DButils.MysqlHelper import conn_str

response = requests.session()
warnings.filterwarnings("ignore")


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def get_sell_online(year,month):
    month_start = f"{year}-0{month}-01"
    month_end = f"{year}-0{month}-31"
    sql = f""" SELECT day,onslae_count_flash as 'flash_sale',onslae_count_cross as 'cross_sale',onslae_count_second as 'second_sale',onslae_count_left_over as 'tail_sale' FROM vh_data_statistics.vh_onsale_periods_count where `day` >= '{month_start}' and `day` <= '{month_end}' """
    statistics_conn = nopool_conn("vh_data_statistics").connect()
    df_onsale_result = pd.read_sql(sql, statistics_conn)
    statistics_conn.close()
    nopool_conn("vh_data_statistics").dispose()
    return df_onsale_result

def get_month_dates(hypothesis_time):
    """
    获取当月的所有日期
    :param hypothesis_time:假设时间
    """
    start = datetime(hypothesis_time.year, hypothesis_time.month, 1)
    end = datetime(hypothesis_time.year, hypothesis_time.month, calendar.monthrange(hypothesis_time.year, hypothesis_time.month)[1])
    now = datetime.now()
    dates = []
    if hypothesis_time.year != now.year or hypothesis_time.month != now.month:
        for day in range(start.day,end.day+1):
            if len(str(hypothesis_time.month)) != 1:
                if len(str(day)) != 1:
                    date = f"{hypothesis_time.year}-{hypothesis_time.month}-{day}"
                elif len(str(day)) == 1:
                    date = f"{hypothesis_time.year}-{hypothesis_time.month}-0{day}"
            else:
                if len(str(day)) != 1:
                    date = f"{hypothesis_time.year}-0{hypothesis_time.month}-{day}"
                elif len(str(day)) == 1:
                    date = f"{hypothesis_time.year}-0{hypothesis_time.month}-0{day}"
            dates.append(date)
    else:
        for day in range(start.day,now.day+1):
            if len(str(hypothesis_time.month)) != 1:
                if len(str(day)) != 1:
                    date = f"{hypothesis_time.year}-{hypothesis_time.month}-{day}"
                elif len(str(day)) == 1:
                    date = f"{hypothesis_time.year}-{hypothesis_time.month}-0{day}"
            else:
                if len(str(day)) != 1:
                    date = f"{hypothesis_time.year}-0{hypothesis_time.month}-{day}"
                elif len(str(day)) == 1:
                    date = f"{hypothesis_time.year}-0{hypothesis_time.month}-0{day}"
            dates.append(date)
    return dates


def get_now_time() -> tuple:
    """
    获取当前时间
    :return:
    """
    return datetime.now()


def get_month_so_stamp(hypothesis_time):
    """
    获取当月始末时间戳
    :param hypothesis_time:假设时间
    :return:
    """
    start_stamp = datetime(hypothesis_time.year, hypothesis_time.month, 1).timestamp()
    end = datetime(hypothesis_time.year, hypothesis_time.month, calendar.monthrange(hypothesis_time.year, hypothesis_time.month)[1])
    end_stamp = datetime.strptime(f"{end.year}-{end.month}-{end.day} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp()
    return int(start_stamp),int(end_stamp)


def get_day_so_stamp(day):
    """
    获取当日始末时间戳
    :param hypothesis_time:假设时间
    :return:
    """
    start_stamp = datetime.strptime(f"{day} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()
    end_stamp = datetime.strptime(f"{day} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp()
    return int(start_stamp),int(end_stamp)


def get_week_day(date):
    """
    根据日期获取星期
    :param date:
    :return:
    """
    date = datetime.strptime(date, '%Y-%m-%d')
    week_day_dict = {
        0: '星期一',
        1: '星期二',
        2: '星期三',
        3: '星期四',
        4: '星期五',
        5: '星期六',
        6: '星期天',
    }
    day = date.weekday()
    return week_day_dict[day]


def get_onsale_online(table:str,monthsatrt:int,monthend:int) -> object:
    """
    获取线上上新商品(渠道)期数
    :param table:表名
    :param monthsatrt:当月开始时间戳
    :param monthsatrt:当月结束时间戳
    :param is_channel:是否为渠道(1 or 2)
    :return:
    """
    if table == "vh_periods_flash":
        sql = f""" SELECT '{table}' as 'channel',id as 'period',DATE_FORMAT(FROM_UNIXTIME(onsale_time),'%%Y-%%m-%%d') 'day'  FROM {table}
                    WHERE onsale_time BETWEEN {monthsatrt} AND {monthend} AND is_channel = 0 """
    else:
        sql = f""" SELECT '{table}' as 'channel',id as 'period',DATE_FORMAT(FROM_UNIXTIME(onsale_time),'%%Y-%%m-%%d') 'day'  FROM {table}
                    WHERE onsale_time BETWEEN {monthsatrt} AND {monthend} """
    commodities_conn = nopool_conn("vh_commodities").connect()
    dff = pd.read_sql_query(sql,commodities_conn)
    commodities_conn.close()
    nopool_conn("vh_commodities").dispose()
    return dff


def get_orders(date:str,channel:str,goods_ids:tuple) -> object:
    """
    获取销售数据
    :param table:表名
    :param goods_id_tuple:商品id
    :return:
    """
    s_time = int(datetime.strptime(f"{date} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp())
    e_time = int(datetime.strptime(f"{date} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
    if set(goods_ids) == {0}:
        dff = pd.DataFrame(columns=["day","sales_money","sales_orders"])
        return dff
    elif len(goods_ids) == 1:
        sql = f"""SELECT '{date}' as 'day',CASE order_type WHEN 0 THEN 'flash' WHEN 1 THEN 'second' WHEN 2 THEN 'cross' WHEN 3 THEN 'tail' END as 'channel',sum(payment_amount) as 'sales_money',count(1) as  'sales_orders' 
                        FROM {channel} WHERE created_time BETWEEN {s_time} AND {e_time} AND period = {goods_ids[0]} AND sub_order_status in (1,2,3) AND payment_amount - refund_money != 0 GROUP BY `day`,`channel`"""
    else:
        sql = f"""SELECT '{date}' as 'day',CASE order_type WHEN 0 THEN 'flash' WHEN 1 THEN 'second' WHEN 2 THEN 'cross' WHEN 3 THEN 'tail' END as 'channel',sum(payment_amount) as 'sales_money',count(1) as  'sales_orders' 
                        FROM {channel} WHERE created_time BETWEEN {s_time} AND {e_time} AND period IN {goods_ids} AND sub_order_status in (1,2,3) AND payment_amount - refund_money != 0 GROUP BY `day`,`channel`"""
    orders_conn = nopool_conn("vh_orders").connect()
    dff = pd.read_sql_query(sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return dff


def get_flash_onsale_channel(monthsatrt:int,monthend:int) -> object:
    """
    获取闪购渠道上新商品情况
    :param monthsatrt:当月开始时间戳
    :param monthend:当月结束时间戳
    :param out_type:输出类型(1-onsale,2-sell)
    :return:
    """
    sql = f""" SELECT id 'period',DATE_FORMAT(FROM_UNIXTIME(onsale_time),'%%Y-%%m-%%d') 'day'  FROM vh_periods_flash
                 WHERE onsale_time BETWEEN {monthsatrt} AND {monthend} AND is_channel = 1 """
    commodities_conn = nopool_conn("vh_commodities").connect()
    dff = pd.read_sql_query(sql,commodities_conn)
    commodities_conn.close()
    nopool_conn("vh_commodities").dispose()
    return dff


def get_online_goods_realtime_statistics(month:str, period_types:list) -> object:
    """
    获取线上上新商品
    :param month:月份
    :param period_types:显示表头
    :return:
    """
    try:
        Cg = ChannelTypeConfig.channel_type_config
        hypothesis_time = datetime.strptime(f"{month}-01","%Y-%m-%d")
        start_stamp,end_stamp = get_month_so_stamp(hypothesis_time)
        goods_tables = [Cg[period_type][1] for period_type in period_types]
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            #获取线上上新、在售商品
            new_goods_take = [executor.submit(get_onsale_online,goods_table,start_stamp,end_stamp) for goods_table in goods_tables]
            df_goods_new = pd.DataFrame(columns=["channel","period","day"])
            for new_goods_future in concurrent.futures.as_completed(new_goods_take):
                df_goods_new = df_goods_new.append(new_goods_future.result()).reset_index(drop=True)
            df_flash_onsale_channel = get_flash_onsale_channel(start_stamp,end_stamp)
            hypothesis_now_date = f"{get_now_time().year}-{get_now_time().month}-{get_now_time().day}"
            df_goods_new = df_goods_new[df_goods_new.day.apply(lambda x:datetime.strptime(x,"%Y-%m-%d") <= datetime.strptime(hypothesis_now_date,"%Y-%m-%d"))]
        return df_goods_new,df_flash_onsale_channel,hypothesis_time
    except Exception as e:
        return f"商品信息获取失败:{e}"
    

def get_one_order(dates:list,channel:str,new_periods_dict:dict):
    with concurrent.futures.ThreadPoolExecutor(max_workers=100) as executor:  #####最大连接数1600
        order_take = [executor.submit(get_orders,date,channel,new_periods_dict[date]['period']) for date in dates]
        order_df = pd.DataFrame(columns = ["day","channel","sales_money","sales_orders"])
        for order_future in concurrent.futures.as_completed(order_take):
            order_df = order_df.append(order_future.result()).reset_index(drop=True)
    return order_df


def Correspondingly_orders(df_list:list,period_types:list):
    Cg = ChannelTypeConfig.channel_type_config
    order_tables = [Cg[period_type][0] for period_type in period_types]
    if len(df_list) == 1:
        df_goods_new = df_list[0]
        df_goods_new_periods = df_goods_new[["period","day"]].groupby(by="day",as_index=False).agg(
        {
            "period":list
        }
        )
        df_goods_new_periods["period"] = df_goods_new_periods.period.apply(lambda x:tuple(set(x)))
        df_goods_new_periods = df_goods_new_periods.T
        df_goods_new_periods.columns = df_goods_new_periods.iloc[0].tolist()
        df_goods_new_periods.drop(index="day", axis=0, inplace=True)
        new_periods_dict = df_goods_new_periods.to_dict()
        dates = list(new_periods_dict.keys())
        with concurrent.futures.ThreadPoolExecutor(max_workers=7) as executor:
            order_takes = [executor.submit(get_one_order,dates,channel,new_periods_dict) for channel in order_tables]
            order_dff = pd.DataFrame(columns = ["day","channel","sales_money","sales_orders"])
            for order_futures in concurrent.futures.as_completed(order_takes):
                order_dff = order_dff.append(order_futures.result()).reset_index(drop=True)
            return order_dff
    elif len(df_list) == 2:
        df_goods_new,df_flash_onsale_channel = df_list
        df_goods_new_periods = df_goods_new[["period","day"]].groupby(by="day",as_index=False).agg(
        {
            "period":list
        }
        )
        df_goods_new_periods["period"] = df_goods_new_periods.period.apply(lambda x:tuple(set(x)))
        df_goods_new_periods = df_goods_new_periods.T
        df_goods_new_periods.columns = df_goods_new_periods.iloc[0].tolist()
        df_goods_new_periods.drop(index="day", axis=0, inplace=True)
        new_periods_dict = df_goods_new_periods.to_dict()
        dates = list(new_periods_dict.keys())
        with concurrent.futures.ThreadPoolExecutor(max_workers=7) as executor:
            order_takes = [executor.submit(get_one_order,dates,channel,new_periods_dict) for channel in order_tables]
            order_dff = pd.DataFrame(columns = ["day","channel","sales_money","sales_orders"])
            for order_futures in concurrent.futures.as_completed(order_takes):
                order_dff = order_dff.append(order_futures.result()).reset_index(drop=True)
        df_flash_channel_new_periods = df_flash_onsale_channel[["period","day"]].groupby(by="day",as_index=False).agg(
        {
        "period":list
        }
        )
        df_flash_channel_new_periods["period"] = df_flash_channel_new_periods.period.apply(lambda x:tuple(set(x)))
        df_flash_channel_new_periods = df_flash_channel_new_periods.T
        df_flash_channel_new_periods.columns = df_flash_channel_new_periods.iloc[0].tolist()
        df_flash_channel_new_periods.drop(index="day", axis=0, inplace=True)
        channel_new_periods_dict = df_flash_channel_new_periods.to_dict()
        dates_channel = list(channel_new_periods_dict.keys())
        channel_order_dff = get_one_order(dates_channel,"vh_flash_order",channel_new_periods_dict)
    return order_dff,channel_order_dff


@retry
def admin_function(count_key:str,month:str):
    if count_key == "day":
        period_types = [1,2,3,4]
        df_goods_new,df_flash_onsale_channel,hypothesis_time = get_online_goods_realtime_statistics(month, period_types)
        df_dates = pd.DataFrame({"day":get_month_dates(hypothesis_time)})
        if df_goods_new.empty:
            df_goods_new = df_dates.copy()
            df_goods_new["period"] = 0
            df_flash_new,df_cross_new,df_second_new,df_tail_new = df_dates.copy(),df_dates.copy(),df_dates.copy(),df_dates.copy()
            df_flash_new["flash_news"],df_cross_new["cross_news"],df_second_new["second_news"],df_tail_new["tail_news"] = 0,0,0,0
        else:
            df_flash_new,df_cross_new,df_second_new,df_tail_new = df_goods_new[df_goods_new.channel == "vh_periods_flash"],df_goods_new[df_goods_new.channel == "vh_periods_cross"],df_goods_new[df_goods_new.channel == "vh_periods_second"],df_goods_new[df_goods_new.channel == "vh_periods_leftover"]
            if df_flash_new.empty or df_cross_new.empty or df_second_new.empty or df_tail_new.empty:
                if df_flash_new.empty:
                    df_flash_new = df_dates.copy()
                    df_flash_new["flash_news"] = 0
                else:
                    df_flash_new = df_flash_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"flash_news"})
                if df_cross_new.empty:
                    df_cross_new = df_dates.copy()
                    df_cross_new["cross_news"] = 0
                else:
                    df_cross_new = df_cross_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"cross_news"})
                if df_second_new.empty:
                    df_second_new = df_dates.copy()
                    df_second_new["second_news"] = 0
                else:
                    df_second_new = df_second_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"second_news"})
                if df_tail_new.empty:
                    df_tail_new = df_dates.copy()
                    df_tail_new["tail_news"] = 0
                else:
                    df_tail_new = df_tail_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"tail_news"})
            else:
                df_flash_new,df_cross_new,df_second_new,df_tail_new = df_flash_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"flash_news"}),df_cross_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"cross_news"}),df_second_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"second_news"}),df_tail_new.groupby(by=["day","channel"],as_index=False).agg({"period":"count"}).drop("channel",axis=1).rename(columns = {"period":"tail_news"})           
        if df_flash_onsale_channel.empty:
            df_flash_onsale_channel = df_dates.copy()
            df_flash_onsale_channel["period"] = 0
            df_orders_new,df_channel_orders = Correspondingly_orders([df_goods_new,df_flash_onsale_channel],period_types)
            df_channel_orders = df_channel_orders.drop("channel",axis=1).groupby(by="day",as_index=False).agg({"sales_money":sum,"sales_orders":sum}).rename(columns={"sales_money":"channel_sales","sales_orders":"channel_orders"})
            df_orders_new = df_orders_new.groupby(by=["day","channel"],as_index=False).agg({"sales_money":sum,"sales_orders":sum})
            df_flash_orders,df_cross_orders,df_second_orders,df_tail_orders = df_orders_new[df_orders_new.channel == "flash"].drop("channel",axis=1).rename(columns={"sales_money":"flash_sales","sales_orders":"flash_orders"}),df_orders_new[df_orders_new.channel == "cross"].drop("channel",axis=1).rename(columns={"sales_money":"cross_sales","sales_orders":"cross_orders"}),df_orders_new[df_orders_new.channel == "second"].drop("channel",axis=1).rename(columns={"sales_money":"second_sales","sales_orders":"second_orders"}),df_orders_new[df_orders_new.channel == "tail"].drop("channel",axis=1).rename(columns={"sales_money":"tail_sales","sales_orders":"tail_orders"})
            df_flash_onsale_channel = df_flash_onsale_channel.rename(columns = {"period":"channel_news"})
            df_flash_onsale_channel["channel_news"] = 0
        else:
            df_orders_new,df_channel_orders = Correspondingly_orders([df_goods_new,df_flash_onsale_channel],period_types)
            df_channel_orders = df_channel_orders.drop("channel",axis=1).groupby(by="day",as_index=False).agg({"sales_money":sum,"sales_orders":sum}).rename(columns={"sales_money":"channel_sales","sales_orders":"channel_orders"})
            df_orders_new = df_orders_new.groupby(by=["day","channel"],as_index=False).agg({"sales_money":sum,"sales_orders":sum})
            df_flash_orders,df_cross_orders,df_second_orders,df_tail_orders = df_orders_new[df_orders_new.channel == "flash"].drop("channel",axis=1).rename(columns={"sales_money":"flash_sales","sales_orders":"flash_orders"}),df_orders_new[df_orders_new.channel == "cross"].drop("channel",axis=1).rename(columns={"sales_money":"cross_sales","sales_orders":"cross_orders"}),df_orders_new[df_orders_new.channel == "second"].drop("channel",axis=1).rename(columns={"sales_money":"second_sales","sales_orders":"second_orders"}),df_orders_new[df_orders_new.channel == "tail"].drop("channel",axis=1).rename(columns={"sales_money":"tail_sales","sales_orders":"tail_orders"})
            df_flash_onsale_channel = df_flash_onsale_channel.rename(columns = {"period":"channel_news"}).groupby(by="day",as_index=False).agg({"channel_news":"count"})
        df_goods_main = pd.merge(df_dates,df_flash_new,how="left").merge(df_flash_orders,how="left").merge(df_cross_new,how="left").merge(df_cross_orders,how="left").merge(df_second_new,how="left").merge(df_second_orders,how="left").merge(df_tail_new,how="left").merge(df_tail_orders,how="left").merge(df_flash_onsale_channel,how="left").merge(df_channel_orders,how="left")
        df_sell = get_sell_online(hypothesis_time.year,hypothesis_time.month)
        if df_sell.empty is False:
            df_goods_main = pd.merge(df_goods_main,df_sell, how='left')
        else:
            df_goods_main['flash_sale'] = 0
            df_goods_main['second_sale'] = 0
            df_goods_main['cross_sale'] = 0
            df_goods_main['tail_sale'] = 0
        df_goods_main = df_goods_main.fillna(0)
        df_goods_main['week'] = df_goods_main.day.apply(lambda x:get_week_day(x))
        df_goods_main = df_goods_main.rename(columns={'day':'date'})
        res_json = json.loads(df_goods_main.to_json(orient='records'))
        return res_json


