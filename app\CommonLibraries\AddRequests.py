import requests

session = requests.session()


def add_requests(req_type, url, body):
    """
    新增requests
    :return:
    """
    if req_type == 'post':
        headers = {
            "Content-Type": "application/json"
        }
        response = session.post(url=url, data=body, headers=headers)
        return response.json()
    elif req_type == 'get':
        headers = {
            "vinehoo-client": "py3-es-synchronous",
            "vinehoo-client-version": "1.0.0"
        }
        response = session.get(url=url, headers=headers)
        return response.json()
