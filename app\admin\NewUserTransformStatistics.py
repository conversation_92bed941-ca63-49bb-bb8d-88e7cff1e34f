import json
import warnings
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str
from app.ScheduleTask.count_new_user_transform_statistics import get_new_user_transform_massage

warnings.filterwarnings("ignore")


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine

def get_cur_day():
    cur_day = (datetime.now()).strftime("%Y-%m-%d")
    return cur_day


def get_cur_day():
    cur_day = (datetime.now()).strftime("%Y-%m-%d")
    return cur_day


def get_new_user_transform_statistics(start_date,over_date,page,page_nums):
    if over_date >= get_cur_day():
        cur_date_df = get_new_user_transform_massage(get_cur_day())
        old_date_sql = f""" select date,created_user_count,new_orders,new_user_payment_amount,created_user_orders_count,new_price,all_orders,all_user_payment_amount,all_user_orders_count,all_price from vh_user_transform_statistics where `date` between '{start_date}' and '{over_date}'"""
        statistics_conn = nopool_conn("vh_data_statistics").connect()
        old_date_df = pd.read_sql_query(old_date_sql,statistics_conn)
        statistics_conn.close()
        nopool_conn("vh_data_statistics").dispose()
        main_dff = old_date_df.append(cur_date_df).reset_index(drop=True)
        main_dff["created_user_buy_transform"] = main_dff.created_user_orders_count / main_dff.created_user_count * 100
        main_dff["created_user_buy_transform"] = main_dff.created_user_buy_transform.apply(lambda x:'%.2f' % x)
        main_dff["created_user_buy_percentage"] = main_dff.created_user_orders_count / main_dff.all_user_orders_count * 100
        main_dff["created_user_buy_percentage"] = main_dff.created_user_buy_percentage.apply(lambda x:'%.2f' % x)
        main_dff["created_user_orders_percentage"] = main_dff.new_orders / main_dff.all_orders * 100
        main_dff["created_user_orders_percentage"] = main_dff.created_user_orders_percentage.apply(lambda x:'%.2f' % x)
        main_dff["created_user_payment_amount_percentage"] = main_dff.new_user_payment_amount / main_dff.all_user_payment_amount * 100
        main_dff["created_user_payment_amount_percentage"] = main_dff.created_user_payment_amount_percentage.apply(lambda x:'%.2f' % x)
    else:
        main_sql = f""" select date,created_user_count,new_orders,new_user_payment_amount,created_user_orders_count,new_price,all_orders,all_user_payment_amount,all_user_orders_count,all_price from vh_user_transform_statistics where `date` between '{start_date}' and '{over_date}'"""
        statistics_conn = nopool_conn("vh_data_statistics").connect()
        main_dff = pd.read_sql_query(main_sql,statistics_conn)
        statistics_conn.close()
        nopool_conn("vh_data_statistics").dispose()
        main_dff["created_user_buy_transform"] = main_dff.created_user_orders_count / main_dff.created_user_count * 100
        main_dff["created_user_buy_transform"] = main_dff.created_user_buy_transform.apply(lambda x:'%.2f' % x)
        main_dff["created_user_buy_percentage"] = main_dff.created_user_orders_count / main_dff.all_user_orders_count * 100
        main_dff["created_user_buy_percentage"] = main_dff.created_user_buy_percentage.apply(lambda x:'%.2f' % x)
        main_dff["created_user_orders_percentage"] = main_dff.new_orders / main_dff.all_orders * 100
        main_dff["created_user_orders_percentage"] = main_dff.created_user_orders_percentage.apply(lambda x:'%.2f' % x)
        main_dff["created_user_payment_amount_percentage"] = main_dff.new_user_payment_amount / main_dff.all_user_payment_amount * 100
        main_dff["created_user_payment_amount_percentage"] = main_dff.created_user_payment_amount_percentage.apply(lambda x:'%.2f' % x)
    if main_dff.empty:
        return{"data":[],"total":0}
    else:
        main_dff["date"] = main_dff.date.astype(str)
        main_dff = main_dff[["date","created_user_count","created_user_buy_transform","created_user_orders_count","all_user_orders_count","created_user_buy_percentage","new_orders","all_orders","created_user_orders_percentage","new_user_payment_amount","all_user_payment_amount","created_user_payment_amount_percentage","new_price","all_price"]]
    Summary_dff = pd.DataFrame({"created_user_count":main_dff.created_user_count.sum(),"created_user_orders_count":main_dff.created_user_orders_count.sum(),"all_user_orders_count":main_dff.all_user_orders_count.sum(),"new_orders":main_dff.new_orders.sum(),"all_orders":main_dff.all_orders.sum(),"new_user_payment_amount":main_dff.new_user_payment_amount.sum(),"all_user_payment_amount":main_dff.all_user_payment_amount.sum()},index=[0])    
    Summary_dff["created_user_buy_transform"] = Summary_dff.created_user_orders_count / Summary_dff.created_user_count * 100
    Summary_dff["created_user_buy_transform"] = Summary_dff.created_user_buy_transform.apply(lambda x:'%.2f' % x)
    Summary_dff["created_user_buy_percentage"] = Summary_dff.created_user_orders_count / Summary_dff.all_user_orders_count * 100
    Summary_dff["created_user_buy_percentage"] = Summary_dff.created_user_buy_percentage.apply(lambda x:'%.2f' % x)
    Summary_dff["created_user_orders_percentage"] = Summary_dff.new_orders / Summary_dff.all_orders * 100
    Summary_dff["created_user_orders_percentage"] = Summary_dff.created_user_orders_percentage.apply(lambda x:'%.2f' % x)
    Summary_dff["created_user_payment_amount_percentage"] = Summary_dff.new_user_payment_amount / Summary_dff.all_user_payment_amount * 100
    Summary_dff["created_user_payment_amount_percentage"] = Summary_dff.created_user_payment_amount_percentage.apply(lambda x:'%.2f' % x)
    Summary_dff["new_price"] = Summary_dff.new_user_payment_amount/Summary_dff.created_user_orders_count
    Summary_dff = Summary_dff.fillna(0) 
    Summary_dff["new_price"] = Summary_dff.new_price.apply(lambda x:round(x))
    Summary_dff["all_price"] = Summary_dff.all_user_payment_amount/Summary_dff.all_user_orders_count
    Summary_dff["all_price"] = Summary_dff.all_price.apply(lambda x:round(x))
    total = len(main_dff)
    main_dff = main_dff[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
    res_json = {"data":json.loads(main_dff.to_json(orient="records")),"Summary":json.loads(Summary_dff.to_json(orient="records")),"total":total}
    return res_json