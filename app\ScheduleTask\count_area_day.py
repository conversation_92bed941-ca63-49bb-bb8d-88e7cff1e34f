import pymysql
import pandas as pd
from datetime import datetime, timedelta
import warnings

## 区域统计

warnings.filterwarnings("ignore")


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_last_day():
    last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    return last_day


def get_province(rr_host, rr_port, rr_user, rr_password):
    """
    获取省份
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, 'vh_user')
    sql = f"""SELECT id 'province_id',`name` 'province' FROM `vh_regional` WHERE type = 0"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df


def get_sales(day, table, rr_host, rr_port, rr_user, rr_password):
    """
    获取销售数据
    :param day:
    :param table:
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_orders")
    sql = f"""SELECT b.province_id, a.payment_amount, a.order_qty, a.uid 
              FROM {table} a 
              LEFT JOIN vh_order_main b ON a.main_order_id = b.id 
              WHERE FROM_UNIXTIME( a.created_time, '%Y-%m-%d') = '{day}'
              AND a.sub_order_status IN( 1, 2, 3 )"""
    df = pd.read_sql(sql, conn)
    conn.close()
    return df


def deal_with_data(rr_host, rr_port, rr_user, rr_password):
    """
    处理数据
    :return:
    """
    tables = ["vh_flash_order", "vh_cross_order", "vh_second_order", "vh_tail_order"]
    last_day = get_last_day()
    # last_day = '2022-07-01'

    # 闪购
    sales_info = get_sales(last_day, 'vh_flash_order', rr_host, rr_port, rr_user, rr_password)
    province_info = get_province(rr_host, rr_port, rr_user, rr_password)
    # 销售额
    df_sales = sales_info[['province_id', 'payment_amount']].groupby('province_id').sum().reset_index()
    if sales_info.empty:
        df_sales['payment_amount'] = 0
    # 销售数量
    df_nums = sales_info[['province_id']].groupby('province_id').size().reset_index().rename(columns={0: 'orders'})
    # 销售人数
    df_users = sales_info[['province_id', 'uid']].groupby(['province_id', 'uid']).size().reset_index().rename(
        columns={0: 'sale_users'}).groupby('province_id').size().reset_index().rename(columns={0: 'sale_users'})
    dff = pd.merge(df_sales, df_nums, how='left').merge(df_users, how='left')
    dff_end = pd.merge(dff, province_info, how='left')
    dff_end_flash = dff_end.rename(
        columns={'payment_amount': 'flash_sales', 'orders': 'flash_orders', 'sale_users': 'flash_buy_users'})
    total_sales = df_sales['payment_amount'].sum()
    dff_end_flash['flash_proportion'] = dff_end_flash['flash_sales'].apply(
        lambda x: round(x / total_sales, 4) * 100)

    # 跨境
    sales_info = get_sales(last_day, 'vh_cross_order', rr_host, rr_port, rr_user, rr_password)
    province_info = get_province(rr_host, rr_port, rr_user, rr_password)
    # 销售额
    df_sales = sales_info[['province_id', 'payment_amount']].groupby('province_id').sum().reset_index()
    if sales_info.empty:
        df_sales['payment_amount'] = 0
    # 销售数量
    df_nums = sales_info[['province_id']].groupby('province_id').size().reset_index().rename(columns={0: 'orders'})
    # 销售人数
    df_users = sales_info[['province_id', 'uid']].groupby(['province_id', 'uid']).size().reset_index().rename(
        columns={0: 'sale_users'}).groupby('province_id').size().reset_index().rename(columns={0: 'sale_users'})
    dff = pd.merge(df_sales, df_nums, how='left').merge(df_users, how='left')
    dff_end = pd.merge(dff, province_info, how='left')
    dff_end_cross = dff_end.rename(
        columns={'payment_amount': 'cross_sales', 'orders': 'cross_orders', 'sale_users': 'cross_buy_users'})
    total_sales = df_sales['payment_amount'].sum()
    dff_end_cross['cross_proportion'] = dff_end_cross['cross_sales'].apply(
        lambda x: round(x / total_sales, 4) * 100)

    # 秒发
    sales_info = get_sales(last_day, 'vh_second_order', rr_host, rr_port, rr_user, rr_password)
    province_info = get_province(rr_host, rr_port, rr_user, rr_password)
    # 销售额
    df_sales = sales_info[['province_id', 'payment_amount']].groupby('province_id').sum().reset_index()
    if sales_info.empty:
        df_sales['payment_amount'] = 0
    # 销售数量
    df_nums = sales_info[['province_id']].groupby('province_id').size().reset_index().rename(columns={0: 'orders'})
    # 销售人数
    df_users = sales_info[['province_id', 'uid']].groupby(['province_id', 'uid']).size().reset_index().rename(
        columns={0: 'sale_users'}).groupby('province_id').size().reset_index().rename(columns={0: 'sale_users'})
    dff = pd.merge(df_sales, df_nums, how='left').merge(df_users, how='left')
    dff_end = pd.merge(dff, province_info, how='left')
    dff_end_second = dff_end.rename(
        columns={'payment_amount': 'second_sales', 'orders': 'second_orders', 'sale_users': 'second_buy_users'})
    total_sales = df_sales['payment_amount'].sum()
    dff_end_second['second_proportion'] = dff_end_second['second_sales'].apply(
        lambda x: round(x / total_sales, 4) * 100)

    # 尾货
    sales_info = get_sales(last_day, 'vh_tail_order', rr_host, rr_port, rr_user, rr_password)
    province_info = get_province(rr_host, rr_port, rr_user, rr_password)
    # 销售额
    df_sales = sales_info[['province_id', 'payment_amount']].groupby('province_id').sum().reset_index()
    if sales_info.empty:
        df_sales['payment_amount'] = 0
    # 销售数量
    df_nums = sales_info[['province_id']].groupby('province_id').size().reset_index().rename(columns={0: 'orders'})
    # 销售人数
    df_users = sales_info[['province_id', 'uid']].groupby(['province_id', 'uid']).size().reset_index().rename(
        columns={0: 'sale_users'}).groupby('province_id').size().reset_index().rename(columns={0: 'sale_users'})
    dff = pd.merge(df_sales, df_nums, how='left').merge(df_users, how='left')
    dff_end = pd.merge(dff, province_info, how='left')
    dff_end_tail = dff_end.rename(
        columns={'payment_amount': 'tail_sales', 'orders': 'tail_orders', 'sale_users': 'tail_buy_users'})
    total_sales = df_sales['payment_amount'].sum()
    dff_end_tail['tail_proportion'] = dff_end_tail['tail_sales'].apply(
        lambda x: round(x / total_sales, 4) * 100)
    # if len(sales_info) > 0:
    #     dff_end_flash = dff_end_flash.apppend(dff_end_tail).reset_index(drop=True)
    #     dff_end_flash = pd.merge(dff_end_flash[['province', 'flash_sales']].groupby('province').sum().reset_index(),
    #                              dff_end_flash[['province', 'flash_orders']].groupby('province').sum().reset_index(),
    #                              how='left').merge(
    #         dff_end_flash[['province', 'flash_buy_users']].groupby('province').sum().reset_index(), how='left')
    #     dff_end_flash['flash_proportion'] = dff_end_flash['flash_sales'].apply(
    #         lambda x: round(x / dff_end_flash['flash_sales'].sum(), 4) * 100)
    # else:
    #     pass
    dff_province = province_info[['province']]
    dff_require = pd.merge(dff_province, dff_end_flash, how='left').merge(dff_end_cross, how='left').merge(
        dff_end_second, how='left').merge(dff_end_tail, how='left')
    dff_require['day'] = last_day
    dff_require = dff_require.fillna(0)
    dff_require = dff_require.drop('province_id', axis=1)
    dff_require = dff_require[['day', 'province',
                               'flash_sales', 'flash_orders', 'flash_buy_users', 'flash_proportion',
                               'cross_sales', 'cross_orders', 'cross_buy_users', 'cross_proportion',
                               'second_sales', 'second_orders', 'second_buy_users', 'second_proportion',
                               'tail_sales', 'tail_orders', 'tail_buy_users', 'tail_proportion']]
    data_list = dff_require.values.tolist()
    return data_list


def exits_data(day, province, cursor):
    """
    验证是否已存在
    :param day: 日期
    :param province: 省份
    :param cursor
    :return:
    """
    sql = f"""select * from vh_regional_statistics where `day` = '{day}' and `province`= '{province}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_regional_statistics where `day` = '{day}' and `province`= '{province}'"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(data_list, rm_host, rm_port, rm_user, rm_password):
    conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
    cursor = conn.cursor()
    try:
        for i in data_list:
            exits_data(day=i[0], province=i[1], cursor=cursor)
            sql = f"""insert into vh_regional_statistics 
                     (`day`, `province`,
                      `flash_sales`, `flash_orders`, `flash_buy_users`, `flash_proportion`, 
                      `cross_sales`, `cross_orders`, `cross_buy_users`, `cross_proportion`,
                     `second_sales`, `second_orders`, `second_buy_users`, `second_proportion`,
                     `tail_sales`, `tail_orders`, `tail_buy_users`, `tail_proportion`) 
                     values('{i[0]}','{i[1]}',
                             {i[2]},{i[3]},{i[4]},{i[5]},
                             {i[6]},{i[7]},{i[8]},{i[9]},
                             {i[10]},{i[11]},{i[12]},{i[13]},
                             {i[14]},{i[15]},{i[16]},{i[17]})"""
            cursor.execute(sql)
            conn.commit()
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
    finally:
        conn.close()


def handler_count_area(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        data_list = deal_with_data(rr_host, rr_port, rr_user, rr_password)
        insert_mysql(data_list, rm_host, rm_port, rm_user, rm_password)
        return 1
    except Exception as e:
        print(e)
        return -1
