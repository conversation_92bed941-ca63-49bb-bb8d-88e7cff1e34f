import json
import pymysql
import warnings
import concurrent
import pandas as pd
from sqlalchemy import create_engine
from config import ChannelTypeConfig
from datetime import datetime
from app.DButils.MysqlHelper import conn_str

warnings.filterwarnings("ignore")


def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine


def get_orders(table,timestamp_start,timestamp_end,new_user_uid):
    orders_conn = nopool_conn("vh_orders").connect()
    if len(new_user_uid) == 1:
        orders_sql = f"""select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid,1 as 'count',payment_amount - refund_money as 'payment_amount' from {table} where created_time between {timestamp_start} and {timestamp_end} and sub_order_status in (1,2,3) and payment_amount - refund_money != 0 and uid = {new_user_uid[0]}"""
    else:
        orders_sql = f"""select from_unixtime(created_time,'%%Y-%%m-%%d') as 'date',uid,1 as 'count',payment_amount - refund_money as 'payment_amount' from {table} where created_time between {timestamp_start} and {timestamp_end} and sub_order_status in (1,2,3) and payment_amount - refund_money != 0 and uid in {new_user_uid}"""  
    df = pd.read_sql_query(orders_sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return df



def get_newusercreatedfrom(start_date,over_date,page,page_nums):
    Cg = ChannelTypeConfig.channel_type_config
    tables = [Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]
    timestamp_start,timestamp_end = int(datetime.strptime(f"{start_date} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"{over_date} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
    new_user_sql = f""" select uid,case reg_from when 0 then '未知' when 1 then 'android' when 2 then 'ios' when 3 then '酒云网小程序' when 4 then 'h5' when 5 then 'PC' when 6 then '抖音小程序' when 7 then '后台添加' when 8 then '酒历小程序' when 9 then '公社小程序' when 10 then 'iPad' when 11 then '门店小程序' when 13 then '酒展通小程序' end as 'reg_from' from vh_user where created_time between {timestamp_start} and {timestamp_end}"""
    user_conn = nopool_conn("vh_user").connect()
    new_user_df = pd.read_sql_query(new_user_sql,user_conn)
    user_conn.close()
    nopool_conn("vh_user").dispose()
    if new_user_df.empty:
        return {"data":[],"total":0}
    new_user_uid = tuple(set(new_user_df.uid.tolist()))
    with concurrent.futures.ThreadPoolExecutor(max_workers=200) as executor:
        tasks = [executor.submit(get_orders,table,timestamp_start,timestamp_end,new_user_uid) for table in tables]
        orders_user_df = pd.DataFrame(columns=["uid","count"])
        for future in concurrent.futures.as_completed(tasks):
            orders_user_df=orders_user_df.append(future.result()).reset_index(drop=True)
    if orders_user_df.empty:
        new_user_df = new_user_df.groupby(by="reg_from",as_index=False).agg({"uid":"count"})
        new_user_df.rename(columns={"uid":"created_user_count"},inplace=True)
        new_user_df["created_user_orders_count"] = 0
        new_created_df = new_user_df.copy()
    else:
        orders_user_df = orders_user_df.drop_duplicates(subset="uid",keep="first")
        new_created_df = pd.merge(new_user_df,orders_user_df,how="left",on="uid").fillna(0).reset_index(drop=True)
        new_created_df = new_created_df.groupby(by="reg_from",as_index=False).agg({"uid":"count","count":sum})
        new_created_df.columns = ["reg_from","created_user_count","created_user_orders_count"]
        new_created_df = new_created_df[["reg_from","created_user_count","created_user_orders_count"]]
    new_created_df = new_created_df[["reg_from","created_user_count","created_user_orders_count"]]
    main_dff = new_created_df.groupby(by="reg_from",as_index=False).agg({"created_user_count":sum,"created_user_orders_count":sum})
    main_dff["created_user_percentage"] = main_dff.created_user_count/sum(main_dff.created_user_count) * 100
    main_dff["created_user_percentage"] = main_dff.created_user_percentage.fillna(0)
    main_dff["created_user_percentage"] = main_dff.created_user_percentage.apply(lambda x:'%.2f' % x)
    main_dff["created_user_orders_percentage"] = main_dff.created_user_orders_count/sum(main_dff.created_user_orders_count) * 100
    main_dff["created_user_orders_percentage"] = main_dff.created_user_orders_percentage.fillna(0)
    main_dff["created_user_orders_percentage"] = main_dff.created_user_orders_percentage.apply(lambda x:'%.2f' % x)
    main_dff["orders_cvr"] = main_dff.created_user_orders_count/main_dff.created_user_count * 100
    main_dff["orders_cvr"] = main_dff.orders_cvr.apply(lambda x:'%.2f' % x)
    main_dff = main_dff[["reg_from","created_user_count","created_user_percentage","created_user_orders_count","created_user_orders_percentage","orders_cvr"]]
    Summary_dff = pd.DataFrame({"created_user_count":main_dff.created_user_count.sum(),"created_user_percentage":"100.00","created_user_orders_count":main_dff.created_user_orders_count.sum(),"created_user_orders_percentage":"100.00"},index=[0])
    Summary_dff["orders_cvr"] = Summary_dff.created_user_orders_count/Summary_dff.created_user_count * 100
    Summary_dff["orders_cvr"] = Summary_dff.orders_cvr.fillna(0)
    Summary_dff["orders_cvr"] = Summary_dff.orders_cvr.apply(lambda x:'%.2f' % x)
    main_dff = main_dff[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
    if main_dff.empty:
        return{"data":[],"total":0}
    else:
        total = len(main_dff)
        res_json = {"data":json.loads(main_dff.to_json(orient="records")),"Summary":json.loads(Summary_dff.to_json(orient="records")),"total":total}
    return res_json
