# -*- coding: utf-8 -*-
# 直播实时统计#
import requests
import json
from app.CommonLibraries.GetNowTime import time_str_to_timestamp
import pandas as pd

response = requests.session()


def get_live_order_info(day, period_type, import_type,buyer, sort_key, goods_type, page, page_nums, commodities_conn, orders_conn,wiki_conn):
    """
    获取订单详细信息
    :param day:
    :param period_type:
    :param import_type:
    :param sort_key:
    :param goods_type:
    :param page:
    :param page_nums:
    :param commodities_conn:
    :param orders_conn:
    :return:
    """
    sql_product_type = "select id,name from vh_product_type"
    all_product_type = pd.read_sql(sql_product_type, wiki_conn)
    all_ids = all_product_type['id'].tolist()
    goods_type_list = []
    if goods_type[0] == 0:
        for id in all_ids:
            goods_type_list.append(str(all_product_type[all_product_type['id'] == id]['name'].values[0]))
    else:
        for id in goods_type:
            goods_type_list.append(str(all_product_type[all_product_type['id'] == id]['name'].values[0]))
    start_timestamp = time_str_to_timestamp(f"{day} 00:00:00")
    end_timestamp = time_str_to_timestamp(f"{day} 23:59:59")
    df_hour = pd.DataFrame(
        ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17',
         '18', '19', '20', '21', '22', '23']).rename(columns={0: 'hour'})
    df_base = pd.DataFrame(columns=['period', 'payment_amount', 'order_qty', 'order_users', 'title', 'price'])
    df_count_base = pd.DataFrame(columns=['hour', 'payment_amount', 'order_qty'])
    #获取采购人信息
    if buyer ==None or buyer == "":
            buyer=""
    else:
        buyer_name_sql=f"select bb.`buyer_id`,bb.`buyer_name` from (select DISTINCT a.buyer_id from (select `buyer_id` from vh_commodities.vh_periods_cross where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id` from vh_commodities. vh_periods_flash where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id` from vh_commodities.vh_periods_second where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id` from vh_commodities.vh_periods_leftover where buyer_name is not null and `buyer_name`  not in (''))a)b left join (select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_cross where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_flash where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_second where buyer_name is not null and `buyer_name`  not in ('') union select `buyer_id`,`buyer_name` from vh_commodities.vh_periods_leftover where buyer_name is not null and `buyer_name`  not in (''))bb on b.`buyer_id` = bb.`buyer_id`"
        df_buyer_name= pd.read_sql_query(buyer_name_sql, commodities_conn)
        df_buyer_name.set_index('buyer_name',inplace=True)
        df_buyer=df_buyer_name.T
        df_buyer_dict=df_buyer.to_dict('list')
        bid=df_buyer_dict.get(buyer)[0]
    if period_type == "":
        # ***********-闪购
        # 获取统计数据
        sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_flash_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
        df_count1 = pd.read_sql(sql_count1, orders_conn)
        if len(df_count1) == 0:
            dff_flash = df_base
            dff_count_flash = df_count_base
            total_flash = 0
        else:
            df_count = df_count1
            sql = f"""SELECT period, payment_amount 'payment_amount', 
                      if(order_qty>1,1,order_qty) 'order_qty', 
                      uid
                      FROM `vh_flash_order` 
                      WHERE created_time between {start_timestamp} and {end_timestamp} 
                      AND sub_order_status BETWEEN 1 AND 3"""
            df_order_info = pd.read_sql_query(sql, orders_conn)
            # 销售额
            df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
            # 销售份数
            df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
            # 销售人数
            df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(0,
                                                                                                                    axis=1)
            df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(columns={0: 'order_users'})
            # 合并订单明细
            df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
            flash_periods = tuple(df_order_info['period'].tolist())

            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
            if import_type != "":
                if int(import_type) == 0:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 1:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 2:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    return "未知进口类型"
            else:
                if goods_type[0] != 0:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]}  AND title like '%%直播%%'AND buyer_id = {bid}"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods}  AND title like '%%直播%%'AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                            else:
                                sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
            if dff_periods.empty:
                dff_flash = df_base
                dff_count_flash = df_count_base
                total_flash = 0
            else:
                dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
                dff_flash = pd.merge(df_order_info, dff_periods)
                total_flash = len(dff_flash)
                if sort_key == "":
                    dff_flash = dff_flash.reset_index(drop=True)
                else:
                    dff_flash = dff_flash.sort_values(sort_key, ascending=False).reset_index(drop=True)
                # 获取统计数据
                dff_count = pd.merge(df_count, dff_periods[['period']])
                dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
                dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
                dff_count_flash = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                                    ascending=False).reset_index(
                    drop=True)############################################
        # ***********-尾货
        # 获取统计数据
        sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_tail_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
        df_count1 = pd.read_sql(sql_count1, orders_conn)
        if len(df_count1) == 0:
            dff_tail = df_base
            dff_count_tail = df_count_base
            total_tail = 0
        else:
            df_count = df_count1
            sql = f"""SELECT period, payment_amount 'payment_amount', 
                                              if(order_qty>1,1,order_qty) 'order_qty', 
                                              uid
                                              FROM `vh_tail_order` 
                                              WHERE created_time between {start_timestamp} and {end_timestamp} 
                                              AND sub_order_status BETWEEN 1 AND 3"""
            df_order_info = pd.read_sql_query(sql, orders_conn)
            # 销售额
            df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
            # 销售份数
            df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
            # 销售人数
            df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(0,
                                                                                                                    axis=1)
            df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(columns={0: 'order_users'})
            # 合并订单明细
            df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
            flash_periods = tuple(df_order_info['period'].tolist())

            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
            if import_type != "":
                if int(import_type) == 0:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 1:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail,commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 2:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    return "未知进口类型"
            else:
                if goods_type[0] != 0:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
            if dff_periods.empty:
                dff_tail= df_base
                dff_count_tail= df_count_base
                total_tail= 0
            else:
                dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
                dff_tail = pd.merge(df_order_info, dff_periods)
                total_tail = len(dff_flash)
                if sort_key == "":
                    dff_tail = dff_tail.reset_index(drop=True)
                else:
                    dff_tail= dff_tail.sort_values(sort_key, ascending=False).reset_index(drop=True)
                # 获取统计数据
                dff_count = pd.merge(df_count, dff_periods[['period']])
                dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
                dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
                dff_count_tail= pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                                    ascending=False).reset_index(
                    drop=True)
        # ***********-跨境
        # 获取统计数据
        sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_cross_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
        df_count1 = pd.read_sql(sql_count1, orders_conn)
        if len(df_count1) == 0:
            dff_cross = df_base
            dff_count_cross = df_count_base
            total_cross = 0
        else:
            df_count = df_count1
            sql = f"""SELECT period, payment_amount 'payment_amount', 
                              if(order_qty>1,1,order_qty) 'order_qty', 
                              uid
                              FROM `vh_cross_order` 
                              WHERE created_time between {start_timestamp} and {end_timestamp}
                              AND sub_order_status BETWEEN 1 AND 3"""
            df_order_info = pd.read_sql_query(sql, orders_conn)
            # 销售额
            df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
            # 销售份数
            df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
            # 销售人数
            df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(0,
                                                                                                                    axis=1)
            df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(columns={0: 'order_users'})
            # 合并订单明细
            df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
            flash_periods = tuple(df_order_info['period'].tolist())

            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
            if import_type != "":
                if int(import_type) == 0:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 1:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross,commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 2:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    return "未知进口类型"
            else:
                if goods_type[0] != 0:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)

            if dff_periods.empty:
                dff_cross = df_base
                dff_count_cross = df_count_base
                total_cross = 0
            else:
                dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
                dff_cross = pd.merge(df_order_info, dff_periods)
                total_cross = len(dff_cross)
                if sort_key == "":
                    dff_cross = dff_cross.reset_index(drop=True)
                else:
                    dff_cross = dff_cross.sort_values(sort_key, ascending=False).reset_index(drop=True)
                # 获取统计数据
                dff_count = pd.merge(df_count, dff_periods[['period']])
                dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
                dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
                dff_count = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                              ascending=False).reset_index(
                    drop=True)
                dff_count['payment_amount'] = dff_count['payment_amount'].astype('int')
                dff_count['order_qty'] = dff_count['order_qty'].astype('int')
                dff_count_cross = pd.merge(df_hour, dff_count, how='left').fillna(0)
        # ***********-秒发
        # 获取统计数据
        sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_second_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
        df_count1 = pd.read_sql(sql_count1, orders_conn)
        if len(df_count1) == 0:
            dff_second = df_base
            dff_count_second = df_count_base
            total_second = 0
        else:
            df_count = df_count1
            sql = f"""SELECT period, payment_amount 'payment_amount', 
                              if(order_qty>1,1,order_qty) 'order_qty', 
                              uid
                              FROM `vh_second_order` 
                              WHERE created_time between {start_timestamp} and {end_timestamp} 
                              AND sub_order_status BETWEEN 1 AND 3"""
            df_order_info = pd.read_sql_query(sql, orders_conn)
            # 销售额
            df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
            # 销售份数
            df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
            # 销售人数
            df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(0,
                                                                                                                    axis=1)
            df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(columns={0: 'order_users'})
            # 合并订单明细
            df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
            flash_periods = tuple(df_order_info['period'].tolist())

            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
            if import_type != "":
                if int(import_type) == 0:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 1:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_second, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_second,commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 2:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    return "未知进口类型"
            else:
                if goods_type[0] != 0:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%'AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)


            if dff_periods.empty:
                dff_second = df_base
                dff_count_second = df_count_base
                total_second = 0
            else:
                dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
                dff_second = pd.merge(df_order_info, dff_periods)
                total_second = len(dff_second)
                if sort_key == "":
                    dff_second = dff_second.reset_index(drop=True)
                else:
                    dff_second = dff_second.sort_values(sort_key, ascending=False).reset_index(drop=True)
                # 获取统计数据
                dff_count = pd.merge(df_count, dff_periods[['period']])
                dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
                dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
                dff_count = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                              ascending=False).reset_index(
                    drop=True)
                dff_count['payment_amount'] = dff_count['payment_amount'].astype('int')
                dff_count['order_qty'] = dff_count['order_qty'].astype('int')
                dff_count_second = pd.merge(df_hour, dff_count, how='left').fillna(0)
        dff_count = dff_count_flash.append(dff_count_tail).append(dff_count_second).append(dff_count_cross)
        if dff_count.empty:
            return {
                "order_json": [],
                "count_json": [],
                "total": 0
            }
        dff_count1 = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
        dff_count2 = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
        dff_count = pd.merge(dff_count1, dff_count2).reset_index(drop=True)
        dff_count['payment_amount'] = dff_count['payment_amount'].astype('int')
        dff_count['order_qty'] = dff_count['order_qty'].astype('int')
        dff_count = pd.merge(df_hour, dff_count, how='left').fillna(0)
        dff = dff_flash.append(dff_tail).append(dff_cross).append(dff_second)
        if dff.empty:
            return {
                "order_json": [],
                "count_json": [],
                "total": 0
            }
        total = total_flash + total_tail + total_cross + total_second
    else:
        period_type = int(period_type)
        if period_type == 0:  # 闪购+尾货
            # 获取统计数据
            sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_flash_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
            df_count1 = pd.read_sql(sql_count1, orders_conn)
            if len(df_count1) == 0:
                dff_flash = df_base
                dff_count_flash = df_count_base
                total_flash = 0
            else:
                df_count = df_count1
                sql = f"""SELECT period, payment_amount 'payment_amount', 
                          if(order_qty>1,1,order_qty) 'order_qty', 
                          uid
                          FROM `vh_flash_order` 
                          WHERE created_time between {start_timestamp} and {end_timestamp} 
                          AND sub_order_status BETWEEN 1 AND 3"""
                df_order_info = pd.read_sql_query(sql, orders_conn)
                # 销售额
                df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
                # 销售份数
                df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
                # 销售人数
                df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(
                    0,
                    axis=1)
                df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(
                    columns={0: 'order_users'})
                # 合并订单明细
                df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
                flash_periods = tuple(df_order_info['period'].tolist())

                # 获取产品数据
                dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
                if import_type != "":
                    if int(import_type) == 0:
                        if goods_type[0] != 0:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                        else:
                            if buyer != "":
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                            else:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    elif int(import_type) == 1:
                        if goods_type[0] != 0:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                        else:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                    elif int(import_type) == 2:
                        if goods_type[0] != 0:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    else:
                                        sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                        else:
                            if buyer != "":
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                            else:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        return "未知进口类型"
                else:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                                else:
                                    sql_periods_flash = f"""SELECT id 'period',title,price FROM `vh_periods_flash` where id in {flash_periods} AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_flash, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)

                if dff_periods.empty:
                    dff_flash = df_base
                    dff_count_flash = df_count_base
                    total_flash = 0
                else:
                    dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(
                        drop=True)
                    dff_flash = pd.merge(df_order_info, dff_periods)
                    total_flash = len(dff_flash)
                    if sort_key == "":
                        dff_flash = dff_flash.reset_index(drop=True)
                    else:
                        dff_flash = dff_flash.sort_values(sort_key, ascending=False).reset_index(drop=True)
                    # 获取统计数据
                    dff_count = pd.merge(df_count, dff_periods[['period']])
                    dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
                    dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
                    dff_count_flash = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values(
                        'payment_amount',
                        ascending=False).reset_index(
                        drop=True)

            # 尾货
            # 获取统计数据
            sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_tail_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
            df_count1 = pd.read_sql(sql_count1, orders_conn)
            if len(df_count1) == 0:
                dff_tail = df_base
                dff_count_tail = df_count_base
                total_tail = 0
            else:
                df_count = df_count1
                sql = f"""SELECT period, payment_amount 'payment_amount', 
                                                  if(order_qty>1,1,order_qty) 'order_qty', 
                                                  uid
                                                  FROM `vh_tail_order` 
                                                  WHERE created_time between {start_timestamp} and {end_timestamp} 
                                                  AND sub_order_status BETWEEN 1 AND 3"""
                df_order_info = pd.read_sql_query(sql, orders_conn)
                # 销售额
                df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
                # 销售份数
                df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
                # 销售人数
                df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(
                    0,
                    axis=1)
                df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(
                    columns={0: 'order_users'})
                # 合并订单明细
                df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
                flash_periods = tuple(df_order_info['period'].tolist())

                # 获取产品数据
                dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
                if import_type != "":
                    if int(import_type) == 0:
                        if goods_type[0] != 0:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    else:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                        else:
                            if buyer != "":
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                            else:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    elif int(import_type) == 1:
                        if goods_type[0] != 0:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    else:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                        else:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                    else:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                    df_periods = pd.read_sql_query(sql_periods_tail,commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                    elif int(import_type) == 2:
                        if goods_type[0] != 0:
                            if buyer != "":
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    else:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                    df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                            else:
                                for per_type in goods_type_list:
                                    if len(flash_periods) == 1:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    else:
                                        sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                    df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                    dff_periods = dff_periods.append(df_periods)
                        else:
                            if buyer != "":
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                            else:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        return "未知进口类型"
                else:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)

                if dff_periods.empty:
                    dff_tail = df_base
                    dff_count_tail = df_count_base
                    total_tail = 0
                else:
                    dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(
                        drop=True)
                    dff_tail = pd.merge(df_order_info, dff_periods)
                    total_tail = len(dff_tail)
                    if sort_key == "":
                        dff_tail = dff_tail.reset_index(drop=True)
                    else:
                        dff_tail = dff_tail.sort_values(sort_key, ascending=False).reset_index(drop=True)
                    # 获取统计数据
                    dff_count = pd.merge(df_count, dff_periods[['period']])
                    dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
                    dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
                    dff_count_tail = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                                       ascending=False).reset_index(
                        drop=True)
            dff_count = dff_count_flash.append(dff_count_tail)
            if dff_count.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            dff_count1 = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
            dff_count2 = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
            dff_count = pd.merge(dff_count1, dff_count2).reset_index(drop=True)
            dff_count['payment_amount'] = dff_count['payment_amount'].astype('int')
            dff_count['order_qty'] = dff_count['order_qty'].astype('int')
            dff_count = pd.merge(df_hour, dff_count, how='left').fillna(0)
            dff = dff_flash.append(dff_tail)
            if dff.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            total = total_flash + total_tail

        # 跨境
        elif period_type == 1:
            # 获取统计数据
            sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_cross_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
            df_count1 = pd.read_sql(sql_count1, orders_conn)
            if len(df_count1) == 0:
                return {"count_json": "", "order_json": "", "total": 0}
            df_count = df_count1
            sql = f"""SELECT period, payment_amount 'payment_amount', 
                              if(order_qty>1,1,order_qty) 'order_qty', 
                              uid
                              FROM `vh_cross_order` 
                              WHERE created_time between {start_timestamp} and {end_timestamp}
                              AND sub_order_status BETWEEN 1 AND 3"""
            df_order_info = pd.read_sql_query(sql, orders_conn)
            # 销售额
            df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
            # 销售份数
            df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
            # 销售人数
            df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(0,
                                                                                                                    axis=1)
            df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(columns={0: 'order_users'})
            # 合并订单明细
            df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
            flash_periods = tuple(df_order_info['period'].tolist())

            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
            if import_type != "":
                if int(import_type) == 0:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 1:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross,commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 2:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    return "未知进口类型"
            else:
                if goods_type[0] != 0:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_cross = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                            else:
                                sql_periods_cross  = f"""SELECT id 'period',title,price FROM `vh_periods_cross` where id in {flash_periods} AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_cross , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
            if dff_periods.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
            dff = pd.merge(df_order_info, dff_periods)
            total = len(dff)
            if sort_key == "":
                dff = dff.reset_index(drop=True)
            else:
                dff = dff.sort_values(sort_key, ascending=False).reset_index(drop=True)
            # 获取统计数据
            dff_count = pd.merge(df_count, dff_periods[['period']])
            if dff_count.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
            dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
            dff_count = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                          ascending=False).reset_index(
                drop=True)
            dff_count['payment_amount'] = dff_count['payment_amount'].astype('int')
            dff_count['order_qty'] = dff_count['order_qty'].astype('int')
            dff_count = pd.merge(df_hour, dff_count, how='left').fillna(0)
        # 秒发
        elif period_type == 2:
            # 获取统计数据
            sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_second_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
            df_count1 = pd.read_sql(sql_count1, orders_conn)
            if len(df_count1) == 0:
                return {"count_json": "", "order_json": "", "total": 0}
            df_count = df_count1
            sql = f"""SELECT period, payment_amount 'payment_amount', 
                              if(order_qty>1,1,order_qty) 'order_qty', 
                              uid
                              FROM `vh_second_order` 
                              WHERE created_time between {start_timestamp} and {end_timestamp} 
                              AND sub_order_status BETWEEN 1 AND 3"""
            df_order_info = pd.read_sql_query(sql, orders_conn)
            # 销售额
            df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
            # 销售份数
            df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
            # 销售人数
            df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(0,
                                                                                                                    axis=1)
            df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(columns={0: 'order_users'})
            # 合并订单明细
            df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
            flash_periods = tuple(df_order_info['period'].tolist())

            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
            if import_type != "":
                if int(import_type) == 0:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 1:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_second, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_second,commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 2:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    return "未知进口类型"
            else:
                if goods_type[0] != 0:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_second = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                            else:
                                sql_periods_second  = f"""SELECT id 'period',title,price FROM `vh_periods_second` where id in {flash_periods} AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_second , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)

            if dff_periods.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
            dff = pd.merge(df_order_info, dff_periods)
            total = len(dff)
            if sort_key == "":
                dff = dff.reset_index(drop=True)
            else:
                dff = dff.sort_values(sort_key, ascending=False).reset_index(drop=True)
            # 获取统计数据
            dff_count = pd.merge(df_count, dff_periods[['period']])
            if dff_count.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
            dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
            dff_count = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                          ascending=False).reset_index(
                drop=True)
            dff_count['payment_amount'] = dff_count['payment_amount'].astype('int')
            dff_count['order_qty'] = dff_count['order_qty'].astype('int')
            dff_count = pd.merge(df_hour, dff_count, how='left').fillna(0)
        # 尾货
        elif period_type == 3:  # 跨境
            # 获取统计数据
            sql_count1 = f"""SELECT period,FROM_UNIXTIME(created_time,'%H') 'hour',payment_amount,if(order_qty>1,1,order_qty) 'order_qty' FROM `vh_tail_order` WHERE created_time between {start_timestamp} and {end_timestamp} AND sub_order_status IN(1,2,3)"""
            df_count1 = pd.read_sql(sql_count1, orders_conn)
            if len(df_count1) == 0:
                return {"count_json": "", "order_json": "", "total": 0}
            df_count = df_count1
            sql = f"""SELECT period, payment_amount 'payment_amount', 
                             if(order_qty>1,1,order_qty) 'order_qty', 
                              uid
                              FROM `vh_tail_order` 
                              WHERE created_time between {start_timestamp} and {end_timestamp} 
                              AND sub_order_status BETWEEN 1 AND 3"""
            df_order_info = pd.read_sql_query(sql, orders_conn)
            # 销售额
            df_order_info_1 = df_order_info[['period', 'payment_amount']].groupby('period').sum().reset_index()
            # 销售份数
            df_order_info_2 = df_order_info[['period', 'order_qty']].groupby('period').sum().reset_index()
            # 销售人数
            df_order_info_3 = df_order_info[['period', 'uid']].groupby(['period', 'uid']).size().reset_index().drop(0,
                                                                                                                    axis=1)
            df_order_info_4 = df_order_info_3.groupby('period').size().reset_index().rename(columns={0: 'order_users'})
            # 合并订单明细
            df_order_info = pd.merge(df_order_info_1, df_order_info_2).merge(df_order_info_4)
            flash_periods = tuple(df_order_info['period'].tolist())

            # 获取产品数据
            dff_periods = pd.DataFrame(columns=['period', 'title', 'price'])
            if import_type != "":
                if int(import_type) == 0:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 0 AND title like '%%直播%%' """
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 0 AND title like '%%直播%%' """
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 1:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail, commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 1 AND title like '%%直播%%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 1 AND title like '%%直播%%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail,commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                elif int(import_type) == 2:
                    if goods_type[0] != 0:
                        if buyer != "":
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                        else:
                            for per_type in goods_type_list:
                                if len(flash_periods) == 1:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                else:
                                    sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                                df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                                dff_periods = dff_periods.append(df_periods)
                    else:
                        if buyer != "":
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                        else:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} and import_type = 2 AND title like '%%直播%%'"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} and import_type = 2 AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    return "未知进口类型"
            else:
                if goods_type[0] != 0:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND product_category like '%{per_type}%'"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                else:
                    if buyer != "":
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%' AND buyer_id = {bid}"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)
                    else:
                        for per_type in goods_type_list:
                            if len(flash_periods) == 1:
                                sql_periods_tail = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id = {flash_periods[0]} AND title like '%%直播%%'"""
                            else:
                                sql_periods_tail  = f"""SELECT id 'period',title,price FROM `vh_periods_leftover` where id in {flash_periods} AND title like '%%直播%%'"""
                            df_periods = pd.read_sql_query(sql_periods_tail , commodities_conn)
                            dff_periods = dff_periods.append(df_periods)

            if dff_periods.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            dff_periods = dff_periods.reset_index(drop=True).drop_duplicates(subset='period').reset_index(drop=True)
            dff = pd.merge(df_order_info, dff_periods)
            total = len(dff)
            if sort_key == "":
                dff = dff.reset_index(drop=True)[
                      (page * page_nums - page_nums):page * page_nums]
            else:
                dff = dff.sort_values(sort_key, ascending=False).reset_index(drop=True)[
                      (page * page_nums - page_nums):page * page_nums]
            # 获取统计数据
            dff_count = pd.merge(df_count, dff_periods[['period']])
            if dff_count.empty:
                return {"count_json": "", "order_json": "", "total": 0}
            dff_count_money = dff_count[['hour', 'payment_amount']].groupby('hour').sum().reset_index()
            dff_count_nums = dff_count[['hour', 'order_qty']].groupby('hour').sum().reset_index()
            dff_count = pd.merge(dff_count_money, dff_count_nums, how='left').sort_values('payment_amount',
                                                                                          ascending=False).reset_index(
                drop=True)
            dff_count['payment_amount'] = dff_count['payment_amount'].astype('int')
            dff_count['order_qty'] = dff_count['order_qty'].astype('int')
            dff_count = pd.merge(df_hour, dff_count, how='left').fillna(0)
    dff = dff.rename(columns={'price': 'market_price'})[(page * page_nums - page_nums):page * page_nums]
    if dff.empty:
        return {"count_json": "", "order_json": "", "total": 0}
    res_json = {
        "order_json": json.loads(dff.to_json(orient="records")),
        "count_json": json.loads(dff_count.to_json(orient="records")),
        "total": total
    }

    return res_json







