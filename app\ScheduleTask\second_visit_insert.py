import json
import re
from datetime import datetime, timedelta
import requests

response = requests.session()


def get_second_visit_statistics(date, client):
    data = {"pv": [{"秒发列表": 0}], "uv": [{"秒发列表": 0}]}
    cards, columns = {}, {}
    pattern = r'【(.*?)】'
    resp = requests.get(
        "https://callback.vinehoo.com/commodities-micro-service/commodities_server/v3/second_home/aggregation_v2?client=3").json()

    # 拿到column
    for k, column in enumerate(resp['data']['column']):
        columns[column['id']] = column['name']
        data["pv"].append({column['name']: 0})
        data["uv"].append({column['name']: 0})

    # 拿到card
    for k, card in enumerate(resp['data']['card']):
        card_name = card.get('card_name', '')
        match = re.search(pattern, card_name)
        if match:
            card_name = match.group(1)
        else:
            card_name = card_name[:10]
        cards[card['id']] = card_name
        data["pv"].append({card_name: 0})
        data["uv"].append({card_name: 0})
    db = client.vinehoo_v3
    collection = db.gateway_logs

    k = 1
    # column数据
    for id, column_name in columns.items():
        pv = get_count_pv(collection, date, "/marketing-conf/v3/column/filtergoodslist",
                          {"request_param": "cid=" + str(id)})
        uv = get_count_uv(collection, date, "/marketing-conf/v3/column/filtergoodslist",
                          {"request_param": "cid=" + str(id)})
        data["pv"][k][column_name] = pv
        data["uv"][k][column_name] = uv
        k = k + 1

    # card数据
    for id, card_name in cards.items():
        pv = get_count_pv(collection, date, "/marketing-conf/v3/cardgoodslive/filtergoodslist",
                          {"request_param": "cid=" + str(id)})
        uv = get_count_uv(collection, date, "/marketing-conf/v3/cardgoodslive/filtergoodslist",
                          {"request_param": "cid=" + str(id)})
        data["pv"][k][card_name] = pv
        data["uv"][k][card_name] = uv
        k = k + 1

    pv = get_count_pv(collection, date, "/openapi/v3/commodity/list",
                      {
                          "request_body": '{"page":1,"periods_type":[1],"sort_type":"sort","order":"desc","filters":{}}'})
    uv = get_count_uv(collection, date, "/openapi/v3/commodity/list",
                      {
                          "request_body": '{"page":1,"periods_type":[1],"sort_type":"sort","order":"desc","filters":{}}'})
    data["pv"][0]['秒发列表'] = pv
    data["uv"][0]['秒发列表'] = uv

    client.close()
    return data


def get_count_uv(collection, date, url, where):
    start_time = datetime.strptime(date, '%Y-%m-%d').replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = datetime.strptime(date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=0)
    # 构建查询条件
    query = {
        'route_url': url,
        'request_time': {'$gte': start_time, '$lte': end_time}
    }
    query.update(where)

    # distinct 查询，根据 `uid` 去重
    unique_users = collection.distinct('uid', query)

    # 返回唯一用户数量
    return len(unique_users)


def get_count_pv(collection, date, url, where):
    start_time = datetime.strptime(date, '%Y-%m-%d').replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = datetime.strptime(date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=0)
    # 构建查询条件
    query = {
        'route_url': url,
        'request_time': {'$gte': start_time, '$lte': end_time}
    }
    query.update(where)
    print(query)
    return collection.count_documents(query)


def insert_mysql(day, data, conn, cursor):
    sql = """INSERT INTO `vh_data_statistics`.`vh_second_visit` (`day`, `pv_uv`) VALUES ('{}', '{}')""".format(day,
                                                                                                               json.dumps(
                                                                                                                   data,
                                                                                                                   ensure_ascii=False))
    cursor.execute(sql)
    conn.commit()


def start_second_visit_insert(client, conn, cursor):
    day = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    data_list = get_second_visit_statistics(day, client)
    insert_mysql(day, data_list, conn, cursor)
