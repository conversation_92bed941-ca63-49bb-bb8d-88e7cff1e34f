# 商品在售统计脚本
import pandas as pd
import pymysql
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings("ignore")


def rr_conn(rr_host, rr_port, rr_user, rr_password, database):
    """
    链接从数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rr_host, port=rr_port, user=rr_user, password=rr_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
    

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def get_last_day():
    last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    return last_day


def get_last_day_on_sale(day, rr_host, rr_port, rr_user, rr_password):
    """
    获取今天以前的在售商品数量
    :return:
    """
    conn = rr_conn(rr_host, rr_port, rr_user, rr_password, "vh_commodities")
    cursor = conn.cursor()
    # 闪购
    sql = f"""select count(1) 'onslae_count'  from vh_periods_flash where onsale_status = 2 and from_unixtime(onsale_time,'%Y-%m-%d') < '{day}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data:
        onslae_count_flash = data[0]
    else:
        onslae_count_flash = 0
    # 跨境
    sql = f"""select count(1) 'onslae_count'  from vh_periods_cross where onsale_status = 2 and from_unixtime(onsale_time,'%Y-%m-%d') < '{day}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data:
        onslae_count_cross = data[0]
    else:
        onslae_count_cross = 0
    # 秒发
    sql = f"""select count(1) 'onslae_count'  from vh_periods_second where onsale_status = 2 and from_unixtime(onsale_time,'%Y-%m-%d') < '{day}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data:
        onslae_count_second = data[0]
    else:
        onslae_count_second = 0
    # 尾货
    sql = f"""select count(1) 'onslae_count'  from vh_periods_leftover where onsale_status = 2 and from_unixtime(onsale_time,'%Y-%m-%d') < '{day}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data:
        onslae_count_left_over = data[0]
    else:
        onslae_count_left_over = 0
    return onslae_count_flash, onslae_count_cross, onslae_count_second, onslae_count_left_over


def exits_data(date, cursor):
    """
    验证是否已存在
    :param date: 日期
    :param cursor
    :return:
    """
    sql = f"""select * from vh_onsale_periods_count where `day` = '{date}'"""
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_onsale_periods_count where `day` = '{date}'"""
        cursor.execute(delete_sql)
    else:
        pass


def insert_mysql(insert_json, rm_host, rm_port, rm_user, rm_password):
    conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
    cursor = conn.cursor()
    day = insert_json['day']
    onslae_count_flash = insert_json['onslae_count_flash']
    onslae_count_cross = insert_json['onslae_count_cross']
    onslae_count_second = insert_json['onslae_count_second']
    onslae_count_left_over = insert_json['onslae_count_left_over']
    exits_data(day, cursor)
    sql = f"""insert into vh_onsale_periods_count 
             (`day`, `onslae_count_flash`, `onslae_count_cross`, `onslae_count_second`, `onslae_count_left_over`) 
             values('{day}',{onslae_count_flash},{onslae_count_cross},{onslae_count_second},{onslae_count_left_over})"""
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
    finally:
        conn.close()


def handler_onsale_count(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
        now_day = datetime.now().strftime("%Y-%m-%d")
        onslae_count_flash, onslae_count_cross, onslae_count_second, onslae_count_left_over = get_last_day_on_sale(
            now_day, rr_host, rr_port, rr_user, rr_password)
        insert_json = {"day": last_day,
                       "onslae_count_flash": onslae_count_flash,
                       "onslae_count_cross": onslae_count_cross,
                       "onslae_count_second": onslae_count_second,
                       "onslae_count_left_over": onslae_count_left_over}
        insert_mysql(insert_json, rm_host, rm_port, rm_user, rm_password)
        return 1
    except Exception as e:
        print(e)
        return -1
