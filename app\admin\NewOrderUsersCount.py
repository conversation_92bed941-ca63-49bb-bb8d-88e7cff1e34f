import pandas as pd
import warnings
from config import ChannelTypeConfig
import datetime
import json

warnings.filterwarnings("ignore")



def get_new_order_users(time,orders_conn):

    """
    统计前一天新购用户
    :param orders_conn:
    :return:
    """

    Cg=ChannelTypeConfig.channel_type_config
    tables_pool=[Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]]

    start_time=int((pd.to_datetime((time-datetime.timedelta(days=1)).strftime("%Y-%m-%d 00:00:00"))-datetime.timedelta(hours=8)).timestamp())
    end_time=int((pd.to_datetime((time-datetime.timedelta(days=1)).strftime("%Y-%m-%d 23:59:59"))-datetime.timedelta(hours=8)).timestamp())
    new_tabel_count=-1
    for table in tables_pool:
        new_tabel_count+=1
        new_order_users_sql=f"select distinct uid from `{table}` where sub_order_status in (1,2,3) and created_time between {start_time} and {end_time}"
        new_order_users_df=pd.read_sql_query(new_order_users_sql,orders_conn)
        if new_tabel_count==0:
            new_order_users_flash=new_order_users_df
        elif new_tabel_count==1:
            new_order_users_cross=new_order_users_df
        elif new_tabel_count==2:
            new_order_users_second=new_order_users_df
        elif new_tabel_count==3:
            new_order_users_tail=new_order_users_df
        else:
            pass
    new_order_users_main_df=pd.concat([new_order_users_flash,new_order_users_cross,new_order_users_second,new_order_users_tail],axis=0)
    new_order_users_main_df.drop_duplicates(keep="first",inplace=True)
    new_order_uid_list=new_order_users_main_df.uid.tolist()
    old_tabel_count=-1
    for table in tables_pool:
        old_tabel_count+=1
        if len(new_order_uid_list) == 0:
            old_order_users_df=pd.DataFrame(columns=["uid"])
        elif len(new_order_uid_list) == 1:
            old_order_users_sql=f"select distinct uid from `{table}` where sub_order_status in (1,2,3) and created_time < {start_time} and uid = {new_order_uid_list[0]}"
            old_order_users_df=pd.read_sql_query(old_order_users_sql,orders_conn)
        elif len(new_order_uid_list) > 1:
            old_order_users_sql=f"select distinct uid from `{table}` where sub_order_status in (1,2,3) and created_time < {start_time} and uid in {tuple(new_order_uid_list)}"
            old_order_users_df=pd.read_sql_query(old_order_users_sql,orders_conn)
        if old_tabel_count==0:
            old_order_users_flash=old_order_users_df
        elif old_tabel_count==1:
            old_order_users_cross=old_order_users_df
        elif old_tabel_count==2:
            old_order_users_second=old_order_users_df
        elif old_tabel_count==3:
            old_order_users_tail=old_order_users_df
        else:
            pass
    old_order_users_main_df=pd.concat([old_order_users_flash,old_order_users_cross,old_order_users_second,old_order_users_tail],axis=0)
    old_order_users_main_df.drop_duplicates(keep="first",inplace=True)
    now_old_uid=old_order_users_main_df.uid.tolist()
    if len(now_old_uid) == 0:
        new_order_user_count=new_order_users_main_df.uid.count()
    elif len(now_old_uid) == 1:
        new_order_user_count=new_order_users_main_df[new_order_users_main_df.uid != now_old_uid[0]].uid.count()
    if len(now_old_uid) > 1:
        new_order_user=new_order_users_main_df[new_order_users_main_df.uid.apply(lambda x:x not in tuple(now_old_uid))]
        new_order_user_count=new_order_user.uid.count()
    else:
        pass
    no_dict={
        "count": 0,
        "error_code": 0,
        "error_msg": ""
    }
    no_json=json.loads(json.dumps(no_dict,ensure_ascii=False))
    if len(old_order_users_main_df.uid.tolist()) == 0:
        return no_json
    new_order_user_dict ={
        "count": int(new_order_user_count),
    }
    new_order_user_json=json.loads(json.dumps(new_order_user_dict,ensure_ascii=False))
    return new_order_user_json

