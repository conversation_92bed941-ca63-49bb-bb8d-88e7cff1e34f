import pymysql
import pandas as pd
import warnings

warnings.filterwarnings("ignore")


#####登陆地址提取城市:
def process_ip_address(value):
    if type(value) == str:
        value = value.split("|")
        if len(value) == 1:
            res = value[0]
        elif len(value) == 2:
            res = f"{value[0]}{value[1]}"
        elif len(value) == 3 or len(value) == 4:
            res = f"{value[1]}{value[2]}"
        else:
            res = None
    else:
        res = value
    return res


def mysql_conn(host, port, user, password, database):
    """
    链接数据库
    :param rr_host:
    :param rr_port:
    :param rr_user:
    :param rr_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


#####获取注册来源
def get_reg_list(rr_host,rr_port,rr_user,rr_password):
    sql = f""" select case reg_from when 0 then '未知' when 1 then 'android' when 2 then 'ios' when 3 then '酒云网小程序' when 4 then 'h5' when 5 then 'PC' when 6 then '抖音小程序' when 7 then '后台添加' when 8 then '酒历小程序' when 9 then '公社小程序' when 10 then 'iPad' when 11 then '门店小程序' end as 'reg_from'  from vh_user group by `reg_from`"""
    users_conn = mysql_conn(rr_host,rr_port,rr_user,rr_password,"vh_user")
    dff = pd.read_sql_query(sql,users_conn)
    users_conn.close()
    return dff


#####获取ip地址
def get_ip_massage(rr_host,rr_port,rr_user,rr_password):
    sql = f""" select ip_address from vh_user_login_behavior group by `ip_address`"""
    statistics_conn = mysql_conn(rr_host,rr_port,rr_user,rr_password,"vh_data_statistics")
    dff = pd.read_sql_query(sql,statistics_conn)
    statistics_conn.close()
    dff["ip_address"] = dff.ip_address.apply(lambda x:process_ip_address(x))
    dff = dff.drop_duplicates(subset="ip_address",keep="first").reset_index(drop=True)
    return dff


def exits_data(args,cursor,exits_type):
    """
    验证是否已存在
    :param date: 日期
    :param is_liquor:
    :param sell_type
    :return:
    """
    if exits_type == 1:
        sql = f"""select reg_from from vh_user_regfrom where `reg_from` = '{args[0]}'"""
        cursor.execute(sql)
        data = cursor.fetchone()
        if data is not None:
            delete_sql = f"""delete from vh_user_regfrom where `reg_from` = '{args[0]}'"""
            cursor.execute(delete_sql)
        else:
            pass
    elif exits_type == 2:
        sql = f"""select ip_address from vh_ipaddress_list where `ip_address` = '{args[0]}'"""
        cursor.execute(sql)
        data = cursor.fetchone()
        if data is not None:
            delete_sql = f"""delete from vh_ipaddress_list where `ip_address` = '{args[0]}'"""
            cursor.execute(delete_sql)
        else:
            pass


def insert_mysql(*args):
    cursor=args[1]
    exits_type=args[2]
    exits_data(args,cursor=cursor,exits_type=exits_type)
    if args[2] == 1:
        sql = f"""insert into vh_user_regfrom 
                 (`reg_from`) values ('{args[0]}')"""
    elif args[2] == 2:
        sql = f"""insert into vh_ipaddress_list 
                 (`ip_address`) values ('{args[0]}')"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")


def handler_reg_from(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = mysql_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        dff = get_reg_list(rr_host, rr_port, rr_user, rr_password)
        data_list = dff.values.tolist()
        for data in data_list:
            insert_mysql(data[0],cursor,1)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()



def handler_ipaddress(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = mysql_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        dff = get_ip_massage(rr_host, rr_port, rr_user, rr_password)
        data_list = dff.values.tolist()
        for data in data_list:
            insert_mysql(data[0],cursor,2)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()
        
        
        
def ip_reg_handler_main(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password):
    try:
        handler_reg_from(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        handler_ipaddress(rr_host, rr_port, rr_user, rr_password,rm_host, rm_port, rm_user, rm_password)
        return 1
    except Exception as e:
        print(e)
        return -1

