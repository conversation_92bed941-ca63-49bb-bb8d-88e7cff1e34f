#!/usr/bin/env python
# coding: utf-8
import pymysql
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta
import base64
from io import BytesIO
import requests
import json
import hashlib
import warnings
from decimal import Decimal
from config import DingtalkAccessToken
from matplotlib.font_manager import FontProperties

font = FontProperties(fname="/var/www/html/simhei.ttf")
# font = FontProperties(fname="/System/Library/Fonts/STHeiti Light.ttc")
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings("ignore")


def send_wechat_image_message(image_data, md5, key):
    # Define the endpoint URL
    url = f'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}'

    # Define the JSON payload with the message type set to 'image'
    payload = {
        'msgtype': 'image',
        'image': {
            'base64': image_data,
            'md5': md5
        }
    }

    # Convert the payload to a JSON string
    json_payload = json.dumps(payload)

    # Define the headers and make the POST request
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, headers=headers, data=json_payload)

    # Return the response status code and content
    return response.status_code,response.content

def plot_hourly_sales():
    conn = pymysql.connect(host='rr-8vbwq6b79kqq5sj26.mysql.zhangbei.rds.aliyuncs.com', user='vinehoodev', password='ziAJWCLwOVs29NbB', db='vh_orders')

    now = datetime.now()
    if now.hour == 0:
        now = now - timedelta(days=1)
        date_str = "'" + now.strftime('%Y-%m-%d') + "'"
    else:
        date_str = 'CURDATE()'

    cursor = conn.cursor()
    cursor.execute(f"SELECT FROM_UNIXTIME(payment_time, '%H') AS payment_hour, SUM(payment_amount) AS total_amount FROM vh_cross_order WHERE DATE(FROM_UNIXTIME(payment_time)) = {date_str} AND sub_order_status in (1,2,3) GROUP BY FROM_UNIXTIME(payment_time, '%Y-%m-%d %H') ORDER BY payment_hour;")
    result = cursor.fetchall()
    cursor.close()

    df_cross = pd.DataFrame(list(result), columns=['payment_hour', 'total_amount'])
    df_cross = df_cross.sort_values(by='payment_hour')
    df_cross = df_cross.set_index('payment_hour')

    cursor = conn.cursor()
    cursor.execute(f"SELECT FROM_UNIXTIME(payment_time, '%H') AS payment_hour, SUM(payment_amount) AS total_amount FROM vh_second_order WHERE DATE(FROM_UNIXTIME(payment_time)) = {date_str} AND sub_order_status in (1,2,3) GROUP BY FROM_UNIXTIME(payment_time, '%Y-%m-%d %H') ORDER BY payment_hour;")
    result = cursor.fetchall()
    cursor.close()

    df_second = pd.DataFrame(list(result), columns=['payment_hour', 'total_amount'])
    df_second = df_second.sort_values(by='payment_hour')
    df_second = df_second.set_index('payment_hour')

    cursor = conn.cursor()
    cursor.execute(f"""
    SELECT FROM_UNIXTIME(payment_time, '%H') AS payment_hour, SUM(payment_amount) AS total_amount 
    FROM (
        SELECT payment_time, payment_amount, period, sub_order_status 
        FROM vh_flash_order 
        UNION ALL 
        SELECT payment_time, payment_amount, period, sub_order_status 
        FROM vh_tail_order
    ) combined_orders 
    WHERE DATE(FROM_UNIXTIME(payment_time)) = {date_str} 
    AND period NOT IN (
        SELECT id FROM vh_commodities.vh_periods_flash 
        WHERE product_category LIKE '%白酒%' 
        OR product_category LIKE '%酱香型%' 
        OR product_category LIKE '%浓香型%' 
        OR product_category LIKE '%清香型%' 
        OR product_category LIKE '%米香型%' 
        OR product_category LIKE '%凤香型%' 
        OR product_category LIKE '%其他香型%'
    ) 
    AND sub_order_status in (1,2,3) 
    GROUP BY FROM_UNIXTIME(payment_time, '%Y-%m-%d %H') 
    ORDER BY payment_hour;
    """)
    result = cursor.fetchall()
    cursor.close()

    df_flash_non_wine = pd.DataFrame(list(result), columns=['payment_hour', 'total_amount'])
    df_flash_non_wine = df_flash_non_wine.sort_values(by='payment_hour')
    df_flash_non_wine = df_flash_non_wine.set_index('payment_hour')

    cursor = conn.cursor()
    cursor.execute(f"""
    SELECT FROM_UNIXTIME(payment_time, '%H') AS payment_hour, SUM(payment_amount) AS total_amount 
    FROM vh_flash_order 
    WHERE DATE(FROM_UNIXTIME(payment_time)) = {date_str} 
    AND period IN (
        SELECT id FROM vh_commodities.vh_periods_flash 
        WHERE product_category LIKE '%白酒%' 
        OR product_category LIKE '%酱香型%' 
        OR product_category LIKE '%浓香型%' 
        OR product_category LIKE '%清香型%' 
        OR product_category LIKE '%米香型%' 
        OR product_category LIKE '%凤香型%' 
        OR product_category LIKE '%其他香型%'
    ) 
    AND sub_order_status in (1,2,3) 
    GROUP BY FROM_UNIXTIME(payment_time, '%Y-%m-%d %H') 
    ORDER BY payment_hour;
    """)
    result = cursor.fetchall()
    cursor.close()

    df_flash_wine = pd.DataFrame(list(result), columns=['payment_hour', 'total_amount'])
    df_flash_wine = df_flash_wine.sort_values(by='payment_hour')
    df_flash_wine = df_flash_wine.set_index('payment_hour')

    conn.close()
    
    hours = [f"{i:02d}" for i in range(24)]

    for hour in hours:
        if hour not in df_flash_wine.index:
            df_flash_wine.loc[hour] = [Decimal('0.00')]
    
    for hour in hours:
        if hour not in df_flash_non_wine.index:
            df_flash_non_wine.loc[hour] = [Decimal('0.00')]
        
    for hour in hours:
        if hour not in df_cross.index:
            df_cross.loc[hour] = [Decimal('0.00')]
        
    for hour in hours:
        if hour not in df_second.index:
            df_second.loc[hour] = [Decimal('0.00')]

    df_all = pd.concat([df_cross, df_second, df_flash_non_wine, df_flash_wine], axis=1)
    df_all.columns = ['Cross-border', 'Second', 'Flash (non-white wine)', 'Flash (white wine)']

    if now.hour == 0:
        df_all = df_all.sort_index()
    else:
        df_all = df_all[df_all.index.astype(int) < now.hour]
        df_all = df_all.sort_index()

    # 绘制折线图
    plt.figure(figsize=(16, 10))

    # 绘制不同订单类型的折线图
    plt.plot(df_all.index, df_all['Cross-border'], label='跨境', color='#1f77b4', marker='o', linewidth=2, markersize=8)
    plt.plot(df_all.index, df_all['Second'], label='秒发', color='#ff7f0e', marker='o', linewidth=2, markersize=8)
    plt.plot(df_all.index, df_all['Flash (non-white wine)'], label='闪购(不含白酒)', color='#2ca02c', marker='o', linewidth=2, markersize=8)
    plt.plot(df_all.index, df_all['Flash (white wine)'], label='闪购(仅白酒)', color='#d62728', marker='o', linewidth=2, markersize=8)

    # 设置图表标签和标题
    plt.xlabel('小时', fontproperties=font, fontsize=18)
    plt.ylabel('销售额', fontproperties=font, fontsize=18)
    plt.title('小时销售趋势', fontproperties=font, fontsize=24)
    plt.legend(prop=font, fontsize=16)

    # 设置刻度标签的字体大小
    plt.tick_params(axis='both', which='major', labelsize=14)

    # 添加网格和布局调整
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    # 获取上一个小时的数据，并在标签下方显示
    last_hour = (now - timedelta(hours=1)).strftime('%H')
    if last_hour in df_all.index:
        cross_border_last = df_all.at[last_hour, 'Cross-border']
        second_last = df_all.at[last_hour, 'Second']
        flash_non_wine_last = df_all.at[last_hour, 'Flash (non-white wine)']
        flash_wine_last = df_all.at[last_hour, 'Flash (white wine)']
        
        # 计算上一个小时的销售额总和
        total_last = cross_border_last + second_last + flash_non_wine_last + flash_wine_last
        
        # 在图表底部显示上一个小时的销售额，包括总和
        # 增加字体大小到 30
        plt.figtext(0.5, -0.14,
                    f"上一个小时销售额（{last_hour}:00:00）\n跨境: {cross_border_last:.2f} | 秒发: {second_last:.2f} | 闪购(不含白酒): {flash_non_wine_last:.2f} | 闪购(仅白酒): {flash_wine_last:.2f}\n总计: {total_last:.2f}",
                    horizontalalignment='center', fontproperties=font, fontsize=30, wrap=True)

    plt.tight_layout()
    
    buffer = BytesIO()
    plt.savefig(buffer, format='jpg', bbox_inches='tight')
    buffer.seek(0)
    img_content = buffer.getvalue()
    img_md5 = hashlib.md5(img_content).hexdigest()
    img_str = base64.b64encode(buffer.read()).decode('utf-8')
    
    # send_wechat_image_message(image_data=img_str, md5=img_md5, key='a5df4c1d-0f20-4f0c-8410-c91dca6d7695')
    send_wechat_image_message(image_data=img_str, md5=img_md5, key=DingtalkAccessToken.robot_image_token)

# plot_hourly_sales()
