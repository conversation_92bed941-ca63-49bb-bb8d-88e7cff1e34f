# -*- coding: utf-8 -*-
# 注册用户统计
import json
import requests
import warnings
import numpy as np
import pandas as pd
from datetime import datetime
from dateutil.relativedelta import relativedelta

warnings.filterwarnings("ignore")
response = requests.session()

        

def get_last_month(date):
    date = datetime(int(date.split("-")[0]), int(date.split("-")[1]), 1)
    prev_month = (date - relativedelta(months=1)).strftime('%Y-%m')
    return prev_month
    
    

def get_channel_user_statistics(count_key, date, conn_statics):
    """
    获取注册用户统计数据
    :param count_key:统计字段
    :param date:日期
    :param conn_statics
    :return:
    """
    if count_key == "day":
        sql = f""" SELECT * FROM `vh_sales_analysis_daily_user` WHERE month = '{date}' """
        df = pd.read_sql(sql, conn_statics)
        df = df.drop('id', axis=1).drop('month', axis=1)
        res_json = json.loads(df.to_json(orient='records'))
    else:
        sql = f""" SELECT a.id,a.date,a.registered_users,a.growth_rate_users,a.order_nums,a.purchase_amount,a.growth_rate_amount,a.monthly_buyuers,aa.count 'monthly_active_users',a.data_year FROM vh_sales_analysis_monthly_user AS a LEFT JOIN vh_active_monthly_user_count AS aa ON a.`data_year` = aa.`year` AND RIGHT(a.date,2) = aa.`month` WHERE a.data_year='{date}' ORDER BY a.`id`  """
        df = pd.read_sql(sql, conn_statics)
        df = df.drop('id', axis=1).drop('data_year', axis=1)
        res_json = json.loads(df.to_json(orient='records'))
    return res_json


def get_Merchant_Second_user_statistics(count_key, date, delivery_store_id, merchant_id,conn_statics,conn_orders):
    if count_key == "day":
        if delivery_store_id == "":
            if merchant_id == "":
                sql = f""" select date,week,merchant_second_sales,registrants_number,order_users,new_users,buyuers from  vh_merchant_second_daily_user where month = '{date}'"""
            else:
                sql = f""" select date,week,merchant_second_sales,registrants_number,order_users,new_users,buyuers from  vh_merchant_second_daily_user where month = '{date}' and merchant_id = {merchant_id}"""
        else:
            if merchant_id == "":
                sql = f""" select date,week,merchant_second_sales,registrants_number,order_users,new_users,buyuers from  vh_merchant_second_daily_user where month = '{date}' and delivery_store_id = {delivery_store_id}"""
            else:
                sql = f""" select date,week,merchant_second_sales,registrants_number,order_users,new_users,buyuers from  vh_merchant_second_daily_user where month = '{date}' and merchant_id = {merchant_id} and delivery_store_id = {delivery_store_id}"""
        df = pd.read_sql_query(sql,conn_statics)
        dff = df.groupby(by=["date","week"],as_index=False).agg({"merchant_second_sales":sum,"registrants_number":sum,"order_users":sum,"new_users":sum,"buyuers":sum})
        dff["date"] = dff.date.apply(lambda x:str(x))
    elif count_key == "month":
        month_sql = f""" select month from vh_merchant_second_daily_user where `year` = '{date}' group by 'month'"""
        month_list = pd.read_sql_query(month_sql,conn_statics).month.tolist()
        if len(month_list) == 0:
            return {"data":[],"total":0}
        if delivery_store_id == "":
            if merchant_id == "":
                sql = f""" select month,merchant_second_sales,registrants_number,order_users,buyuers from  vh_merchant_second_daily_user where (`year` = '{date}' or `month` = '{get_last_month(month_list[0])}')"""
            else:
                sql = f""" select month,merchant_second_sales,registrants_number,order_users,buyuers from  vh_merchant_second_daily_user where (`year` = '{date}' or `month` = '{get_last_month(month_list[0])}') and merchant_id = {merchant_id}"""
        else:
            if merchant_id == "":
                sql = f""" select month,merchant_second_sales,registrants_number,order_users,buyuers from  vh_merchant_second_daily_user where (`year` = '{date}' or `month` = '{get_last_month(month_list[0])}') and delivery_store_id = {delivery_store_id}"""
            else:
                sql = f""" select month,merchant_second_sales,registrants_number,order_users,buyuers from  vh_merchant_second_daily_user where (`year` = '{date}' or `month` = '{get_last_month(month_list[0])}') and merchant_id = {merchant_id} and delivery_store_id = {delivery_store_id}"""
        df = pd.read_sql_query(sql,conn_statics)
        dff = df.groupby(by=["month"],as_index=False).agg({"merchant_second_sales":sum,"registrants_number":sum,"order_users":sum,"buyuers":sum})
        if delivery_store_id == "" and merchant_id == "":
            order_sql = f""" select from_unixtime(created_time,"%Y-%m") as 'month',count(sub_order_no) as 'order_counts' from vh_merchant_second_order where (from_unixtime(created_time,"%Y") = '{date}' or from_unixtime(created_time,"%Y-%m") = '{get_last_month(month_list[0])}') group by `month`"""
        elif delivery_store_id != "" and merchant_id == "":
            order_sql = f""" select from_unixtime(created_time,"%Y-%m") as 'month',count(sub_order_no) as 'order_counts' from vh_merchant_second_order where (from_unixtime(created_time,"%Y") = '{date}' or from_unixtime(created_time,"%Y-%m") = '{get_last_month(month_list[0])}') and delivery_store_id = {delivery_store_id} group by `month`"""
        elif delivery_store_id == "" and merchant_id != "":
            order_sql = f""" select from_unixtime(created_time,"%Y-%m") as 'month',count(sub_order_no) as 'order_counts' from vh_merchant_second_order where (from_unixtime(created_time,"%Y") = '{date}' or from_unixtime(created_time,"%Y-%m") = '{get_last_month(month_list[0])}') and merchant_id = {merchant_id} group by `month`"""
        else:
            order_sql = f""" select from_unixtime(created_time,"%Y-%m") as 'month',count(sub_order_no) as 'order_counts' from vh_merchant_second_order where (from_unixtime(created_time,"%Y") = '{date}' or from_unixtime(created_time,"%Y-%m") = '{get_last_month(month_list[0])}') and merchant_id = {merchant_id} and delivery_store_id = {delivery_store_id} group by `month`"""
        dff = pd.merge(dff,pd.read_sql_query(order_sql,conn_orders),how="left",on="month").fillna(0)
        dff['previous_day_users'] = dff['registrants_number'].shift(1)
        dff['growth_rate_users'] = (dff['registrants_number'] - dff['previous_day_users']) / dff['previous_day_users']
        dff['previous_rate_amount'] = dff['merchant_second_sales'].shift(1)
        dff['growth_rate_amount'] = (dff['merchant_second_sales'] - dff['previous_rate_amount']) / dff['previous_rate_amount']
        if dff.empty:
            return {"data":[],"total":0}
        else:
            dff.drop(index=0,axis=0,inplace=True)
            dff.fillna(0,inplace=True)
            dff['growth_rate_users'] = dff['growth_rate_users'].replace([np.inf, -np.inf], 1)
            dff['growth_rate_amount'] = dff['growth_rate_amount'].replace([np.inf, -np.inf], 1)
            dff['growth_rate_users'] = dff.growth_rate_users.apply(lambda x:round(x * 100,2))
            dff['growth_rate_amount'] = dff.growth_rate_amount.apply(lambda x:round(x * 100,2))
            dff = dff[["month","registrants_number","growth_rate_users","order_counts","merchant_second_sales","growth_rate_amount","buyuers"]]
            dff["month"] = dff.month.apply(lambda x:str(x))
    res_json = json.loads(dff.to_json(orient="records"))
    return res_json


def get_registered_user_statistics(count_key, date, channel_type,delivery_store_id, merchant_id,conn_statics,conn_orders):
    if channel_type == "1":
        res_json = get_channel_user_statistics(count_key, date, conn_statics)
    elif channel_type == "2":
        res_json = get_Merchant_Second_user_statistics(count_key, date, delivery_store_id, merchant_id,conn_statics,conn_orders)
    return res_json