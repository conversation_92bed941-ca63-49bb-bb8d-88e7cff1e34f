import pymysql
import pandas as pd
from datetime import datetime,timedelta,timezone
from pymongo import MongoClient
import warnings
import time
import requests
import json
from app.DButils.MysqlHelper import conn_str
from app.CommonLibraries.getConfig import get_config
from sqlalchemy import create_engine

warnings.filterwarnings("ignore")


rm_config = get_config("db.data.write.v3_all", "vinehoo.accounts")
rm_host = rm_config['host']
rm_port = rm_config['port']
rm_user = rm_config['user']
rm_password = rm_config['password']


def get_last_day():
    last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    return last_day


def get_user_ip(gte_time, lte_time) -> pd.DataFrame:
    try:
        client = MongoClient('**************************************************************************************************')
        result = client['vinehoo_v3']['gateway_logs'].aggregate([
            {
                '$match': {
                    'request_time': {
                        '$gte': datetime(gte_time.tm_year, gte_time.tm_mon, gte_time.tm_mday, gte_time.tm_hour,gte_time.tm_min,gte_time.tm_sec, tzinfo=timezone.utc),
                        '$lte': datetime(lte_time.tm_year, lte_time.tm_mon, lte_time.tm_mday, lte_time.tm_hour,lte_time.tm_min,lte_time.tm_sec, tzinfo=timezone.utc)
                    },
                    'logon': True
                }
            },
            {
                '$sort': {
                    'request_time': -1
                }
            },
            {
                '$group': {
                    '_id': '$uid',
                    'ip': {'$first': '$ip'},
                    'request_time': {'$first': '$request_time'}
                }
            },
            {
                '$project': {
                    '_id': 0,
                    'uid': '$_id',
                    'ip': 1,
                    'request_time': 1
                }
            }
        ])
        df = pd.DataFrame(list(result))
        return df
    except Exception as e:
        print(f"PV获取失败: {e}")
        return pd.DataFrame()
    
    
def get_address(ip_list):
    try:
        url = "https://test-wine.wineyun.com/nali-ip/services/v3/ip/check"

        payload = {
            "ips": ip_list,
        }
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request(
            "POST", url, headers=headers, data=json.dumps(payload))

        ret = response.json()["data"]
        key_list = []
        ret_list = []
        for i,each in zip(ret,ip_list):
            key_list.append(each)
            ret_list.append(i[each])
        ret_df = pd.DataFrame({"ip":key_list,"ip_address":ret_list})
        return ret_df
    except Exception as e:
        print(e)
        return response.json()
    
    
    
def process_ip_address(value):
    if len(value) == 1:
        res = value[0]
    elif len(value) == 2:
        res = f"{value[0]}{value[1]}"
    else:
        res = value[2]
    return res

def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine

def rm_conn(rm_host, rm_port, rm_user, rm_password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=rm_host, port=rm_port, user=rm_user, password=rm_password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
        
        
def exits_data(uid,cursor):
    """
    验证是否已存在
    :param date: 日期
    :param is_liquor:
    :param sell_type
    :return:
    """
    sql = f"""select uid from vh_user_login_behavior where uid = {uid} """
    cursor.execute(sql)
    data = cursor.fetchone()
    if data is not None:
        delete_sql = f"""delete from vh_user_login_behavior where uid = {uid} """
        cursor.execute(delete_sql)
    else:
        pass
    
    
def insert_mysql(*args):
    cursor=args[3]
    exits_data(uid=args[1],cursor=cursor)
    sql = f"""INSERT INTO vh_user_login_behavior
             (`request_time`,`uid`,`ip_address`) 
             VALUES ('{args[0]}',{args[1]},'{args[2]}')"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
        
        
def handler_new_user_transform_daily(dff,rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = rm_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        data_list = dff.values.tolist()
        for data in data_list:
            if data[1] == 0:
                return '无用户注册信息'
            else:
                insert_mysql(data[0], data[1], data[2],cursor)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()

def get_ip_address_dff(date):
    start_timestamp,end_timestamp = int(datetime.strptime(f"{date} 00:00:00","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"{date} 23:59:59","%Y-%m-%d %H:%M:%S").timestamp())
    gte_time = time.localtime(start_timestamp)
    lte_time = time.localtime(end_timestamp)
    ip_df = get_user_ip(gte_time, lte_time)
    ip_address_df = get_address(list(set(ip_df.ip.tolist())))
    ip_address_df = pd.merge(ip_df,ip_address_df,how="left",on="ip")
    ip_address_df = ip_address_df.drop("ip",axis=1)
    return ip_address_df

def get_ip_address_begin():
    dff = get_ip_address_dff(get_last_day())
    return handler_new_user_transform_daily(dff,rm_host, rm_port, rm_user, rm_password)

