import urllib

import pymongo

from app.CommonLibraries.getConfig import get_config

config_info = get_config(data_id="db.mongodb", group="vinehoo.accounts")
mongo_client_master = config_info['mongo_client_master']
mongo_port = config_info['mongo_port']
mongo_auth = urllib.parse.quote_plus(config_info['mongo_auth'])
mongo_password = urllib.parse.quote_plus(config_info['mongo_password'])


def conn_mongo():
    """
    链接数据库
    :return:
    """
    try:
        mongo_cli = pymongo.MongoClient(
            'mongodb://%s:%s@%s:%s' % (
                mongo_auth, mongo_password, mongo_client_master, mongo_port))
        return mongo_cli
    except ConnectionError as ce:
        print(f"连接mogodb数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
