import requests
from config import ConfigCenter
from app.CommonLibraries.LogRecord import record_error


class AccessConfiguration:
    config_url_route = "config/v3/nacos/readstring"

    def get_config_info(self):
        """
        获取配置信息
        :return:
        """
        url = f"{ConfigCenter.config_url['config_url']}/{self.config_url_route}"
        headers = {
            "vinehoo-client": "py3-v3-data-analysis",
            "vinehoo-client-version": "1.0.0"
        }
        res = requests.get(url=url, headers=headers)
        if res.json()['error_code'] != 0:
            record_error(error_function="get_config_info", error_info="获取配置信息失败")
            raise Exception('配置信息获取失败')
        else:
            return res.json()['data']
