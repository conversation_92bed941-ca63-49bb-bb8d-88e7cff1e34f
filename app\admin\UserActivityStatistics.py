# -*- coding: utf-8 -*-
# 用户时段统计
from dateutil import parser
import pymongo
import pandas as pd
import requests
from app.CommonLibraries.getConfig import get_config
import urllib
from datetime import datetime, timezone, timedelta
import json
response = requests.session()


def get_user_activity_statistics(date, cursor):
    """
    获取用户时段统计数据
    :param date:日期
    :return:
    """
    sql = f"select active_frame from vh_active_user_count where day = '{date}'"
    cursor.execute(sql)
    data = cursor.fetchone()[0]
    data_list = json.loads(data)
    return data_list
