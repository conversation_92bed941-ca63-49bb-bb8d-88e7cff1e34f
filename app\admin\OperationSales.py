from app.CommonLibraries.DateToWeek import date_to_week
from datetime import datetime, timedelta
import pandas as pd
from config import DingtalkAccessToken
import json
import requests
import base64
import warnings
from app.CommonLibraries.GetNowTime import get_now_time
from app.admin.OperationSales_plot import plot_hourly_sales

warnings.filterwarnings("ignore")


def get_param_time():
    """
    获取参数时间
    :return:
    """
    now_time = datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S')
    now_time_hour = now_time.split(' ')[1].split(':')[0]
    if now_time_hour == '00':
        now_time = str(datetime.strptime(now_time.split(' ')[0], '%Y-%m-%d') + timedelta(days=-1))
    return now_time

'''
    获取时间区间的where条件语句
'''
def getTimeRangeWhereConditions(cur_date):
    return f" payment_time >= unix_timestamp('{cur_date} 00:00:00') AND payment_time <= unix_timestamp('{cur_date} 23:59:59') "
    

def get_operation_sales(now_time, conn_statics_cursor, conn_orders_cursor, conn_commodities_cursor, conn_orders,
                        conn_commodities):
    """
    获取运营销售数据
    :param now_time:
    :param conn_statics_cursor:
    :param conn_orders_cursor:
    :param conn_commodities_cursor:
    :param conn_orders:
    :param conn_commodities:
    :return:
    """
    date = str(now_time).split(' ')[0]
    datetime_now_date = datetime.strptime(date, '%Y-%m-%d')
    datetime_last_date = str(datetime_now_date + timedelta(days=-1))
    last_date = datetime_last_date.split(' ')[0]
    datetime_next_date = str(datetime_now_date + timedelta(days=1))
    next_date = datetime_next_date.split(' ')[0]
    week = date_to_week(str(now_time).split(' ')[0])
    end_date = f"{date.split('-')[1]}-{date.split('-')[2]}"
    curtime = f"{now_time.split(' ')[1].split(':')[0]}:00"
    if now_time.split(' ')[1].split(':')[0] == '00':
        cur = datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S')
        curtime = f"{cur.split(' ')[1].split(':')[0]}:00"
        end_date = f"{next_date.split('-')[1]}-{next_date.split('-')[2]}"
    else:
        pass
    # 获取运营人员及计划数据
    sql_last = f"""SELECT operation_duty,operation_duty_assist FROM `vh_sales_analysis_plan` WHERE date = '{last_date}'"""
    conn_statics_cursor.execute(sql_last)
    last_operation_info = conn_statics_cursor.fetchone()
    if last_operation_info is None:
        last_operation = ""
        last_operation_assist = ""
    else:
        last_operation = last_operation_info[0]
        last_operation_assist = last_operation_info[1]
    if date[0:4] =="2022":
        sql = f"""SELECT operation_duty,sales_plan,single_plan,cross_border_plan,second_delivery_plan,remarks,operation_duty_assist FROM `vh_sales_analysis_plan` WHERE date = '{date}'"""
    else:
        sql = f"""SELECT operation_duty,flash_redwine_sales_plan,flash_baijiu_sales_plan,main_single_plan 'single_plan',cross_border_plan,second_delivery_plan,remarks,operation_duty_assist FROM `vh_sales_analysis_plan` WHERE date = '{date}'"""
    conn_statics_cursor.execute(sql)
    operation_info = conn_statics_cursor.fetchone()
    if operation_info is None and date[0:4] =="2022":
        operation = ""
        sg_plan_saleamount = 0
        plan_amount = 0
        kj_plan_saleamount = 0
        mf_plan_saleamount = 0
        memo = ""
        operation_assist = ""
    elif operation_info is not None and date[0:4] =="2022":
        operation = operation_info[0]
        sg_plan_saleamount = float(operation_info[1])
        plan_amount = operation_info[2]
        kj_plan_saleamount = float(operation_info[3])
        mf_plan_saleamount = float(operation_info[4])
        memo = operation_info[5]
        operation_assist = operation_info[6]
    elif operation_info is None and date[0:4] !="2022":
        operation = ""
        sg_fbj_plan_saleamount = 0
        sg_bj_plan_saleamount = 0
        plan_amount = 0
        kj_plan_saleamount = 0
        mf_plan_saleamount = 0
        memo = ""
        operation_assist = ""
    elif operation_info is not None and date[0:4] !="2022":
        operation = operation_info[0]
        sg_fbj_plan_saleamount =float(operation_info[1])
        sg_bj_plan_saleamount =float(operation_info[2])
        plan_amount = operation_info[3]
        kj_plan_saleamount = float(operation_info[4])
        mf_plan_saleamount = float(operation_info[5])
        memo = operation_info[6]
        operation_assist = operation_info[7]
    else:
        pass
    sql_next = f"""SELECT operation_duty,operation_duty_assist FROM `vh_sales_analysis_plan` WHERE date = '{next_date}'"""
    conn_statics_cursor.execute(sql_next)
    next_operation_info = conn_statics_cursor.fetchone()
    if next_operation_info is None:
        next_operation = ""
        next_operation_assist = ""
    else:
        next_operation = next_operation_info[0]
        next_operation_assist = next_operation_info[1]
        
    where_timerange = getTimeRangeWhereConditions(date)
        
    # 闪购总销售数据
    sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""
    conn_orders_cursor.execute(sql)
    flash_sales_info = conn_orders_cursor.fetchone()
    sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""
    conn_orders_cursor.execute(sql)
    tail_sales_info = conn_orders_cursor.fetchone()
    if flash_sales_info is None:
        shangou_amount_total = 0
        shangou_nums_total = 0
    else:
        shangou_amount_total = float(flash_sales_info[0])
        shangou_nums_total = flash_sales_info[1]
    if tail_sales_info is None:
        shangou_amount_total = shangou_amount_total
        shangou_nums_total = shangou_nums_total
    else:
        shangou_amount_total = shangou_amount_total + float(tail_sales_info[0])
        shangou_nums_total = shangou_nums_total + tail_sales_info[1]
    # 秒发销售数据
    sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'miaofa' FROM `vh_second_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""
    conn_orders_cursor.execute(sql)
    second_sales_info = conn_orders_cursor.fetchone()
    if second_sales_info is None:
        miaofa = 0
    else:
        miaofa = float(second_sales_info[0])
    # 跨境销售数据
    sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'miaofa' FROM `vh_cross_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""
    conn_orders_cursor.execute(sql)
    cross_sales_info = conn_orders_cursor.fetchone()
    if cross_sales_info is None:
        kuajing = 0
    else:
        kuajing = float(cross_sales_info[0])
        
    # 秒发订单数量
    sql = f"""SELECT count(1) as orders_count FROM `vh_second_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""
    conn_orders_cursor.execute(sql)
    second_sales_orders_count = conn_orders_cursor.fetchone()
    if second_sales_orders_count is None:
        miaofa_sales_count = 0
    else:
        miaofa_sales_count = second_sales_orders_count[0]
        
    # 跨境订单数量
    sql = f"""SELECT count(1) as orders_count FROM `vh_cross_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""
    conn_orders_cursor.execute(sql)
    kuajing_sales_orders_count = conn_orders_cursor.fetchone()
    if second_sales_orders_count is None:
        kuajing_sales_count = 0
    else:
        kuajing_sales_count = kuajing_sales_orders_count[0]

        
        
    # 闪购所有下单商品
    sql = f"""SELECT distinct period FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""
    df_order = pd.read_sql(sql, conn_orders)
    sql_tail = f"""SELECT distinct period FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3)"""

    df_order_tail = pd.read_sql(sql_tail, conn_orders)
    if df_order.empty and df_order_tail.empty:
        shangou_amount = 0
        shangou_nums = 0
        sg_baijiu_amount = 0
        sg_baijiu_nums = 0
        tail_amount = 0
        tail_nums = 0
        baijiu_tail_amount_not = 0
        baijiu_tail_nums_not = 0
    elif df_order.empty and df_order_tail.empty is False:
        shangou_amount = 0
        shangou_nums = 0
        sg_baijiu_amount = 0
        sg_baijiu_nums = 0
        tuple_tail_ids = tuple(df_order_tail['period'].tolist())
        if len(tuple_tail_ids) == 1:
            sql_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id = {tuple_tail_ids[0]} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id = {tuple_tail_ids[0]} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        else:
            sql_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id in {tuple_tail_ids} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id in {tuple_tail_ids} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        df_commodities_tail = pd.read_sql(sql_tail, conn_commodities)
        df_commodities_not_tail = pd.read_sql(sql_not_tail, conn_commodities)
        if df_commodities_tail.empty:
            tail_amount = 0
            tail_nums = 0
        else:
            re_periods = tuple(df_commodities_tail['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                tail_amount = 0
                tail_nums = 0
            else:
                tail_amount = float(flash_sales_info[0])
                tail_nums = flash_sales_info[1]
        if df_commodities_not_tail.empty:
            baijiu_tail_amount_not = 0
            baijiu_tail_nums_not = 0
        else:
            re_periods = tuple(df_commodities_not_tail['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                baijiu_tail_amount_not = 0
                baijiu_tail_nums_not = 0
            else:
                baijiu_tail_amount_not = float(flash_sales_info[0])
                baijiu_tail_nums_not = flash_sales_info[1]
    elif df_order.empty is False and df_order_tail.empty:
        tail_amount = 0
        tail_nums = 0
        baijiu_tail_amount_not = 0
        baijiu_tail_nums_not = 0
        tuple_ids = tuple(df_order['period'].tolist())
        if len(tuple_ids) == 1:
            sql = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id = {tuple_ids[0]} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id = {tuple_ids[0]} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        else:
            sql = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id in {tuple_ids} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id in {tuple_ids} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        df_commodities = pd.read_sql(sql, conn_commodities)
        df_commodities_not = pd.read_sql(sql_not, conn_commodities)
        if df_commodities.empty:
            shangou_amount = 0
            shangou_nums = 0
        else:
            re_periods = tuple(df_commodities['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                shangou_amount = 0
                shangou_nums = 0
            else:
                shangou_amount = float(flash_sales_info[0])
                shangou_nums = flash_sales_info[1]
        if df_commodities_not.empty:
            sg_baijiu_amount = 0
            sg_baijiu_nums = 0
        else:
            re_periods = tuple(df_commodities_not['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                sg_baijiu_amount = 0
                sg_baijiu_nums = 0
            else:
                sg_baijiu_amount = float(flash_sales_info[0])
                sg_baijiu_nums = flash_sales_info[1]
    else:
        tuple_ids = tuple(df_order['period'].tolist())
        tuple_tail_ids = tuple(df_order_tail['period'].tolist())
        if len(tuple_ids) == 1:
            sql = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id = {tuple_ids[0]} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id = {tuple_ids[0]} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        else:
            sql = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id in {tuple_ids} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not = f"""SELECT id 'period' FROM `vh_periods_flash` WHERE id in {tuple_ids} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        if len(tuple_tail_ids) == 1:
            sql_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id = {tuple_tail_ids[0]} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id = {tuple_tail_ids[0]} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        else:
            sql_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id in {tuple_tail_ids} AND (product_category NOT LIKE '%白酒%' and product_category NOT LIKE '%酱香型%' and product_category NOT LIKE '%浓香型%' and product_category NOT LIKE '%清香型%' and product_category NOT LIKE '%米香型%' and product_category NOT LIKE '%凤香型%' and product_category NOT LIKE '%其他香型%')"""
            sql_not_tail = f"""SELECT id 'period' FROM `vh_periods_leftover` WHERE id in {tuple_tail_ids} AND (product_category LIKE '%白酒%' or product_category LIKE '%酱香型%' or product_category LIKE '%浓香型%' or product_category LIKE '%清香型%' or product_category LIKE '%米香型%' or product_category LIKE '%凤香型%' or product_category LIKE '%其他香型%')"""
        df_commodities = pd.read_sql(sql, conn_commodities)
        df_commodities_not = pd.read_sql(sql_not, conn_commodities)
        df_commodities_tail = pd.read_sql(sql_tail, conn_commodities)
        df_commodities_not_tail = pd.read_sql(sql_not_tail, conn_commodities)
        if df_commodities_tail.empty:
            tail_amount = 0
            tail_nums = 0
        else:
            re_periods = tuple(df_commodities_tail['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                tail_amount = 0
                tail_nums = 0
            else:
                tail_amount = float(flash_sales_info[0])
                tail_nums = flash_sales_info[1]
        if df_commodities_not_tail.empty:
            baijiu_tail_amount_not = 0
            baijiu_tail_nums_not = 0
        else:
            re_periods = tuple(df_commodities_not_tail['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_tail_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                baijiu_tail_amount_not = 0
                baijiu_tail_nums_not = 0
            else:
                baijiu_tail_amount_not = float(flash_sales_info[0])
                baijiu_tail_nums_not = flash_sales_info[1]
        if df_commodities.empty:
            shangou_amount = 0
            shangou_nums = 0
        else:
            re_periods = tuple(df_commodities['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                shangou_amount = 0
                shangou_nums = 0
            else:
                shangou_amount = float(flash_sales_info[0])
                shangou_nums = flash_sales_info[1]
        if df_commodities_not.empty:
            sg_baijiu_amount = 0
            sg_baijiu_nums = 0
        else:
            re_periods = tuple(df_commodities_not['period'].tolist())
            if len(re_periods) == 1:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period = {re_periods[0]}"""
            else:
                sql = f"""SELECT IFNULL(SUM(payment_amount), 0) 'shangou_amount',IFNULL(COUNT(1), 0) 'shangou_nums' FROM `vh_flash_order` WHERE {where_timerange} AND sub_order_status IN (1,2,3) AND period IN {re_periods}"""
            conn_orders_cursor.execute(sql)
            flash_sales_info = conn_orders_cursor.fetchone()
            if flash_sales_info is None:
                sg_baijiu_amount = 0
                sg_baijiu_nums = 0
            else:
                sg_baijiu_amount = float(flash_sales_info[0])
                sg_baijiu_nums = flash_sales_info[1]

    # 逾期订单
    order_table = {0: "vh_flash_order", 1: "vh_second_order",2: "vh_cross_order", 3: "vh_tail_order"}
    overdue_nums = {i: 0 for i in range(4)}
    now_timestamp = get_now_time("timestamp")
    for i, order_table_name in order_table.items():
        if i == 2:
            continue
        sql_orders = f"""SELECT count(1) as c FROM {order_table_name} WHERE sub_order_status = 1 and is_delete = 0 and is_ts = 0 and predict_time < {now_timestamp}"""
        conn_orders_cursor.execute(sql_orders)
        overdue = conn_orders_cursor.fetchone()
        overdue_nums[i] = int(overdue[0])

    shangou_amount = shangou_amount+tail_amount
    shangou_nums = shangou_nums+tail_nums
    sg_baijiu_amount = sg_baijiu_amount + baijiu_tail_amount_not
    sg_baijiu_nums = sg_baijiu_nums + baijiu_tail_nums_not
    if date[0:4] =="2022":
    # 闪购销售达标
        sg_rate = 0
        if shangou_amount > 0 and sg_plan_saleamount > 0:
            sg_rate = (shangou_amount / int(sg_plan_saleamount)) * 100
    # 闪购单量达标
        sg_num_rate = 0
        if shangou_nums > 0 and plan_amount > 0:
            sg_num_rate = (shangou_nums / int(plan_amount)) * 100
    elif date[0:4] !="2022":
    # 闪购白酒销售达标
        sg_rate = 0
        if shangou_amount > 0 and sg_bj_plan_saleamount > 0:
            sg_rate = (shangou_amount / sg_bj_plan_saleamount) * 100
    # 闪购非白酒销售达标
        sg_rate = 0
        if shangou_amount > 0 and sg_fbj_plan_saleamount > 0:
            sg_rate = (shangou_amount / sg_fbj_plan_saleamount) * 100
    # 闪购单量达标
        sg_num_rate = 0
        if shangou_nums > 0 and plan_amount > 0:
            sg_num_rate = (shangou_nums / plan_amount) * 100
    else:
        pass
    # 秒发达标
    mf_rate = 0
    if miaofa > 0 and mf_plan_saleamount > 0:
        mf_rate = (miaofa / mf_plan_saleamount) * 100
    # 跨境达标
    kj_rate = 0
    if kuajing > 0 and kj_plan_saleamount > 0:
        kj_rate = (kuajing / kj_plan_saleamount) * 100
    if date[0:4] =="2022": 
        res_json = {
            "end_date": end_date,
            "last_date": last_date,
            "date": date,
            "next_date": next_date,
            "week": week,
            "curtime": curtime,
            "last_operation": last_operation,
            "last_operation_assist": last_operation_assist,
            "operation": operation,
            "operation_assist": operation_assist,
            "next_operation": next_operation,
            "next_operation_assist": next_operation_assist,
            "sg_plan_saleamount": sg_plan_saleamount / 10000,
            "plan_amount": plan_amount,
            "kj_plan_saleamount": kj_plan_saleamount / 10000,
            "mf_plan_saleamount": mf_plan_saleamount / 10000,
            "memo": memo,
            "shangou_amount_total": '%.2f' % (shangou_amount_total / 10000),
            "shangou_nums_total": shangou_nums_total,
            "miaofa": '%.2f' % (miaofa / 10000),
            "kuajing": '%.2f' % (kuajing / 10000),
            "shangou_amount": '%.2f' % (shangou_amount / 10000),
            "shangou_nums": shangou_nums,
            "sg_baijiu_amount": '%.2f' % (sg_baijiu_amount / 10000),
            "sg_baijiu_nums": sg_baijiu_nums,
            "sg_rate": str('%.2f' % sg_rate) + '%',
            "sg_num_rate": str('%.2f' % sg_num_rate) + '%',
            "mf_rate": str('%.2f' % mf_rate) + '%',
            "kj_rate": str('%.2f' % kj_rate) + '%',
            "miaofa_sales_count": miaofa_sales_count,
            "kuajing_sales_count": kuajing_sales_count,
            "overdue_nums": overdue_nums,
        }
    elif date[0:4] !="2022":
        res_json = {
            "end_date": end_date,
            "last_date": last_date,
            "date": date,
            "next_date": next_date,
            "week": week,
            "curtime": curtime,
            "last_operation": last_operation,
            "last_operation_assist": last_operation_assist,
            "operation": operation,
            "operation_assist": operation_assist,
            "next_operation": next_operation,
            "next_operation_assist": next_operation_assist,
            "sg_bj_plan_saleamount": sg_bj_plan_saleamount / 10000,
            "sg_fbj_plan_saleamount": sg_fbj_plan_saleamount / 10000,
            "plan_amount": plan_amount,
            "kj_plan_saleamount": kj_plan_saleamount / 10000,
            "mf_plan_saleamount": mf_plan_saleamount / 10000,
            "memo": memo,
            "shangou_amount_total": '%.2f' % (shangou_amount_total / 10000),
            "shangou_nums_total": shangou_nums_total,
            "miaofa": '%.2f' % (miaofa / 10000),
            "kuajing": '%.2f' % (kuajing / 10000),
            "shangou_amount": '%.2f' % (shangou_amount / 10000),
            "shangou_nums": shangou_nums,
            "sg_baijiu_amount": '%.2f' % (sg_baijiu_amount / 10000),
            "sg_baijiu_nums": sg_baijiu_nums,
            "sg_rate": str('%.2f' % sg_rate) + '%',
            "sg_num_rate": str('%.2f' % sg_num_rate) + '%',
            "mf_rate": str('%.2f' % mf_rate) + '%',
            "kj_rate": str('%.2f' % kj_rate) + '%',
            "miaofa_sales_count": miaofa_sales_count,
            "kuajing_sales_count": kuajing_sales_count,
            "overdue_nums": overdue_nums,
        }
    else:
        pass
    return res_json


def assemble_data(res_json, now_time):
    """
    组装数据
    :param res_json:
    :parser now_time
    :return:
    """
    now_time_hour = now_time.split(' ')[1].split(':')[0]
    end_date = res_json['end_date']
    # last_date = res_json['last_date']
    date = res_json['date']
    # next_date = res_json['next_date']
    week = res_json['week']
    curtime = res_json['curtime']
    last_operation = res_json['last_operation']
    last_operation_assist = res_json['last_operation_assist']
    operation = res_json['operation']
    operation_assist = res_json['operation_assist']
    next_operation = res_json['next_operation']
    next_operation_assist = res_json['next_operation_assist']
    if date[0:4] =="2022":
        sg_plan_saleamount = res_json['sg_plan_saleamount']
    elif date[0:4] !="2022":
        sg_bj_plan_saleamount = res_json['sg_bj_plan_saleamount']
        sg_fbj_plan_saleamount = res_json['sg_fbj_plan_saleamount']
    else:
        pass
    plan_amount = res_json['plan_amount']
    kj_plan_saleamount = res_json['kj_plan_saleamount']
    mf_plan_saleamount = res_json['mf_plan_saleamount']
    memo = res_json['memo']
    shangou_amount_total = res_json['shangou_amount_total']
    shangou_nums_total = res_json['shangou_nums_total']
    miaofa = res_json['miaofa']
    kuajing = res_json['kuajing']
    shangou_amount = res_json['shangou_amount']
    shangou_nums = res_json['shangou_nums']  
    sg_baijiu_amount = res_json['sg_baijiu_amount']       
    sg_baijiu_nums = res_json['sg_baijiu_nums']
    sg_rate = res_json['sg_rate']
    sg_num_rate = res_json['sg_num_rate']
    mf_rate = res_json['mf_rate']
    kj_rate = res_json['kj_rate']
    miaofa_sales_count = res_json['miaofa_sales_count']
    kuajing_sales_count = res_json['kuajing_sales_count']
    main_count=shangou_nums+sg_baijiu_nums+kuajing_sales_count+miaofa_sales_count
    main_amount="{:.2f}".format(float(shangou_amount)+float(sg_baijiu_amount)+float(kuajing)+float(miaofa))
    main_amount = main_amount.strip()
    count_rate = 0
    if main_count > 0 and plan_amount > 0:
        count_rate=(main_count/plan_amount) * 100
    count_rate_str=str('%.2f' % count_rate) + '%'
    overdue_nums = res_json['overdue_nums']
    total_overdue_nums = sum(overdue_nums.values())

    if date[0:4] =="2022":
        if memo == "" or memo is None:
             memo = "无"
        if now_time_hour != '00':
            content_template = f"### 运营数据V3 \n\n > 截止{end_date} 周{week} {curtime} \n\n **今日值班:** {operation}+{operation_assist} \n\n **明日值班:** {next_operation}+{next_operation_assist} \n\n **闪购非白酒:** \n\n > - **销售:** {shangou_amount}(计划:{sg_plan_saleamount}万), 达标率: {sg_rate} \n\n > - **单量:** {shangou_nums}（计划: {plan_amount}单), 达标率: {sg_num_rate} \n\n **闪购白酒:** \n\n > - **销售:** {sg_baijiu_amount}万 / {sg_baijiu_nums}单 \n\n **闪购总销售额:** {shangou_amount_total}万 / {shangou_nums_total}单 \n\n **秒发销售:** {miaofa}（计划:{mf_plan_saleamount}万), 达标率: {mf_rate}, 单量:{miaofa_sales_count} \n\n **跨境销售:** {kuajing}（计划:{kj_plan_saleamount}万), 达标率: {kj_rate}, 单量:{kuajing_sales_count}\n\n  **备注:** {memo}"
        else:
            content_template = f"### 运营数据V3 \n\n > 截止{end_date} 周{week} {curtime} \n\n **昨日值班:** {operation}+{operation_assist} \n\n **今日值班:** {next_operation}+{next_operation_assist} \n\n **闪购非白酒:** \n\n > - **销售:** {shangou_amount}(计划:{sg_plan_saleamount}万), 达标率: {sg_rate} \n\n > - **单量:** {shangou_nums}（计划: {plan_amount}单), 达标率: {sg_num_rate} \n\n **闪购白酒:** \n\n > - **销售:** {sg_baijiu_amount}万 / {sg_baijiu_nums}单 \n\n **闪购总销售额:** {shangou_amount_total}万 / {shangou_nums_total}单 \n\n **秒发销售:** {miaofa}（计划:{mf_plan_saleamount}万), 达标率: {mf_rate}, 单量:{miaofa_sales_count} \n\n **跨境销售:** {kuajing}（计划:{kj_plan_saleamount}万), 达标率: {kj_rate}, 单量:{kuajing_sales_count}\n\n  **备注:** {memo}"
    else:
        if memo == "" or memo is None:
             memo = "无"
        if now_time_hour != '00':
            content_template = f"### 运营数据V3 \n\n 截止{end_date} 周{week} {curtime} \n\n **今日值班:** {operation}+{operation_assist} \n\n **明日值班:** {next_operation}+{next_operation_assist} \n\n **备注:** {memo} \n\n **闪购:** {shangou_amount}万（计划:{sg_fbj_plan_saleamount}万）, 销售额达标率: {sg_rate} / {shangou_nums}单 \n\n **白酒:** {sg_baijiu_amount}万（计划:{sg_bj_plan_saleamount}万）/ {sg_baijiu_nums}单 \n\n **秒发:** {miaofa}万（计划:{mf_plan_saleamount}万）, 销售额达标率: {mf_rate} / {miaofa_sales_count}单 \n\n **跨境:** {kuajing}万（计划:{kj_plan_saleamount}万）, 销售额达标率: {kj_rate} / {kuajing_sales_count}单 \n\n **合计:** {main_amount} 万，{main_count} 单（计划 {plan_amount} 单，达标率: {count_rate_str}）  \n\n **逾期:** {total_overdue_nums}（闪购:{overdue_nums[0]}，秒发:{overdue_nums[1]}，尾货:{overdue_nums[3]}）"

            #**总订单数:** {main_count}（计划:{plan_amount}单）, 单量达标率: {count_rate_str}

        else:
            content_template = f"### 运营数据V3 \n\n 截止{end_date} 周{week} {curtime} \n\n **昨日值班:** {operation}+{operation_assist} \n\n **今日值班:** {next_operation}+{next_operation_assist}\n\n  **备注:** {memo} \n\n **闪购:** {shangou_amount}万（计划:{sg_fbj_plan_saleamount}万）, 销售额达标率: {sg_rate} / {shangou_nums}单 \n\n **白酒:** {sg_baijiu_amount}万（计划:{sg_bj_plan_saleamount}万）/ {sg_baijiu_nums}单 \n\n **秒发:** {miaofa}万（计划:{mf_plan_saleamount}万）, 销售额达标率: {mf_rate} / {miaofa_sales_count}单 \n\n **跨境:** {kuajing}万（计划:{kj_plan_saleamount}万）, 销售额达标率: {kj_rate} / {kuajing_sales_count}单 \n\n **合计:** {main_amount} 万，{main_count} 单（计划 {plan_amount} 单，达标率: {count_rate_str}） \n\n **逾期:** {total_overdue_nums}（闪购:{overdue_nums[0]}，秒发:{overdue_nums[1]}，尾货:{overdue_nums[3]}）"     
    return content_template, curtime


'''
    使用推送队列的方式发送消息到钉钉机器人
    @author:vber
    @date:2022-09-07
'''
def push_info_robot_by_queue_service(text, curtime):
    headers = {
        'Content-Type': 'application/json',
        'vinehoo-client': 'py3-v3-data-analysis'
    }
    url =  "http://go-queueservice/services/v3/queue/push"#"http://test-wine.wineyun.com/queueservice/services/v3/queue/push"
    data = json.dumps({
            "title": f"运营数据 - {curtime}",
            "text":  text
        }
    ,ensure_ascii=False)

    print("dingtalk accesstoken:",DingtalkAccessToken.robot_access_token)
    dingtalk_data = base64.b64encode(json.dumps({
            "access_token": DingtalkAccessToken.robot_access_token,
            "type":"markdown",
            "at":"",
            "content": base64.b64encode(data.encode('utf-8')).decode('utf-8')
        },ensure_ascii=False).encode("utf-8"))
    
    print(dingtalk_data)
    queue_data = json.dumps({
        "exchange_name":"dingtalk",
        "routing_key":"dingtalk_sender",
        "data":str(dingtalk_data, encoding = "utf-8")
    }, ensure_ascii=False)
    
    print(queue_data)
    res = requests.post(url, headers=headers, data=queue_data)
    plot_hourly_sales()
    return res.json()
    
def push_info_robot(text, curtime):
    """
    推送消息给钉钉机器人
    :return: f
    """
    
    return push_info_robot_by_queue_service(text, curtime)
    
    # headers = {
    #     'Content-Type': 'application/json'
    # }
    # url = f"https://oapi.dingtalk.com/robot/send?access_token={DingtalkAccessToken.robot_access_token}"
    # data = json.dumps({
    #     "msgtype": "markdown",
    #     "markdown": {
    #         "title": f"运营数据 - {curtime}",
    #         "text": text
    #     }
    # })
    # res = requests.post(url, headers=headers, data=data)
    # return res.json()



