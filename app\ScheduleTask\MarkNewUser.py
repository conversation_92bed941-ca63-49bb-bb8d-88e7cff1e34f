import pandas as pd
import warnings
import pymysql
import json
from app.CommonLibraries.getConfig import get_config
from pymysql import IntegrityError

warnings.filterwarnings("ignore")


config_write_info = get_config("db.data.write.v3_all", "vinehoo.accounts")
mysql_write_host = config_write_info['host']
mysql_write_port = config_write_info['port']
mysql_write_user = config_write_info['user']
mysql_write_password = config_write_info['password']


def conn_mysql(host, port, user, password, database):
    """
    链接数据库
    :param host:
    :param port:
    :param user:
    :param password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")


def validation_tup(eventmark, cursor):
    """
    校验函数
    :param date: 日期
    :param sell_type
    :return:
    """
    validation_sql = f""" SELECT * FROM `vh_nowuser_mark` where `eventmark`='{eventmark}'"""
    cursor.execute(validation_sql)
    data = cursor.fetchone()
    return data
    
def get_newuser_mark(keyword,now_eventmark,is_open,page,page_nums,query_type):
    
    """
    标识首页(打开或关闭)
    
    :param keyword: 搜索关键词
    :param make_eventmark: 需要编辑的事件标识
    :param is_open: 开启状态
    :return:
    """
    
    wrconn_statistics = conn_mysql(mysql_write_host, mysql_write_port, mysql_write_user, mysql_write_password, "vh_data_statistics")
    wrconn_statistics_cursor = wrconn_statistics.cursor()
    where = ''
    if keyword != '':
        where = f""" and `platformmark` like '%{keyword}%' or `eventmark` like '%{keyword}%' or `source_type` like '%{keyword}%' """
    #query_type：0-外部销售，1-专题销售
    if query_type == '0':
        where = f"""{where} and `activity_id` = 0 """
    elif query_type == '1':
        where = f"""{where} and `activity_id` > 0 """
    
    nowuser_mark_sql = f""" select `platformmark`,`eventmark`,`source_type`,`is_open` from `vh_nowuser_mark` where 1 {where} """

    if is_open == "1":  ######是否开启标识 (0-关闭,1-开启)
        take_mark_sql = f"""UPDATE `vh_nowuser_mark` SET `is_open` = 1 WHERE `eventmark`='{now_eventmark}'"""
    elif is_open == "0":
        take_mark_sql = f"""UPDATE `vh_nowuser_mark` SET `is_open` = 0 WHERE `eventmark`='{now_eventmark}'"""
    else:
        take_mark_sql = ""
        pass
    try:
        if take_mark_sql != "":
            wrconn_statistics_cursor.execute(take_mark_sql)
            wrconn_statistics.commit()
        else:
            pass
    except Exception as e:
        print(f"编辑开启状态失败,失败信息{e}")
    finally:
        nowuser_mark_df = pd.read_sql_query(nowuser_mark_sql,wrconn_statistics)
        if nowuser_mark_df.empty:
            return {"1":{},"0":{},"total":0}
        if page == "":
            page = "1"
        if page_nums == "":
            page_nums = "10"
        else:
            pass
        total = len(nowuser_mark_df)
        nowuser_mark_df= nowuser_mark_df[(int(page) * int(page_nums) - int(page_nums)):int(page) * int(page_nums)].reset_index(drop=True)
        if nowuser_mark_df.empty:
            nowuser_mark_json = {}
        else:
            nowuser_mark_json = json.loads(nowuser_mark_df.to_json(orient="records"))
        nowuser_mark_json = {
            "nowuser_markjson":nowuser_mark_json,
            "total":total
        }
        wrconn_statistics.close()
    return nowuser_mark_json


def take_newuser_mark(args):
    
    """
    编辑活动标识入口(添加或更改)
    
    :param is_add: 更改状态是否为新增(1-新增,0-编辑)
    :param now_platformmark : 当前选中平台标识
    :param now_eventmark: 当前选中标识事件标识
    :param now_source_type: 当前选中渠道标识
    :param platformmark: 需要添加或更改的平台标识
    :param eventmark: 需要添加或更改的事件标识
    :param source_type: 需要添加或更改的投放渠道    
    :return:
    """
    is_add = args["is_add"]
    if is_add == "0":
        now_platformmark = args["now_platformmark"]
        now_eventmark = args["now_eventmark"]
        now_source_type = args["now_source_type"]
        if args["platformmark"] == "":
            platformmark = now_platformmark
        else:
            platformmark = args["platformmark"]
        if args["eventmark"] == "":
            eventmark = now_eventmark
        else:
            eventmark = args["eventmark"]
        if args["source_type"] == "":
            source_type = now_source_type
        else:
            source_type = args["source_type"]
    elif is_add == "1":
        platformmark = args["platformmark"]
        eventmark = args["eventmark"]
        source_type = args["source_type"]
        if platformmark == "" or eventmark == "" or source_type == "":
            return {"error_code": -1, "error_msg":"请完整录入标识信息！", "status": "fail"}
        else:
            pass
    wrconn_statistics = conn_mysql(mysql_write_host, mysql_write_port, mysql_write_user, mysql_write_password, "vh_data_statistics")
    wrconn_statistics_cursor = wrconn_statistics.cursor()
    if is_add == "1":  ######添加
        take_mark_sql = f"""insert into `vh_nowuser_mark` (`platformmark`,`eventmark`,`source_type`,`is_open`) values ('{platformmark}','{eventmark}','{source_type}',1)"""
    elif is_add =="0":  ######编辑
        take_mark_sql = f"""UPDATE `vh_nowuser_mark` SET `platformmark`='{platformmark}',`eventmark`='{eventmark}',`source_type`='{source_type}' WHERE  `eventmark`= '{now_eventmark}'"""
        if args["eventmark"] != "" and validation_tup(eventmark, wrconn_statistics_cursor) != None:
            is_open = validation_tup(eventmark, wrconn_statistics_cursor)[3]
            delete_sql = f"""delete from `vh_nowuser_mark` where `eventmark` = '{eventmark}'"""
            wrconn_statistics_cursor.execute(delete_sql)
            take_mark_sql = f"""insert into `vh_nowuser_mark` (`platformmark`,`eventmark`,`source_type`,`is_open`) values ('{platformmark}','{eventmark}','{source_type}',{is_open})"""
        else:
            pass
    else:
        pass
    try:
        wrconn_statistics_cursor.execute(take_mark_sql)
        wrconn_statistics.commit()
    except IntegrityError as e:
        return {"error_code": -1, "error_msg": f"标识已存在,请勿重复添加", "status": "fail"}
    finally:
        wrconn_statistics.close()