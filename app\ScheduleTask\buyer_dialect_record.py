import pymysql
import json
import warnings
import concurrent
import pandas as pd
from datetime import datetime,timedelta
from config import ChannelTypeConfig
from sqlalchemy import create_engine
from app.DButils.MysqlHelper import conn_str


warnings.filterwarnings("ignore")

def nopool_conn(database:str) -> object:
    """
    获取指定数据库链接
    :param database:数据库名
    :return:
    """
    conn_engine = create_engine(conn_str(database,"channel"))
    return conn_engine

def mysql_conn(host, port, user, password, database):
    """
    链接主数据库
    :param rm_host:
    :param rm_port:
    :param rm_user:
    :param rm_password:
    :param database:
    :return:
    """
    try:
        conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
        return conn
    except ConnectionError as ce:
        print(f"连接数据库失败,失败信息:{ce}")
        raise ConnectionError("连接失败")
        
def get_last_day():
    last_day = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    return last_day

def get_buyer_massage(period):
    commodities_conn = nopool_conn("vh_commodities").connect()
    buyer_name_sql=f"select buyer_id,buyer_name from {period} where buyer_name != '' and buyer_name is not null group by `buyer_name`"
    df_buyer_name= pd.read_sql_query(buyer_name_sql,commodities_conn)
    commodities_conn.close()
    nopool_conn("vh_commodities").dispose()
    return df_buyer_name
        
def get_order_massage(table,period,package,start,end):
    orders_conn = nopool_conn("vh_orders").connect()
    sql = f"""select a.main_order_id,from_unixtime(a.payment_time,"%%Y-%%m-%%d") as 'date',from_unixtime(a.payment_time,"%%H") as 'hour',a.period,aa.title,aa.price,'{table}' as 'channel_type',aa.buyer_name,aa.buyer_id,a.sub_order_no,a.payment_amount - a.refund_money as 'payment_amount',a.uid,b.associated_products,aa.import_type from {table} AS a left join vh_commodities.{period} AS aa on a.period = aa.id left join vh_commodities.{package} AS b on a.package_id = b.id left join vh_user.vh_user AS bb on a.uid = bb.uid where a.payment_time between {start} and {end} and a.sub_order_status in (1,2,3) and aa.title != "" """
    dff = pd.read_sql_query(sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return dff

def get_eventmark(main_order_ids):
    orders_conn = nopool_conn("vh_orders").connect()
    sql = f""" select `main_order_id`,`source_event` from vh_order_source_log where main_order_id in {main_order_ids}"""
    dff = pd.read_sql_query(sql,orders_conn)
    orders_conn.close()
    nopool_conn("vh_orders").dispose()
    return dff


def get_sale_massage(date):
    start,end = int(datetime.strptime(f"""{date} 00:00:00""","%Y-%m-%d %H:%M:%S").timestamp()),int(datetime.strptime(f"""{date} 23:59:59""","%Y-%m-%d %H:%M:%S").timestamp())
    Cg = ChannelTypeConfig.channel_type_config
    tables,periods,packages = [Cg[1][0],Cg[2][0],Cg[3][0],Cg[4][0]],[Cg[1][1],Cg[2][1],Cg[3][1],Cg[4][1]],[Cg[1][2],Cg[2][2],Cg[3][2],Cg[4][2]]
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        tasks = [executor.submit(get_order_massage,table,period,package,start,end) for table,period,package in zip(tables,periods,packages)]
        Sale_Data = pd.DataFrame(columns=["main_order_id","date","hour","period","title","price","channel_type","buyer_name","buyer_id","sub_order_no","payment_amount","uid","associated_products","import_type"])
        for future in concurrent.futures.as_completed(tasks):
            Sale_Data=Sale_Data.append(future.result()).reset_index(drop=True)
    main_order_no_sql = f""" select id as 'main_order_id',main_order_no from vh_order_main where id in {tuple(set(Sale_Data.main_order_id.tolist()))} """
    order_conn = nopool_conn("vh_orders").connect()
    main_order_no_dff = pd.read_sql_query(main_order_no_sql,order_conn)
    Sale_Data = pd.merge(Sale_Data,main_order_no_dff,how="left",on="main_order_id")
    mh_sql = f"""  select main_order_no,period,product_info from vh_order_mystery_box_log where main_order_no in {tuple(set(Sale_Data.main_order_no.tolist()))} """
    mh_dff = pd.read_sql_query(mh_sql,order_conn)
    Sale_Data = pd.merge(Sale_Data,mh_dff,how="left",on=["main_order_no","period"])
    order_conn.close()
    nopool_conn("vh_orders").dispose()
    for index, row in Sale_Data.iterrows():
        if pd.notna(row['product_info']):
            Sale_Data.at[index, 'associated_products'] = row['product_info']
    have_massage = Sale_Data[Sale_Data.associated_products.apply(lambda x:x != '[]' and x != None and type(x) != float)]
    no_massage = Sale_Data[Sale_Data.associated_products.apply(lambda x:x == '[]' or x == None or type(x) == float)]
    transfer_df = have_massage.copy()
    transfer_df["associated_products"]=transfer_df.associated_products.apply(lambda x:eval(x))
    is_any = []
    for is_a in transfer_df.associated_products:
        if len(is_a) == 1:
            a = 0
        elif len(is_a) > 1:
            a = 1
        is_any.append(a)
    transfer_df["is_any"] = is_any
    main_data_any = transfer_df[transfer_df["is_any"] == 1]
    main_data_one = transfer_df[transfer_df["is_any"] == 0]
    if main_data_any.empty:
        dff_main_data = main_data_one
        dff_main_data["product_id"] = dff_main_data.associated_products.apply(lambda x:x[0]["product_id"])
        dff_main_data["nums"] = dff_main_data.associated_products.apply(lambda x:x[0]["nums"])        
    else:
        main_data_one["product_id"] = main_data_one.associated_products.apply(lambda x:x[0]["product_id"])
        main_data_one["nums"] = main_data_one.associated_products.apply(lambda x:x[0]["nums"])    
        main_data_any = main_data_any.explode('associated_products').reset_index(drop=True)
        main_data_any["mark"] = main_data_any.associated_products.apply(lambda x:json.dumps(x))
        main_data_any = main_data_any.drop_duplicates(subset=["sub_order_no","mark"],keep="first")
        main_data_any["product_id"] = main_data_any.associated_products.apply(lambda x:x["product_id"])
        main_data_any["nums"] = main_data_any.associated_products.apply(lambda x:x["nums"])
        main_data_any.loc[main_data_any.duplicated(subset='sub_order_no', keep='first'), 'payment_amount'] = 0
        main_data_any.loc[main_data_any.duplicated(subset='sub_order_no', keep='first'), 'sub_order_no'] = None
        dff_main_data = main_data_one.append(main_data_any).reset_index(drop=True)
    mh_sql = f"""  select period from vh_order_mystery_box_log group by `period` """
    order_conn = nopool_conn("vh_orders").connect()
    mh_dff = pd.read_sql_query(mh_sql,order_conn)
    order_conn.close()
    nopool_conn("vh_orders").dispose()
    mh_periods = tuple(set(mh_dff.period.tolist()))
    dff_main_data = dff_main_data[dff_main_data.period.apply(lambda x:x not in mh_periods)]
    product_id = tuple(dff_main_data.product_id.tolist())
    in_sql = f"select a.id as 'product_id',a.product_type,aa.fid from vh_products as a left join vh_product_type as aa on a.product_type = aa.id where a.id in {product_id}"
    wiki_conn = nopool_conn("vh_wiki").connect()
    in_df = pd.read_sql_query(in_sql, wiki_conn)
    wiki_conn.close()
    nopool_conn("vh_wiki").dispose()
    Sale_df = pd.merge(dff_main_data, in_df, how="left", on="product_id")
    Sale_df.drop(["is_any","product_id"], axis=1, inplace=True)
    no_massage["nums"] = ''
    no_massage["product_type"] = ''
    no_massage["fid"] = ''
    Sale_df = Sale_df.append(no_massage).reset_index(drop=True)
    main_order_ids = tuple(set(Sale_df.main_order_id.tolist()))
    eventmark_df = get_eventmark(main_order_ids)
    Sale_df = pd.merge(Sale_df,eventmark_df,how="left",on="main_order_id")
    Sale_df["source_event"] = Sale_df.source_event.fillna("internal")
    Sale_dff = Sale_df.groupby(by=["date","hour","channel_type","source_event","period"],as_index=False).agg({"title":"first","price":"first","buyer_name":"first","buyer_id":"first","sub_order_no":"count","payment_amount":sum,"product_type":set,"import_type":"first","fid":set})
    Sale_dff["product_type"] = Sale_dff.product_type.apply(lambda x:list(x))
    Sale_dff["fid"] = Sale_dff.fid.apply(lambda x:list(x))
    Sale_df_count = Sale_df[["date","hour","channel_type","source_event","period","uid"]].groupby(by=["date","hour","channel_type","source_event","period"],as_index=False)["uid"].nunique().reset_index(drop=True)
    Sale_dff = pd.merge(Sale_dff,Sale_df_count,how="left",on=["date","hour","channel_type","source_event","period"])
    Sale_dff = Sale_dff.rename(columns={"sub_order_no":"order_counts","uid":"user_counts"})
    Sale_dff = Sale_dff[["date","hour","source_event","period","title","price","channel_type","buyer_name","buyer_id","order_counts","payment_amount","product_type","user_counts","import_type","fid"]]
    channel_dict = {"vh_flash_order":0,"vh_cross_order":1,"vh_second_order":2,"vh_tail_order":3}
    Sale_dff["channel_type"] = Sale_dff.channel_type.apply(lambda x:channel_dict[x])
    Sale_dff["title"] = Sale_dff.title.apply(lambda x:x.replace("'", "''"))
    Sale_dff["product_type"] = Sale_dff.product_type.apply(lambda x:",".join([str(each) for each in x]))
    Sale_dff["fid"] = Sale_dff.fid.apply(lambda x:",".join([str(each) for each in x]))
    return Sale_dff

def get_buyers():
    Cg = ChannelTypeConfig.channel_type_config
    periods = [Cg[1][1],Cg[2][1],Cg[3][1],Cg[4][1]]
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        tasks = [executor.submit(get_buyer_massage,period) for period in periods]
        Buyers_Data = pd.DataFrame(columns=["buyer_id","buyer_name"])
        for future in concurrent.futures.as_completed(tasks):
            Buyers_Data=Buyers_Data.append(future.result()).reset_index(drop=True)
    Buyers_Data = Buyers_Data.drop_duplicates(subset="buyer_name",keep="first")
    return Buyers_Data

def exits_data(args,cursor,operate_type):
    """
    验证是否已存在
    :param date: 日期
    :param is_liquor:
    :param sell_type
    :return:
    """
    if operate_type == 1:
        sql = f"""select source_event from `vh_sales_statistics_dialect` where `date` = '{args[0]}' and `hour` = '{args[1]}' and `period` = {args[3]} and `channel_type` = '{args[6]}' and `source_event` = '{args[2]}' and `product_type` = '{args[11]}' """
        cursor.execute(sql)
        data = cursor.fetchone()
        if data is not None:
            delete_sql = f"""delete from `vh_sales_statistics_dialect` where `date` = '{args[0]}' and `hour` = '{args[1]}' and `period` = {args[3]} and `channel_type` = '{args[6]}' and `source_event` = '{args[2]}' and `product_type` = '{args[11]}' """
            cursor.execute(delete_sql)
        else:
            pass
    elif operate_type == 2:
        sql = f"""select buyer_id from vh_buyer_record where `buyer_name` = '{args[0]}'"""
        cursor.execute(sql)
        data = cursor.fetchone()
        if data is not None:
            delete_sql = f"""delete from vh_buyer_record where `buyer_name` = '{args[0]}'"""
            cursor.execute(delete_sql)
        else:
            pass

def insert_mysql(*args):
    if args[-1] == 1:
        cursor=args[15]
        operate_type=args[16]
    else:
        cursor=args[2]
        operate_type=args[3]
    exits_data(args,cursor=cursor,operate_type=operate_type)
    if operate_type == 1:
        sql = f"""insert into `vh_sales_statistics_dialect` 
                 (`date`,`hour`,`source_event`,`period`,`title`,`price`,`channel_type`,`buyer_name`,`buyer_id`,`order_counts`,`payment_amount`,`product_type`,`user_counts`,`import_type`,`fid`) values ('{args[0]}','{args[1]}','{args[2]}',{args[3]},'{args[4]}',{args[5]},{args[6]},'{args[7]}',{args[8]},{args[9]},{args[10]},'{args[11]}',{args[12]},{args[13]},'{args[14]}')"""
    elif operate_type == 2:
        sql = f"""insert into `vh_buyer_record` 
                 (`buyer_id`,`buyer_name`) values ({args[0]},'{args[1]}')"""
    try:
        cursor.execute(sql)
    except Exception as e:
        print(f"写入数据失败,失败信息{e}")
        
def handler_statistics_dialect(rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = mysql_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        dff = get_sale_massage(get_last_day())
        data_list = dff.values.tolist()
        for data in data_list:
            insert_mysql(data[0],data[1],data[2],data[3],data[4],data[5],data[6],data[7],data[8],data[9],data[10],data[11],data[12],data[13],data[14],cursor,1)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()
        
def handler_buyer_record(rm_host, rm_port, rm_user, rm_password):
    """
    入口函数
    :return:
    """
    try:
        conn = mysql_conn(rm_host, rm_port, rm_user, rm_password, 'vh_data_statistics')
        cursor = conn.cursor()
        dff = get_buyers()
        data_list = dff.values.tolist()
        for data in data_list:
            insert_mysql(data[0],data[1],cursor,2)
        conn.commit()
        return 1
    except Exception as e:
        print(e)
        return -1
    finally:
        conn.close()

def handler_buyer_dialect_main(rm_host, rm_port, rm_user, rm_password):
    try:
        handler_buyer_record(rm_host, rm_port, rm_user, rm_password)
        handler_statistics_dialect(rm_host, rm_port, rm_user, rm_password)
        return 1
    except Exception as e:
        print(e)
        return -1
    