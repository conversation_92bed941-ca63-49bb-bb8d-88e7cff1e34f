import json

from flask_restful import Resource, request
from app.DButils.MysqlHelper import data_statistic_pool


class SecondVisitPvUvList(Resource):
    def get(self):
        try:
            # 获取查询参数
            start_date = request.args.get('start_date', '')  # 开始日期，例如：2024-12-01
            end_date = request.args.get('end_date', '')  # 结束日期，例如：2024-12-31
            page = int(request.args.get('page', 1))  # 当前页码
            limit = int(request.args.get('limit', 10))  # 每页显示记录数

            # 构建 WHERE 子句
            where_clause = ""
            if start_date and end_date:
                where_clause = f"WHERE `day` BETWEEN '{start_date}' AND '{end_date}'"
            elif start_date:
                where_clause = f"WHERE `day` >= '{start_date}'"
            elif end_date:
                where_clause = f"WHERE `day` <= '{end_date}'"

            # 计算偏移量，以支持分页
            offset = (page - 1) * limit

            conn_statics, conn_statics_cursor = data_statistic_pool.get_connection()
            # 获取总记录数
            total_sql = f'SELECT COUNT(*) AS total FROM vh_second_visit {where_clause}'
            # 执行 SQL 查询
            conn_statics_cursor.execute(total_sql)
            total_result = conn_statics_cursor.fetchall()
            total = total_result[0][0] if total_result else 0

            # 构建分页SQL查询
            sql = f'SELECT * FROM vh_second_visit {where_clause} ORDER BY id DESC LIMIT {limit} OFFSET {offset}'
            conn_statics_cursor.execute(sql)
            data = conn_statics_cursor.fetchall()

            # 处理每行的 pv_uv JSON 数据
            formatted_data = []
            for row in data:
                # 获取所有列，但转换第三列 JSON 数据
                id_value, day_value, pv_uv = row
                pv_uv = json.loads(pv_uv)  # 将 JSON 字符串解析为 Python 对象
                formatted_data.append({
                    "id": id_value,
                    "day": day_value,
                    "pv_uv": pv_uv
                })

            data_statistic_pool.dispose(conn_statics, conn_statics_cursor)

            # 构建返回结果
            result = {
                "error_code": 0,
                "error_msg": "",
                "data": {
                    "total": total,
                    "list": formatted_data,
                },
            }

            return result

        except Exception as e:
            print(e)
            return {"error_code": -1, "error_msg": "请求失败", "status": "failure", "data": [], "error_info": str(e)}
