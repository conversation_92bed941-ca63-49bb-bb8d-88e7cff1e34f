# 配置中心
class ConfigCenter:
    config_url = {
        # "config_url": "https://test-wine.wineyun.com/config-center"
        "config_url": "http://go-config-center"
    }
    config_oss_url = {
        "config_oss_url": "https://images.wineyun.com"
    }
    decrypt_url = {"decrypt_url": "https://callback.vinehoo.com/des-server/v1/decrypt"
    }


# 商品类型配置
class GoodsTypeConfig:
    goods_type_config = {
        1: '红葡萄酒',
        2: '干红葡萄酒',
        3: '半干红葡萄酒',
        4: '半甜葡萄酒',
        5: '甜红葡萄酒',
        6: '白葡萄酒',
        7: '干白葡萄酒',
        8: '半干白葡萄酒',
        9: '半甜白葡萄酒',
        10: '甜白葡萄酒',
        11: '桃红葡萄酒',
        12: '干桃红葡萄酒',
        13: '半干桃红葡萄酒',
        14: '半甜桃红葡萄酒',
        15: '桃红葡萄酒',
        16: '气泡酒',
        17: '香槟',
        18: '加强酒',
        19: '黄酒',
        20: '米酒',
        21: '白酒',
        22: '烈酒',
        23: '白兰地',
        24: '威士忌',
        25: '金酒',
        26: '伏特加',
        27: '朗姆酒',
        28: '龙舌兰',
        29: '利口酒',
        30: '啤酒',
        31: '果酒',
        32: '其他酒',
        33: '牛奶',
        34: '果汁',
        35: '矿泉水',
        36: '气泡水',
        37: '其他饮料',
        38: '生鲜',
        39: '预包装食品',
        40: '其他食品',
        41: '玻璃制品',
        42: '金属制品',
        43: '纸质制品',
        44: '布料制品',
        45: '塑料制品',
        46: '泡沫制品',
        47: '保温制品',
        48: '木制品',
        49: '酒会',
        50: '课程',
        51: '直播',
        52: '线下门店',
        53: '书籍',
        54: '厨具',
        55: '酒杯',
        56: '酒塞',
        57: '开瓶器',
        58: '酒袋',
        59: '其他酒具',
        60: '日用品',
        61: '清酒',
        62: '日本酒',
        63: '配制酒'
    }


# 排序字段配置
class SortKeyConfig:
    sort_key_config = {
        0: "period",
        1: "views",
        2: "views",
        3: "order_users",
        4: "order_users",
        5: "conversion_rate",
        6: "conversion_rate"
    }


# 频道对应表配置
class ChannelTypeConfig:
    channel_type_config = {
        1: ["vh_flash_order", "vh_periods_flash", "vh_periods_flash_set"],
        2: ["vh_cross_order", "vh_periods_cross", "vh_periods_cross_set"],
        3: ["vh_second_order", "vh_periods_second", "vh_periods_second_set"],
        4: ["vh_tail_order", "vh_periods_leftover", "vh_periods_leftover_set"]
    }


class DingtalkAccessToken:
    robot_access_token = "a5df4c1d-0f20-4f0c-8410-c91dca6d7695"#"f57bc9e343d4268b16a1ebfd66f655caba89a6b5341ef70870ce2da5392b9d41"
    auction_access_token = "48c2aca4-2e0c-48df-961d-c5925440d05a"#"209b9e91-b041-49d9-8bd9-83b0b455e224"
    robot_image_token = "a9450712-b89f-4578-81e6-d7f37c256fa9"

